{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/components/common/Slider.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react'\r\nimport Slider from \"react-slick\"\r\nimport \"slick-carousel/slick/slick.css\";\r\nimport \"slick-carousel/slick/slick-theme.css\";\r\n\r\nfunction CustomSlider(props) {\r\n    const { ref, children, sliderSettings, className } = props;\r\n\r\n    return (\r\n        <Slider ref={ref} {...sliderSettings} className={className}>\r\n            {children}\r\n        </Slider>\r\n    )\r\n}\r\n\r\nexport default CustomSlider\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;;;AAOA,SAAS,aAAa,KAAK;IACvB,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG;IAErD,qBACI,8OAAC,8IAAA,CAAA,UAAM;QAAC,KAAK;QAAM,GAAG,cAAc;QAAE,WAAW;kBAC5C;;;;;;AAGb;uCAEe", "debugId": null}}]}