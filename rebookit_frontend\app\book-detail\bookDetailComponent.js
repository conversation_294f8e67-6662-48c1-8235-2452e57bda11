"use client";
"use strict";
import dynamic from "next/dynamic";
import Image from "next/image";
import React, { useRef, useState } from "react";
import "./bookDetailComponent.scss";
import { HiStar } from "react-icons/hi";
import { IoMdStar } from "react-icons/io";
import { GoBookmarkFill } from "react-icons/go";
import { useEffect } from "react";
import { USER_ROUTES } from "../config/api";
import { FaHeart } from "react-icons/fa";

import {
  DecideItemAction,
  editItemPayload,
  getToken,
  RedirectToLoginIfNot,
  userDataFromLocal,
} from "../utils/utils";
import { useRouter, useSearchParams } from "next/navigation";
import moment from "moment";
import FeaturedBooks from "../components/common/BookCard";
import testBook from "@/public/landing/testbook.png";
import { MdArrowOutward, MdStar } from "react-icons/md";
import { LuHeart } from "react-icons/lu";
import Link from "next/link";
import { FaStar } from "react-icons/fa";
import { FaRegStar } from "react-icons/fa";
import CircularRating from "./CircleRating";
import { FaRegThumbsUp } from "react-icons/fa";
import { FaRegThumbsDown } from "react-icons/fa";
import Select, { StylesConfig } from "react-select";
import AddRating from "./AddRating";
import { toast } from "react-toastify";
import { HiOutlineChatBubbleLeftRight } from "react-icons/hi2";
import { HiBookmark } from "react-icons/hi";
import {
  editListingPrefillData,
  updateItemId,
  updateProfileComponentIndex,
  updateUserListingData,
} from "../redux/slices/storeSlice";
import { useDispatch } from "react-redux";
import {
  bookMarkItem,
  boostItem,
  delete_bookMarkItem,
  getBooksById,
  getItemBySeller,
  getReviewsOfUser,
} from "../services/profile";
import { RxCross1 } from "react-icons/rx";
import { ItemListStatusEnum } from "../config/constant";
import Skeleton from "react-loading-skeleton";
import { Circles } from "react-loader-spinner";
import Mapview from "../components/book-listing/Mapview";
import ItemRespectiveDetails from "./ItemRespectiveDetails";
import { createInitialsAvatar } from "../components/InitialAvatar/CreateInitialAvatar";
import { formatWithCommas } from "../utils/utils"
// Sample data structure for the book
const sampleBook = {
  description:
    "This is a fascinating book about the history of programming languages and their evolution over time. The author explores how different languages shaped the way we think about software development and problem-solving.",
  averageRating: 4.5,
  totalReviews: 24,
  ratings: {
    5: 12,
    4: 8,
    3: 3,
    2: 1,
    1: 0,
  },
  reviews: [
    {
      author: "Alex Johnson",
      image: "",
      date: "July 2, 2020 03:29 PM",
      rating: 5,
      comment:
        "Absolutely loved this book! It provided great insights into how programming languages evolved.",
    },
    {
      author: "Sam Wilson",
      image: "",
      date: "July 2, 2020 03:29 PM",
      rating: 4,
      comment:
        "Well-researched and engaging. Could have included more examples though.",
    },
  ],
  location: {
    address: "123 Book Street, Knowledge City, 54321",
    distance: "2.5 miles",
  },
};

// import testBook from "@/public/landing/testbook.png"
const CustomSlider = dynamic(() => import("@/app/components/common/Slider"));

function BookDetailComponent() {
  // const router = useRouter();
  const dispatch = useDispatch();
  const router = useRouter();

  const query = useSearchParams();
  const [bookState, setbookState] = useState({});
  console.log(bookState, "bookState");

  const [allBookSeller, setallBookSeller] = useState([]);
  const [activeTab, setActiveTab] = useState("Descriptions");

  const [address, setaddress] = useState("");
  const [seeMore, setseeMore] = useState(false);
  const contentRef = useRef(null);
  const smallcontentRef = useRef(null);
  const [isSeller, setisSeller] = useState(false);
  const [isLoading, setisLoading] = useState(false);
  const [allReviews, setallReviews] = useState({});
  const [isImageLoaded, setisImageLoaded] = useState(false);
  const [height, setHeight] = useState("50px");
  const [ratingData, setratingData] = useState({
    5: 0,
    4: 0,
    3: 0,
    2: 0,
    1: 0,
  });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  // let isSeller=""
  console.log("query", query.get("id"));

  let isLoggedIn = getToken();
  const settings = {
    dots: false,
    dotsClass: "custom_inside_dots slick-dots !bottom-4.5 md:!bottom-6",
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 4,
    arrows: false,
    adaptiveHeight: true,
  };
  useEffect(() => {
    if (seeMore && contentRef.current) {
      // console.log()
      setHeight(`${contentRef.current.scrollHeight}px`);
    } else {
      setHeight("50px");
    }
  }, [seeMore]);
  console.log(height, "height");

  const userId = (() => {
    try {
      const userData = JSON.parse(localStorage.getItem("userData"));
      return userData?._id || null;
    } catch (e) {
      return null;
    }
  })();

  const fetchBookDetails = async (id) => {
    try {
      setisLoading(true);
      if (!id) {
        return;
      }
      let userToken = getToken();
      let response = await getBooksById(id, userId);
      // let data = await fetch(`${USER_ROUTES.LIST_ITEM}/${id}`, {
      //     headers: {
      //         "Content-Type": "application/json",
      //         "Authorization": `Bearer ${userToken}`
      //     },
      // },)
      // let response = await data.json()
      console.log("data fetchBookDetails", response);
      if (response?.status == 200) {
        setbookState(response?.data);
        let userData = userDataFromLocal();
        if (userData?._id == response?.data?.createdBy._id) {
          setisSeller(true);
        }
      }
      setisLoading(false);
    } catch (err) {
      console.log("fetchBookDetails err", err);
    }
  };

  console.log(bookState, "bookState");

  const getItemsBySellerFunc = async (text) => {
    try {
      // let userToken = getToken()

      // let data = await fetch(`${USER_ROUTES.LIST_ITEM}/user/${bookState.createdBy._id}`, {
      //     headers: {
      //         "Content-Type": "application/json",
      //         "Authorization": `Bearer ${userToken}`
      //     },
      // },)

      let response = await getItemBySeller(bookState.createdBy._id);

      console.log("response in seller", response);
      // let response = await data.json()
      // console.log("data getAllBookOfUser", await data.json())
      if (response.data.length) {
        // setBookData(response.data)
        setallBookSeller(
          response.data.filter((item) => item._id != bookState?._id)
        );
      }
    } catch (err) {
      console.log("err", err);
    }
    // setBookData()
  };
  console.log("allBookSeller", allBookSeller);
  console.log(bookState, "bookState");

  useEffect(() => {
    fetchBookDetails(query.get("id"));
  }, [query.get("id")]);
  let actionPayload = {};
  useEffect(() => {
    if (bookState.createdBy) {
      getItemsBySellerFunc();
      getReviews(bookState);
      actionPayload = DecideItemAction(bookState.status);
    }
  }, [bookState]);

  let ratings = "1";
  const total = Object.values(ratings).reduce((acc, val) => acc + val, 0);
  const StarReviewBreakdown = ({ ratings }) => {
    // ratings is an object like {5: 1000, 4: 800, 3: 200, 2: 50, 1: 10}
    // Calculate total reviews
    const total = Object.values(ratings).reduce((acc, val) => acc + val, 0);

    return (
      <div className=" mx-auto">
        {Object.entries(ratings)
          .sort((a, b) => b[0] - a[0]) // Sort descending by star rating
          .map(([star, count]) => {
            const percentage = (count / total) * 100;
            return (
              <div key={star} className="flex items-center space-x-3 mb-2">
                {/* Star number */}
                <span className="w-5 font-semibold flex">
                  {star}{" "}
                  <span className="text-yellow">
                    <MdStar fill="#FFD700" size={24} />
                  </span>
                </span>

                {/* Bar container */}
                <div className="flex-1 bg-gray-300 rounded-full h-3 overflow-hidden">
                  <div
                    className="bg-yellow-400 h-3"
                    style={{
                      width: `${percentage}%`,
                      backgroundColor: "#292929",
                    }}
                  ></div>
                </div>
                {/* Count display */}
                <span className="w-12 text-right font-medium text-gray-700">
                  {count >= 1000 ? (count / 1000).toFixed(1) + "k" : count}
                </span>
              </div>
            );
          })}
        {/* <p className="text-sm text-gray-500 mt-4">Total reviews: {total}</p> */}
      </div>
    );
  };
  // const ratingData = {
  //     5: 1000,
  //     4: 800,
  //     3: 200,
  //     2: 50,
  //     1: 10,
  // };

  // let allRatingData = [
  //     { name: "Kashish Akansha", disLikeCount: 5, likeCount: 10, date: "2025-05-16T11:25:26.961Z", rating: 5, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
  //     { name: "Tony Stark", disLikeCount: 30, likeCount: 1, date: "2025-05-08T11:29:26.961Z", rating: 4, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
  //     { name: "Huge Jakcman", disLikeCount: 8, likeCount: 56, date: "2025-05-17T11:29:45.961Z", rating: 4, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
  //     { name: "Richard Branson", disLikeCount: 45, likeCount: 100, date: "2025-05-16T11:54:26.961Z", rating: 3, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
  //     { name: "Captain", disLikeCount: 2, likeCount: 11, date: "2025-05-09T11:29:26.961Z", rating: 5, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
  //     { name: "Kashish Akansha", disLikeCount: 3, likeCount: 5, date: "2025-05-16T11:29:26.961Z", rating: 1, review: "Smooth buying experience on ReBookIt! Book was in great condition." }
  // ]

  const getLocationFromCoordinates = async (cordinates) => {
    console.log("cordinates check ", cordinates);

    // return
    try {
      if (!cordinates?.length) {
        return;
      }
      // let [lat, lon] = cordinates
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${cordinates[0]}&lon=${cordinates[1]}`
      );
      const data = await response.json();
      console.log(data, "location search"); // Full human-readable address
      setaddress(data?.display_name || "NA");
      return data?.display_name || "NA";
    } catch (err) {
      console.error("Error fetching location:", err);
      setaddress(bookState.address);
    }
  };
  // console.log("address", address);
  console.log(
    "bookState?.geometry?.location?.coordinates",
    bookState?.address?.geometry?.location?.coordinates
  );
  useEffect(() => {
    if (bookState?.address?.geometry?.location?.coordinates?.length)
      getLocationFromCoordinates(
        bookState?.address?.geometry?.location?.coordinates
      );
  }, [bookState?.address?.geometry?.location?.coordinates]);
  // getLocationFromCoordinates("33.425125", "-94.04768820000001")
  console.log("bookState.description", bookState.description?.length);
  console.log("see More", seeMore);

  // Navigation of chat button
  const chatNavigationHandler = (e, itemId) => {

    e.stopPropagation();
    const profileIndex = 3;
    dispatch(updateProfileComponentIndex(profileIndex));
    dispatch(updateItemId(itemId));
    if (getToken()) {
      router.push(`/profile/messages?itemId=${itemId}`);
    } else {
      RedirectToLoginIfNot(
        `${window.location.pathname}${window.location.search}`,
        router
      );
    }
  };
  console.log("window.location.", window.location);
  console.log(
    "bookState.authors[0]",
    bookState?.authors?.length && bookState?.authors[0]
  );

  const [loading, setLoading] = useState(false);

  const bookTheItemMark = async (id) => {
    setLoading(true);

    if (!getToken()) {
      let url = `${window.location.href}`;
      url = url.replace(window.location.origin, "");
      router.push(`/login?redirect=${url}`);
    }
    if (!isSeller) {
      try {
        let bookMarkResponse = await bookMarkItem({
          itemId: bookState._id || id,
          itemId: bookState._id || id,
        });
        // console.log("bookMarkResponse", bookMarkResponse)
        if (bookMarkResponse.data?._id) {
          toast.success("Item added");
        }
        setbookState({
          ...bookState,
          isBookmarked: true,
          bookmarkDoc: {
            _id: bookMarkResponse.data?._id,
          },
        });
        setLoading(false);
      } catch (err) {
        setLoading(false);

        console.log("err bookTheItemMark", err);
      }
    } else {
      if (bookState.status == ItemListStatusEnum.MARKED_AS_SOLD) {
        return;
      }
      RedirectToLoginIfNot("/become-seller", router);
      let payloadToSet = editItemPayload(bookState);
      dispatch(editListingPrefillData(payloadToSet));
    }
  };

  function Spinner() {
    return (
      <span
        className="inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
        style={{ verticalAlign: "middle" }}
        aria-label="Loading"
      />
    );
  }

  const deleteBookMarkItem = async (id) => {
    setLoading(true);
    let response = await delete_bookMarkItem(id);
    if (response.status == 200) {
      setLoading(false);
      toast.success("Removed Wishlist Item");
      setbookState({
        ...bookState,
        isBookmarked: false,
        bookmarkDoc: {},
      });
    } else {
      setLoading(false);
    }
  };

  const getReviews = async (bookState) => {
    try {
      let userReviewsData = await getReviewsOfUser(bookState?.createdBy?._id);
      console.log("userReviewsData", userReviewsData);
      if (userReviewsData.status == 200) {
        setallReviews(userReviewsData.data);
        if (userReviewsData.data.reviews) {
          let ratingDataObj = {
            5: 0,
            4: 0,
            3: 0,
            2: 0,
            1: 0,
          };
          if (userReviewsData.data.reviews.length) {
            userReviewsData.data.reviews.map((item) => {
              if (item.rating == 5) {
                ratingDataObj[5]++;
              } else if (item.rating == 4) {
                ratingDataObj[4]++;
              } else if (item.rating == 3) {
                ratingDataObj[3]++;
              } else if (item.rating == 2) {
                ratingDataObj[2]++;
              } else {
                ratingDataObj[1]++;
              }
            });

            console.log(ratingDataObj, "ratingDataObj");
            setratingData(ratingDataObj);
          }
        }
      }
    } catch (err) {
      console.log("getReviews err", err);
    }
  };
  console.log("allReviews", allReviews);

  console.log("contentRef.current", contentRef?.current?.scrollHeight);
  const chatFunc = (e) => {
    if (!isSeller) {
      chatNavigationHandler(e, query.get("id"));
    } else {
      if (DecideItemAction(bookState.status).boost) {
        let docElement = document
          .getElementById("myModal")
          .classList.remove("hidden");
      }
    }
  };

  // const bookMarkfunc = () => {

  //     if (!isSeller) {
  //         toast.success("Work in progress")
  //     } else {
  //         let payloadToSet = {
  //             currentStep: 3,
  //             completedStep: 2,
  //             category: { _id: bookState.categoryId._id, name:bookStatecategoryId.name|| "" },
  //             subCategory
  //                 : { _id: bookState.subCategoryId._id||"", name:bookState.subCategoryId.name ||"" },
  //             listData: {
  //                 tags: bookState.tags||[],
  //                 ISBN: bookState.isbn_number||"",
  //                 bookName: bookState.itemName||"",
  //                 desciption: bookState.description||"",
  //                 bookAuthor: bookState.author||"",
  //                 price: bookState.price||"",
  //                 bookCondition: "",
  //                 quantity: 0,
  //                 address: bookState.address||"",
  //                 locationCoordinates: {
  //                     lat: bookState.location.coordinates[0], lng:bookState.location.coordinates[1]
  //                 },
  //                 OTP: "",
  //                 isVerified: false,
  //                 bookImages: {
  //                     cover: bookState.coverImage||"",
  //                     front: bookState.frontImage||"",
  //                     middle: middleImage.middleImage|| "",
  //                     spine: bookState.spineImage||"",
  //                 }
  //             }
  //         }
  //         // dispatch(updateUserListingData(payloadToSet))
  //     }
  // }

  //    const bookTheItemMark = async (id) => {
  //         try {

  //             let bookMarkResponse = await bookMarkItem({ itemId: id })
  //             console.log("bookMarkResponse", bookMarkResponse)
  //             if (bookMarkResponse.data?._id) {
  //                 toast.success("Item added")
  //             }
  //         } catch (err) {
  //             console.log("err bookTheItemMark", err)
  //         }
  //     }
  const attachXYZ = (string) => {
    if (string?.includes("http")) {
      return string
    } else {
      return "https://" + string
    }
  }
  const btnTextBoostChat = () => {
    if (isSeller) {
      if (bookState.status == ItemListStatusEnum.ACCEPTED) {
        return { text: "Boost Item", enable: true };
      } else {
        return { text: "Boost Item", enable: false };
      }
    } else {
      return { text: "Chat", enable: true };
    }
  };
  let starRate = Array.from({
    length: Math.ceil(allReviews?.averageRating),
  }).map((item) => "hii");
  console.log(
    "array",
    Array.from({ length: Math.ceil(allReviews?.averageRating) }).map(
      (item) => "hii"
    )
  );

  console.log(
    "center check",
    bookState?.address?.geometry?.location?.coordinates
  );

  const boostItemFunc = async (id) => {

    try {
      let response = await boostItem(id);
      console.log(response);
      if (response.status == 200) {
        document.getElementById("myModal").classList.add("hidden");
        toast.success(`Boost Initiated Successfull`);
      }
    } catch (err) { }
  };
  console.log("setisLoading", isLoading);
  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Circles
          height="80"
          width="80"
          color="#4fa94d"
          ariaLabel="circles-loading"
          wrapperStyle={{}}
          wrapperClass=""
          visible={true}
        />
      </div>
    );
  } else {
    return (
      <div className="bookDetailContainer max-w-[1500px] mx-auto mt-[10px] md:mt-[50px] lg:mt-[90px] md-px-4 sm:px-12 md:px-8">
        <section className="w-full mb-12 flex flex-col md:flex-row gap-6">
          {/* LEFT: Images */}
          <div className="w-full  md:w-[35%] flex flex-col 0 gap-3">
            {/* Main Image Display */}
            <div className="relative w-full bg-[#FFFBF6] border-[1.42px] border-[#F1F1F1] overflow-hidden">
              {bookState?.images?.[currentImageIndex] && (
                <img
                  src={
                    bookState.images[currentImageIndex] || "/images/book_1.jpg"
                  }
                  alt={`Book view ${currentImageIndex + 1}`}
                  onLoad={() => setisImageLoaded(true)}
                  className={`
        w-full 
        h-auto 
        max-h-[300px]       /* constrain height if you like */
        mx-auto 
        object-contain      /* show entire image */
        transition-opacity 
        duration-300 
        ${isImageLoaded ? "opacity-100" : "opacity-0"}
      `}
                />
              )}
            </div>

            {/* Thumbnails */}
            <div className="flex gap-2 overflow-x-auto no_scrollbar py-2">
              {bookState?.images?.map((src, idx) => (
                <button
                  key={idx}
                  onClick={() => setCurrentImageIndex(idx)}
                  className={`flex-shrink-0 w-[22%] sm:w-[18%] p-1 rounded bg-[#FFFBF6] ${idx === currentImageIndex ? "" : "border-[#F1F1F1] border"
                    } transition`}
                >
                  <img
                    src={src || "/images/book_1.jpg"}
                    alt={`Thumbnail ${idx + 1}`}
                    onLoad={() => setisImageLoaded(true)}
                    className="w-full h-auto object-cover"
                    style={{ aspectRatio: "3/4" }}
                  />
                </button>
              ))}
            </div>
          </div>

          {/* RIGHT: Details */}
          <div className="w-full md:w-[60%] flex flex-col md:gap-4 pl-0 md:pl-[3%]">
            {/* Title and Price */}
            <div className="space-y-2">
              <p className="text-[#212A30] text-[16px] font-medium md:text-[19px]">
                {bookState?.categoryId?.name || "Sample Category"}
                {bookState?.subCategoryId?.name &&
                  `/${bookState?.subCategoryId?.name}`}
                {bookState?.subSubCategoryId?.name &&
                  `/${bookState?.subSubCategoryId?.name}`}
                {bookState?.subSubSubCategoryId?.name &&
                  `/${bookState?.subSubSubCategoryId?.name}`}
              </p>
              <p className="text-[#212A30] text-2xl md:text-[30px] font-bold mb-2 md:mb-[20px]">
                {bookState?.title}
              </p>

              <div className="flex flex-wrap justify-between items-center gap-2 mb-2 md:mb-[20px]">
                <button className="text-white text-[20px] md:text-[33px] global_linear_gradient rounded-full py-1.5 px-5">
                  J${formatWithCommas(bookState?.price)}
                </button>
                <p className=" md:text-[21px] text-lg leading-[27px]">
                  Date:{" "}
                  <span className="font-medium">
                    {moment(bookState?.createdAt).format("DD-MM-YYYY")}
                  </span>
                </p>
              </div>
            </div>

            <hr className="border-t border-gray-300 hidden md:block" />

            {/* Seller Info */}
            <div className="flex flex-col space-y-4">
              <div className="flex items-center gap-2">
                <p className="text-lg md:text-[21px] leading-[27px]">
                  <span className="font-medium">
                    {`${bookState?.createdBy?.firstName} ${bookState?.createdBy?.lastName}`}
                  </span>
                </p>
                <div className="flex items-center gap-1">
                  {allReviews?.averageRating && (
                    <span className="bg-[#14884C] text-white text-[8px] md:text-[12px] py-[1px] px-2 rounded">
                      {allReviews?.averageRating}
                    </span>
                  )}
                  <div className="flex">
                    {starRate.map((_, i) => (
                      <IoMdStar key={i} size={16} />
                    ))}
                  </div>
                </div>
              </div>

              <ItemRespectiveDetails bookState={bookState} />
              {/* <p className="text-xs  mb-2 text-[16px] md:text-[21px] leading-[27px]">
          {[labelItemToKind.ScholarshipAwardItem.website]}:{" "}
          <span className="font-medium"><a href={attachXYZ(bookState?.website)} target="_blank">{bookState?.website}</a></span>
        </p> */}

              {bookState?.website && <p className="mb-4 text-md md:text-[21px] leading-[27px]">
                Website:{" "}
                <span className="font-medium text-blue-500"><a href={attachXYZ(bookState?.website)} target="_blank">{bookState?.website}</a></span>
              </p>}
              <p className=" text-md md:text-[21px] leading-[27px]">
                Location:{" "}
                <span className="font-medium">
                  {bookState?.address?.formatted_address}
                </span>
              </p>
            </div>

            {/* Description on mobile */}
            {/* <div className="md:hidden mt-4">
              <h4 className="font-semibold mb-2">Description:</h4>
              <p
                ref={smallcontentRef}
                className={`text-[16px] overflow-hidden transition-all duration-500 ease-in-out ${
                  seeMore ? "max-h-[300px]" : "max-h-[50px]"
                }`}
              >
                {bookState?.description}
              </p>
              {smallcontentRef.current?.scrollHeight > 50 && (
                <button
                  className="text-blue-500 text-xs mt-1"
                  onClick={() => setseeMore(!seeMore)}
                >
                  See {seeMore ? "less" : "more"}
                </button>
              )}
            </div> */}

            {/* Buttons */}
            <div className="flex flex-wrap gap-3 mt-5">
              <button
                disabled={!btnTextBoostChat().enable}
                onClick={chatFunc}
                className={`flex gap-2 items-center py-2 px-4 rounded-full text-lg md:px-20 md:text-[20px] lg:text-[24px] ${btnTextBoostChat().enable
                  ? "global_linear_gradient text-white cursor-pointer"
                  : "bg-[gray] text-[white] border-[2px] cursor-not-allowed opacity-50 pointer-events-none"
                  }`}
              >
                {!isSeller && <HiOutlineChatBubbleLeftRight />}
                <span>{btnTextBoostChat().text}</span>
              </button>
              {isSeller ? (
                <button
                  onClick={bookTheItemMark}
                  className="flex gap-2 items-center py-2 px-4 md:px-15 rounded-full text-lg md:text-[20px] lg:text-[24px] bg-white border-2 border-[#211F54] text-[#211F54]"
                >
                  <span>Edit</span>
                </button>
              ) : (
                <button
                  onClick={() =>
                    bookState?.isBookmarked
                      ? deleteBookMarkItem(bookState?.bookmarkDoc?._id)
                      : bookTheItemMark()
                  }
                  className={`flex gap-2 items-center py-2 px-4 md:px-15 rounded-full text-lg md:text-[20px] lg:text-[24px] border-2 border-[#211F54] ${bookState?.isBookmarked
                    ? "bg-[#211F54] text-white"
                    : "bg-white text-[#211F54]"
                    }`}
                >
                  {loading ? (
                    <>
                      <span
                        className="inline-block w-5 h-5 border-2 border-blue-500 border-t-white rounded-full animate-spin"
                        style={{ verticalAlign: "middle" }}
                        aria-label="Loading"
                      />
                      <span className="ml-2 "></span>
                    </>
                  ) : (
                    <>
                      {/* <HiBookmark
                        className="text-[20px]"
                        color={bookState?.isBookmarked ? "#FFD700" : "black"}
                        fill={bookState?.isBookmarked ? "#FFD700" : "red"}
                      /> */}
                      <FaHeart size={20} />

                    </>
                  )}
                  <span>Wishlist</span>
                </button>
              )}
            </div>
          </div>
        </section>

        <div className="tabbed-interface">
          {/* Tab Navigation */}
          <div className="tab-navigation flex overflow-x-auto whitespace-nowrap border-b border-[#DBDBDB] bg-[#FAFAFA]">
            {["Descriptions", "Reviews", "Location"].map((tab) => (
              <button
                key={tab}
                className={`tab-button flex-shrink-0 px-4 py-2 font-medium text-sm md:text-base relative ${activeTab === tab
                  ? "text-[#3C3C3C] border-b-2 border-[#3C3C3C]"
                  : "text-gray-500 hover:text-gray-700"
                  }`}
                onClick={() => {
                  setActiveTab(tab);
                  document
                    .getElementById(`${tab.toLowerCase()}-content`)
                    .scrollIntoView({ behavior: "smooth", block: "start" });
                }}
              >
                {tab}
                {activeTab === tab && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#3C3C3C]" />
                )}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {/* Descriptions Tab */}
            <div
              className="description-content p-4 md:p-[24px]"
              id="descriptions-content"
            // hidden={activeTab !== "Descriptions"}
            >
              <p className="text-gray-700">
                {bookState?.description1 || (
                  <div className="space-y-2">
                    {/* fallback content */}
                    <p>{bookState.description}</p>
                  </div>
                )}
              </p>
            </div>

            <hr
              className="my-2 md:my-[20px] border-t border-gray-300"
            // hidden={activeTab === "Reviews" && "Descriptions"}
            />

            {/* Location Tab */}
            <div
              className="location-content px-4 md:px-[24px] py-4 md:py-[24px]"
              id="location-content"
            // hidden={activeTab !== "Location"}
            >
              <h3 className="text-xl md:text-[40px] font-bold mb-2">
                Seller&apos;s Location
              </h3>
              <Mapview
                width="100%"
                height="300px"
                data={bookState}
                isSingleBookDetails={true}
                center={{
                  lat: bookState?.address?.geometry?.location?.coordinates?.[1],
                  lng: bookState?.address?.geometry?.location?.coordinates?.[0]
                }}
              />
            </div>

            <hr
              className="my-5 md:my-[40px] border-t border-gray-300"
            // hidden={activeTab !== "Reviews" || activeTab === "Descriptions"}
            />

            {/* Reviews Tab */}
            <div
              className="reviews-content px-4 md:px-[24px]"
              id="reviews-content"
            // hidden={activeTab !== "Reviews"}
            >
              {allReviews?._id && (
                <section>
                  <h2 className="text-2xl md:text-[45px] font-bold">
                    Seller Reviews
                  </h2>
                  <div className="mt-4 grid grid-cols-1 md:grid-cols-10 gap-4">
                    <div className="col-span-1 md:col-span-4 flex items-center">
                      <CircularRating
                        rating={allReviews.averageRating || 0}
                        maxRating={5}
                        size={80}
                      />
                      <div className="ml-3">
                        <div className="flex gap-1">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <MdStar key={i} fill="#FFD700" size={20} />
                          ))}
                        </div>
                        <div className="text-sm text-gray-600">
                          From {allReviews?.reviews?.length || 0} Reviews
                        </div>
                      </div>
                    </div>

                    <div className="col-span-1 md:col-span-6">
                      <StarReviewBreakdown ratings={ratingData} />
                    </div>
                  </div>
                </section>
              )}

              <section className="mt-6 space-y-6">
                <h2 className="text-xl md:text-[34px] font-bold">
                  Review Lists
                </h2>
                {allReviews?._id ? (
                  allReviews.reviews.map((item) => (
                    <div key={item._id} className="individual_review">
                      <div className="flex mb-2">
                        {Array.from({ length: item.rating }).map((_, idx) => (
                          <MdStar key={idx} fill="#FFD700" size={18} />
                        ))}
                      </div>
                      <p className="font-semibold text-base mb-1.5">
                        "{item.comment}"
                      </p>
                      <p className="text-sm text-gray-500 mb-2">
                        {moment(item.createdAt).format("MMMM D, YYYY hh:mm a")}
                      </p>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <img
                            className="w-10 h-10 rounded-full"
                            src={item?.reviewer?.profileImage || createInitialsAvatar(`${item?.reviewer?.firstName}  ${item?.reviewer?.lastName || ""}`, {
                              bgColor: "#3f51b5",
                              textColor: "#ffffff",
                            })}
                            alt="Reviewer"
                          />
                          <p className="ml-2 text-base font-semibold">
                            {item?.reviewer?.firstName}
                          </p>
                        </div>
                      </div>
                      <hr className="my-4 border-t border-gray-300" />
                    </div>
                  ))
                ) : (
                  <p>No Review Available</p>
                )}

                {isLoggedIn && !isSeller && (
                  <AddRating bookState={bookState} getReviews={getReviews} />
                )}
              </section>
            </div>
          </div>
        </div>

        <hr className="my-5 border-t border-gray-300 md:my-[40px]" />

        {!isSeller && allBookSeller.length > 0 && (
          <section>
            <div className="flex justify-between">
              <div>
                <text className="md:text-[45px]  font-bold md:text-3xl sm:text-[24px] ">
                  BOOKS BY SAME SELLER
                </text>
                <p className="max-w-[700px] my-8">
                  Explore stories, knowledge, and imagination with ReBookIt.
                  Find academic essentials, timeless novels, and rare gems—all
                  in one place.
                </p>
              </div>

              <div className="hidden md:block">
                {/* <Link href="/" aria-label="View all book categories"> */}
                <svg
                  width="178"
                  height="72"
                  viewBox="0 0 178 72"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="cursor-pointer"
                  onClick={() => {
                    router.push("/search");
                  }}
                >
                  <path
                    d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285"
                    stroke="#211F54"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017"
                    stroke="#0161AB"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285"
                    stroke="#EFDC2A"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285"
                    stroke="#0161AB"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M140.693 6L36.937 5.99999"
                    stroke="#FF0009"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M140.693 65.457L36.937 65.457"
                    stroke="#4A8B40"
                    strokeWidth="11.679"
                  />
                  <rect
                    x="11.6016"
                    y="7.93848"
                    width="154.01"
                    height="54.6036"
                    rx="27.3018"
                    fill="white"
                  />
                  <text
                    x="50%"
                    y="50%"
                    dominantBaseline="middle"
                    textAnchor="middle"
                    fontSize="20"
                    fill="#211F54"
                    fontFamily="Poppins, sans-serif"
                  >
                    View All
                  </text>
                </svg>
                {/* </Link> */}
              </div>
            </div>

            <div
              // className="my-5 grid grid-cols-2 lg:grid-cols-4 gap-2.5 md:my-14 md:gap-8 md:justify-between lg:mt-[30px]"
              className=""
            >
              <CustomSlider sliderSettings={settings}>
                {allBookSeller.map((item, idx) => (
                  <article
                    key={idx}
                    className="border border-[#80808017] pt-1 px-[3px] pb-[15px] lg:border-0"
                  >
                    <div className="bg-gray px-[30px] relative flex justify-center items-center py-1.5 bg-[#f1f1f1] w-full aspect-square lg:aspect-square lg:py-2.5 lg:px-2.5">
                      <img
                        // src={item.coverImage || item.backCoverImage}
                        src={item.images}
                        alt="book image"
                        fill
                        className={`object-cover px-[30px] max-h-[250px]  transition-opacity duration-300 ${isImageLoaded ? "opacity-100" : "opacity-0"
                          }`}
                        sizes="(min-width: 1024px) 25vw, (min-width: 768px) 50vw, 100vw"
                        onLoad={() => setisImageLoaded(true)}
                      />
                      <div
                        className="h-7 w-7 p-1 rounded-full border border-gray-200 bg-white flex justify-center items-center absolute top-1 right-1 cursor-pointer"
                        onClick={() => bookTheItemMark(item._id)}
                      >
                        <LuHeart
                          size={17}
                          color="#000"
                          className="opacity-90"
                        />
                      </div>
                    </div>

                    <div className="pt-2 pl-2 pr-2.5 md:text-[20px] lg:pt-5 lg:pb-[28px]">
                      <p
                        title={item.title}
                        className="text-sm leading-[19px] font-semibold lg:text-[20px] lg:leading-[26px] lg:w-11/12 line-clamp-1"
                      >
                        {item.title}
                      </p>

                      <div className="flex justify-between items-center my-2.5">
                        <p className="font-bold self-end">
                          <span className="text-[#4D7906] lg:text-[20px] lg:leading-[22px]">
                            JMD{" "}
                          </span>
                          ${formatWithCommas(item.price)}
                        </p>
                        <div className="cursor-pointer global_linear_gradient p-2 rounded-full flex justify-center items-center lg:h-[46px] lg:w-[46px]">
                          <Link
                            href={{
                              pathname: "/book-detail",
                              query: { id: item._id },
                            }}
                            onClick={() => document.body.scrollTop()}
                            aria-label="View all book categories"
                          >
                            <MdArrowOutward
                              className="h-[12px] w-[12px] lg:w-[16px] lg:h-[16px]"
                              color="#fff"
                            />
                          </Link>
                        </div>
                      </div>
                    </div>
                  </article>
                ))}
              </CustomSlider>
            </div>
            <div className="block md:hidden mx-auto flex justify-center">
              <Link href="/" aria-label="View all book categories">
                <svg
                  width="178"
                  height="72"
                  viewBox="0 0 178 72"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285"
                    stroke="#211F54"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017"
                    stroke="#0161AB"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285"
                    stroke="#EFDC2A"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285"
                    stroke="#0161AB"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M140.693 6L36.937 5.99999"
                    stroke="#FF0009"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M140.693 65.457L36.937 65.457"
                    stroke="#4A8B40"
                    strokeWidth="11.679"
                  />
                  <rect
                    x="11.6016"
                    y="7.93848"
                    width="154.01"
                    height="54.6036"
                    rx="27.3018"
                    fill="white"
                  />
                  <text
                    x="50%"
                    y="50%"
                    dominantBaseline="middle"
                    textAnchor="middle"
                    fontSize="20"
                    fill="#211F54"
                    fontFamily="Poppins, sans-serif"
                  >
                    View All
                  </text>
                </svg>
              </Link>
            </div>
          </section>
        )}

        {/* {!isSeller && (
          <hr className="my-5 border-t border-gray-300 md:my-[100px]" />
        )} */}

        <div
          id="myModal"
          className="fixed inset-0 flex items-center justify-center z-50 bg-[#EAEAEA]/60 backdrop-blur-sm transition-opacity hidden duration-300 ease-in-out"
        >
          <div className="bg-[#fdfdfd] p-[50px] w-[500px] rounded-lg shadow-lg relative transform transition-all duration-300 ease-in-out scale-95 opacity-0 animate-fadeIn">
            {/* Close Button */}
            <div
              className="absolute top-[-10px] right-[-10px] w-[50px] h-[50px] rounded-full bg-white flex items-center justify-center cursor-pointer"
              onClick={() => {
                document.getElementById("myModal").classList.add("hidden");
                setmodelForAnswer(false);
              }}
            >
              <RxCross1 className="" size={23} />
            </div>

            <div className="relative w-full h-full  bg-[#FFFBF6] bookDetailMainImageShadow">
              {bookState?.images?.length && (
                <img
                  src={bookState.images[0] || "/images/book_1.jpg"}
                  alt="...loading"
                  fill
                  className={`object-cover mx-auto min-w-350px  transition-opacity duration-300 ${isImageLoaded ? "opacity-100" : "opacity-0"
                    }`}
                  onLoad={() => setisImageLoaded(true)}
                  sizes="33w"
                />
              )}
            </div>
            <p className="text-[#212A30] text-[14px] md:text-[28px] font-bold my-4">
              {bookState?.itemName}
            </p>

            <p className="text-[#212A30] text-[12px] md:text-[19px] text-sm my-2">
              Category:{" "}
              <span className="font-medium">
                {bookState?.categoryId?.name || "Sample Category"}
              </span>
            </p>
            <p className="text-[12px] md:text-[21px] leading-[12px] md:leading-[27px] my-2 ">
              Location:{" "}
              <span className="font-medium">
                {bookState.address?.formatted_address || address}
              </span>
            </p>

            <div className="flex items-center my-4">
              <p className="text-[27px] font-semibold">Price</p>
              <button className="text-white text-[20px] ml-2 bg-[#1E2858] rounded-full py-1.5 px-3">
                J${formatWithCommas(bookState?.price)}
              </button>
            </div>
            {/* Action Button */}
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => boostItemFunc(bookState._id)}
                className="py-[1rem] px-[30px] text-[25px]   leading-[18px] text-center text-white rounded-full global_linear_gradient"
              >
                Confirm Boost
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default BookDetailComponent;
