"use client";

import React, { useEffect, useMemo, useRef, useState } from "react";
import bookListingScss from "./book-listing.module.scss";
import { MdKeyboardArrowDown } from "react-icons/md";
import { FaListUl } from "react-icons/fa6";
import { LuLayoutGrid } from "react-icons/lu";
import Image from "next/image";
import dynamic from "next/dynamic";
import { HiOutlineHeart } from "react-icons/hi";
import { PiShareFatFill } from "react-icons/pi";
import { useRouter, useSearchParams } from "next/navigation";
import { ELASTIC_DB_ROUTES } from "../config/api";
import { FaStar, FaCheck } from "react-icons/fa";
import {
  formatWithCommas,
  getCurrentLocationAndAddress,
  ipBasedLocationFinder,
  parseGeocodeResponse,
} from "../utils/utils";
import { CiCircleRemove } from "react-icons/ci";
import {
  Debounce,
  getToken,
  globleBookmarkFunc,
  RedirectToLoginIfNot,
  userDataFromLocal,
} from "../utils/utils";
import { toast } from "react-toastify";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";
import {
  updateItemId,
  updateProfileComponentIndex,
  updateUserLocationData,
} from "../redux/slices/storeSlice";
import No_result_found_search_page from "@/public/images/No_result_found_search_page.svg";
import {
  bookMarkItem,
  delete_bookMarkItem,
  getCategories,
  getSubCategories,
} from "../services/profile";
import ShareCard from "../components/common/ShareCard";
import { bookSearch } from "../services/bookDetails";
import { FaHeart } from "react-icons/fa";
import ItemRespectiveDetails from "./ItemRespectiveDetails";
import Search from "./Search";
import { FaMapLocation } from "react-icons/fa6";

const CustomSlider = dynamic(() => import("@/app/components/common/Slider"));
const MapView = dynamic(() => import("@/app/components/book-listing/Mapview"));
const GoogleSearch = dynamic(() => import("@/app/search/GoogleMapSearch.js"));
const BuyAndSellComponent = dynamic(() =>
  import("@/app/components/about/BuyAndSellComponent")
);

function BookListing() {
  ////////////////////////////////
  const searchParams = useSearchParams();
  const categoryId = useMemo(
    () => searchParams.get("category"),
    [searchParams]
  );
  const subCategoryId = useMemo(
    () => searchParams.get("subCategory"),
    [searchParams]
  );

  ////////////////////////////////

  let GoogleSearchBack = GoogleSearch;
  const router = useRouter();

  const [bookmarkedItems, setBookmarkedItems] = useState({});
  const [location, setLocation] = useState("");
  const dispatch = useDispatch();
  const filterRef = useRef(null);
  const isListCheck = searchParams.get("isList");
  const [isList, setIsList] = useState(isListCheck ? false : true);
  const [filterOccurred, setFilterOccurred] = useState(false);
  const [selectedId, setselectedId] = useState(null);
  const [selectedParish, setselectedParish] = useState(null);
  const [searchCoordinates, setsearchCoordinates] = useState(null);
  const [isParish, setIsParish] = useState(false);

  console.log(searchCoordinates, selectedParish, isParish, "searchCoordinates");

  /////////////new
  const userLocationData = useSelector(
    (state) => state.storeData.userLocationData
  );

  const [locationData, setLocationData] = useState();

  useEffect(() => {
    // If userLocationData is not present in the store, fetch and store it
    if (
      !userLocationData ||
      !userLocationData.latitude ||
      !userLocationData.longitude
    ) {
      (async () => {
        try {
          const locationData = await ipBasedLocationFinder();
          // Store in redux
          dispatch(updateUserLocationData(locationData));
          // Store in local state as well
          setLocationData({
            locality: locationData.locality || "",
            latitude: locationData.latitude || "",
            longitude: locationData.longitude || "",
          });
        } catch (e) {
          console.warn("Failed to get user location data:", e);
        }
      })();
    } else {
      setLocationData({
        locality: userLocationData.locality || "",
        latitude: userLocationData.latitude || "",
        longitude: userLocationData.longitude || "",
      });
    }
  }, [userLocationData, dispatch]);

  console.log(locationData, "locationData");

  const [bookList, setBookList] = useState([]);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    totalPages: 1,
    totalItems: 0,
    showLoader: false,
  });
  const [booksLoading, setBooksLoading] = useState(false);

  // New Filters State
  // Load selected category and subcategory from localStorage if available
  const getInitialCategory = () => {
    if (typeof window !== "undefined") {
      try {
        const stored = localStorage.getItem("selectedCategory");
        if (stored) {
          const parsed = JSON.parse(stored);
          return {
            id: parsed.id || "",
            name: parsed.name || "Category",
          };
        }
      } catch (e) {}
    }
    return { id: "", name: "Category" };
  };

  const getInitialSubCategory = () => {
    if (typeof window !== "undefined") {
      try {
        const stored = localStorage.getItem("selectedSubCategory");
        if (stored) {
          const parsed = JSON.parse(stored);
          return {
            id: parsed.id || "",
            name: parsed.name || "Sub-Category",
          };
        }
      } catch (e) {}
    }
    return { id: "", name: "Sub-Category" };
  };

  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState(
    getInitialCategory().id
  );
  const [selectedCategoryName, setSelectedCategoryName] = useState(
    getInitialCategory().name
  );
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState(
    getInitialSubCategory().id
  );
  const [selectedSubCategoryName, setSelectedSubCategoryName] = useState(
    getInitialSubCategory().name
  );
  const [openCategory, setOpenCategory] = useState(false);
  const [openSubCategory, setOpenSubCategory] = useState(false);

  // Skeleton Components
  const ListViewSkeleton = ({ count = 4 }) => (
    <div className="flex flex-col gap-5 w-full lg:w-[65%] xl:w-[60%]  lg:gap-[30px] order-2 lg:order-1">
      {Array.from({ length: count }).map((_, idx) => (
        <div
          key={`list-skeleton-${idx}`}
          className="border border-[#EAEAEA] p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 h-fit bg-white rounded animate-pulse"
          style={{ minHeight: 180 }}
        >
          {/* Image Skeleton */}
          <div className="col-span-1 w-full aspect-[3/4] sm:aspect-[2/3] md:aspect-auto overflow-hidden bg-gray-200 rounded relative flex items-center justify-center">
            <div className="w-[80%] h-[80%] bg-gray-300 rounded" />
          </div>
          {/* Details Skeleton */}
          <div className="col-span-1 md:col-span-3 flex flex-col gap-3 justify-between">
            <div>
              <div className="h-5 w-2/3 bg-gray-200 rounded mb-2" />
              <div className="h-4 w-1/3 bg-gray-200 rounded mb-2" />
              <div className="h-4 w-1/2 bg-gray-200 rounded mb-2" />
              <div className="h-4 w-1/4 bg-gray-200 rounded mb-2" />
              <div className="h-4 w-1/3 bg-gray-200 rounded mb-2" />
              <div className="h-4 w-1/2 bg-gray-200 rounded mb-2" />
            </div>
            <div className="flex items-center gap-2 mt-2">
              <div className="h-8 w-20 bg-gray-200 rounded" />
              <div className="h-8 w-8 bg-gray-200 rounded-full" />
              <div className="h-8 w-8 bg-gray-200 rounded-full" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  const GridViewSkeleton = ({ count = 8 }) => (
    <>
      {Array.from({ length: count }).map((_, idx) => (
        <div
          key={`grid-skeleton-${idx}`}
          className="relative p-[13px] pb-4 border border-[#EAEAEA] bg-white rounded animate-pulse"
        >
          {/* Image Skeleton */}
          <div className="relative h-[218px] aspect-[3/4] bg-[#FFFBF6] py-2.5 flex justify-center items-center box-border">
            <div className="w-[90%] h-[80%] bg-gray-200 rounded" />
          </div>

          {/* Content Skeleton */}
          <div className="mt-[11px]">
            <div className="h-6 w-3/4 bg-gray-200 rounded mb-2" />
            <div className="h-4 w-1/2 bg-gray-200 rounded mb-2" />
            <div className="h-4 w-2/3 bg-gray-200 rounded mb-2" />
            <div className="h-4 w-1/3 bg-gray-200 rounded mb-2" />
            <div className="h-4 w-1/2 bg-gray-200 rounded mb-2" />
            <div className="h-4 w-1/4 bg-gray-200 rounded mb-2" />
            <div className="h-4 w-1/3 bg-gray-200 rounded mb-2" />

            <div className="mt-4 flex gap-[34px] justify-between items-center">
              <div className="h-10 w-[120px] bg-gray-200 rounded-full" />
              <div className="h-6 w-20 bg-gray-200 rounded" />
            </div>
          </div>
        </div>
      ))}
    </>
  );

  const GridViewSkeletonContainer = ({ count = 8 }) => (
    <div
      className="grid grid-cols-1  md:grid-cols-2 lg:grid-cols-3 gap-[33px] mt-5 pb-10 md:pb-[70px]"
      style={{ display: "contents" }}
    >
      <GridViewSkeleton count={count} />
    </div>
  );

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    // Handle category selection
    if (categoryId && categories.length) {
      if (categoryId !== selectedCategoryId) {
        const cat = categories.find((c) => c._id === categoryId);
        setSelectedCategoryId(categoryId);
        setSelectedCategoryName(cat ? cat.name : "Category");
        localStorage.setItem(
          "selectedCategory",
          JSON.stringify({ id: categoryId, name: cat ? cat.name : "Category" })
        );
        fetchSubCategories(categoryId);
        // Reset subcategory if category changes
        setSelectedSubCategoryId("");
        setSelectedSubCategoryName("Sub-Category");
        localStorage.removeItem("selectedSubCategory");
      }
    }

    // Handle subcategory selection (only if subCategories are loaded)
    if (subCategoryId && subCategories.length) {
      if (subCategoryId !== selectedSubCategoryId) {
        const subCat = subCategories.find((s) => s._id === subCategoryId);
        setSelectedSubCategoryId(subCategoryId);
        setSelectedSubCategoryName(subCat ? subCat.name : "Sub-Category");
        localStorage.setItem(
          "selectedSubCategory",
          JSON.stringify({
            id: subCategoryId,
            name: subCat ? subCat.name : "Sub-Category",
          })
        );
      }
    }
    // eslint-disable-next-line
  }, [categoryId, subCategoryId]);

  useEffect(() => {
    if (locationData) {
      fetchSearchData();
    }
  }, [
    categoryId,
    subCategoryId,
    selectedCategoryId,
    selectedSubCategoryId,
    searchCoordinates,
    selectedParish,
    isParish,
    locationData,
  ]);

  // Additional effect to handle location data changes more robustly
  useEffect(() => {
    // If we have userLocationData but no locationData, sync them
    if (userLocationData && !locationData) {
      setLocationData({
        locality: userLocationData.locality || "",
        latitude: userLocationData.latitude || "",
        longitude: userLocationData.longitude || "",
      });
    }
  }, [userLocationData, locationData]);

  useEffect(() => {
    if (pagination.page !== 1) {
      fetchSearchData(pagination.page);
    }
  }, [pagination.page]);

  let userData = userDataFromLocal();

  const fetchSearchData = async (isLoadMore) => {
    try {
      if (!isLoadMore) {
        setBooksLoading(true);
      }
      let userData = userDataFromLocal();
      let urlstring = ELASTIC_DB_ROUTES.SEARCH?.replace(
        "{{page}}",
        isLoadMore ? pagination.page : 1
      );

      if (userData) {
        urlstring = urlstring + `&userId=${userData._id}`;
      }
      const finalCategoryId = categoryId || selectedCategoryId || null;
      const finalSubCategoryId = subCategoryId || selectedSubCategoryId || null;

      // Ensure we have valid location data
      let locationFilter = {};
      if (userLocationData?.isParishSelection && userLocationData?.locality) {
        locationFilter = { parish: [userLocationData.locality] };
      } else if (locationData?.latitude && locationData?.longitude) {
        locationFilter = {
          coordinates: [locationData.longitude, locationData.latitude],
          maxDistanceInKm: 100,
        };
      } else if (userLocationData?.latitude && userLocationData?.longitude) {
        // Fallback to userLocationData if locationData is not available
        locationFilter = {
          coordinates: [userLocationData.longitude, userLocationData.latitude],
          maxDistanceInKm: 100,
        };
      }

      let payload = {
        filters: {
          ...(finalCategoryId ? { category: [finalCategoryId] } : {}),
          ...(finalSubCategoryId ? { subCategory: [finalSubCategoryId] } : {}),
          ...locationFilter,
        },
        sort: {},
      };
      let fetchedBookData = await bookSearch(urlstring, payload);
      if (isLoadMore) {
        setBookList((prevList) => [
          ...prevList,
          ...(fetchedBookData?.data?.data || []),
        ]);
        setPagination((prev) => ({
          ...prev,
          showLoader: false,
        }));
      } else {
        setBookList(fetchedBookData?.data?.data);
      }
      setPagination({
        page: fetchedBookData?.data?.page,
        pageSize: fetchedBookData?.data?.pageSize,
        totalPages: fetchedBookData?.data?.totalPages,
        totalItems: fetchedBookData?.data?.totalCount,
      });
      setBooksLoading(false);
    } catch (error) {
      console.log("error", error);
      setBooksLoading(false);
      setPagination((prev) => ({
        ...prev,
        showLoader: false,
      }));
    }
  };

  const fetchCategories = async () => {
    try {
      let fetchedCategories = await getCategories();
      if (fetchedCategories?.status == 200) {
        const cats = fetchedCategories?.data?.categories || [];
        setCategories(cats);

        // Check for categoryId in params or local state
        let selectedCatId = categoryId || getInitialCategory().id || "";
        let selectedCat = cats.find((cat) => cat._id === selectedCatId);

        // If not found, fallback to first category
        if (!selectedCat && cats.length > 0) {
          selectedCat = cats[0];
          selectedCatId = cats[0]._id;
        }

        setSelectedCategoryId(selectedCat ? selectedCat._id : "");
        setSelectedCategoryName(selectedCat ? selectedCat.name : "Category");
        localStorage.setItem(
          "selectedCategory",
          JSON.stringify({
            id: selectedCat?._id || "",
            name: selectedCat?.name || "Category",
          })
        );

        if (selectedCat && selectedCat._id) {
          await fetchSubCategories(selectedCat._id);
        }
      }
    } catch (error) {
      console.error("Category fetch error:", error);
    }
  };

  // fetchSubCategories should accept a categoryId and actually fetch subcategories
  const fetchSubCategories = async (categoryId) => {
    try {
      let response = await getSubCategories(categoryId);
      const subs = response?.data?.subCategories ?? [];
      setSubCategories(subs);

      // Check for subCategoryId in params or local state
      let selectedSubCatId = subCategoryId || getInitialSubCategory().id || "";
      let selectedSubCat = subs.find((sub) => sub._id === selectedSubCatId);

      // If found, set as selected, otherwise fallback to default
      if (selectedSubCat) {
        setSelectedSubCategoryId(selectedSubCat._id);
        setSelectedSubCategoryName(selectedSubCat.name);
        localStorage.setItem(
          "selectedSubCategory",
          JSON.stringify({ id: selectedSubCat._id, name: selectedSubCat.name })
        );
      } else {
        setSelectedSubCategoryId("");
        setSelectedSubCategoryName("Sub-Category");
        localStorage.removeItem("selectedSubCategory");
      }
    } catch (error) {
      console.error("Subcategory fetch error:", error);
    }
  };

  // this remove the param from teh url
  useEffect(() => {
    if (filterOccurred && ((categoryId && subCategoryId) || categoryId)) {
      router.replace("/search", undefined, { shallow: true });
    }
    setFilterOccurred(false);
  }, [filterOccurred]);
  /////////////////

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Modern Navigation Timing API
    const navEntries = window.performance.getEntriesByType("navigation");
    const navType = navEntries.length > 0 ? navEntries[0].type : null;

    // Fallback for older browsers
    console.log("navEntries", navEntries);
    const legacyNav =
      window.performance.navigation && window.performance.navigation.type === 1;

    if (navType === "navigate" || legacyNav) {
      const hasQuery = Object.keys(router.query || {}).length > 0;
      if (hasQuery) {
        // replace URL to "/search" without a full reload
        router.replace("/search", undefined, { shallow: true });
      }
    }
  }, [router]);

  const settings = {
    dots: true,
    dotsClass: "custom_inside_dots slick-dots !bottom-4.5 md:!bottom-6",
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    adaptiveHeight: true,
  };

  // Navigation of chat button
  const chatNavigationHandler = (e, itemId) => {
    e.stopPropagation();

    const profileIndex = 3;
    dispatch(updateProfileComponentIndex(profileIndex));
    dispatch(updateItemId(itemId));

    let redirectpath =
      `${window.location.pathname}` + `${window.location.search}`;
    console.log("redirectpath", redirectpath);
    if (getToken()) {
      router.push("/profile/messages");
      return;
    }
    RedirectToLoginIfNot(redirectpath, router);
    // router.push("/profile/messages");
  };

  // Handle click outside to close new dropdowns
  useEffect(() => {
    function handleClickOutside(event) {
      if (filterRef.current && !filterRef.current.contains(event.target)) {
        setOpenCategory(false);
        setOpenSubCategory(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [filterRef]);

  // const bookTheItemMark = async (id) => {
  //   try {
  //     let bookMarkResponse = await bookMarkItem({itemId: id});
  //     console.log("bookMarkResponse", bookMarkResponse);
  //     if (bookMarkResponse.data?._id) {
  //       toast.success("Item added");
  //     }
  //   } catch (err) {
  //     console.log("err bookTheItemMark", err);
  //   }
  // };

  const bookTheItemMark = async (id) => {
    try {
      setBookmarkedItems((prev) => ({
        ...prev,
        [id]: !prev[id], // Toggle bookmark state
      }));

      let bookMarkResponse = await bookMarkItem({ itemId: id });

      if (bookMarkResponse.data?._id) {
        toast.success(
          bookmarkedItems[id]
            ? "Item removed from Wishlist"
            : "Item Added In Wishlist"
        );
      }
      fetchSearchData();
    } catch (err) {
      setBookmarkedItems((prev) => ({
        ...prev,
        [id]: !prev[id],
      }));
      toast.error("Failed to update Wishlist");
    }
  };

  const returnCategorySubCat = (item) => {
    let str = item?.categoryDoc?.name;
    if (item?.subcategoryDoc?.name) {
      str = str + `/${item?.subcategoryDoc?.name}`;
    }
    return str;
  };

  useEffect(() => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setLocation({ lat: latitude, lng: longitude });
        },
        (error) => {
          console.error("Error getting location:", error);
        }
      );
    } else {
      console.log("Geolocation is not supported");
    }
  }, []);

  const deleteBookMarkItem = async (id) => {
    let response = await delete_bookMarkItem(id);
    if (response.status == 200) {
      fetchSearchData();
      toast.success("Removed Wishlist Item");
    }
  };
  const debouncePriceHandler = Debounce((e) => {
    setFilterOccurred(true);
    setnumberFilter(e.target.value);
  }, 800);

  const [showMap, setShowMap] = useState(false);

  return (
    <div
      className={`${bookListingScss.bookListingContainer} py-5  md:px-[50px] lg:px-[100px]`}
    >
      <section className="container-wrapper">
        <div className={`mt-2.5 mb-10 md:mt-[50px] lg:mt-[100px] `}>
          {/* Filters */}
          <section className="">
            <div className="grid grid-cols-3 mx-3 ">
              <div className="col-span-3  md:col-span-1 flex items-center gap-3   ">
                <h1 className="text-4xl font-semibold">Items</h1>
                <p className="text-sm font-semibold mb-1 text-gray-400">
                  ({pagination?.totalItems || 0} Results)
                </p>
              </div>

              <div className=" col-span-3 md:col-span-1 w-full relative flex items-center  h-[70px] ">
                <Search />
              </div>

              <div className="col-span-3 md:col-span-1 flex items-center relative w-full h-[70px] ">
                <GoogleSearchBack />
                {/* <LocationSearch setselectedParish={setselectedParish} selectedParish={selectedParish} setsearchCoordinates={setsearchCoordinates} /> */}
              </div>
            </div>

            <div className="h-full md:flex justify-between items-center gap-[9px] relative">
              <div className="mt-5 flex justify-between pr-4 z  items-center gap-[9px]  w-full">
                <div
                  className="flex gap-2 mx-[10px]  no_scrollbar"
                  ref={filterRef}
                >
                  {/* Category Dropdown */}
                  <button
                    className={`flex flex-row items-center gap-2 bg-white border border-[#EAEAEA] rounded-md py-2 px-3 text-[#192024] relative transition-all ease-in-out duration-200 shadow-sm ${
                      openCategory ? "rounded-b-none" : ""
                    }`}
                    onClick={() => {
                      setOpenCategory(!openCategory);
                      setOpenSubCategory(false);
                    }}
                  >
                    <span
                      className={`text-xs leading-normal md:w-[200px] max-w-[160px] line-clamp-1 ${
                        selectedCategoryId ? "font-medium" : "text-[#6b7280]"
                      }`}
                    >
                      {selectedCategoryName}
                    </span>
                    <MdKeyboardArrowDown
                      className={`w-4 h-4 transition-transform ${
                        openCategory ? "rotate-180" : "rotate-0"
                      }`}
                    />
                    <div
                      className={`absolute top-full left-0 z-[1000] bg-white w-full overflow-y-auto overflow-x-hidden h-fit flex flex-col no_scrollbar  transition-all duration-200 linear shadow-md ${
                        openCategory
                          ? "max-h-[300px] md:max-h-[360px]"
                          : "max-h-0"
                      }`}
                    >
                      {categories?.map((cat, idx) => (
                        <div
                          key={`cat-${cat?._id}`}
                          className={`py-2 px-3 text-sm flex items-center justify-between cursor-pointer hover:bg-[#F7F7F7] ${
                            selectedCategoryId === cat?._id
                              ? "bg-[#F2F8FF]"
                              : ""
                          }`}
                          title={cat?.name}
                          onClick={() => {
                            setSelectedCategoryId(cat?._id);
                            fetchSubCategories(cat?._id);
                            setSelectedCategoryName(cat?.name);
                            localStorage.setItem(
                              "selectedCategory",
                              JSON.stringify({ id: cat?._id, name: cat?.name })
                            );
                            localStorage.removeItem("selectedSubCategory");
                            // clear subcategory
                            setSelectedSubCategoryId("");
                            setSelectedSubCategoryName("Sub-Category");
                            setOpenCategory(false);
                            setFilterOccurred(true);
                          }}
                        >
                          <span className="truncate pr-2">{cat?.name}</span>
                          {selectedCategoryId === cat?._id && (
                            <FaCheck className="w-3.5 h-3.5 text-[#0161ab]" />
                          )}
                        </div>
                      ))}
                    </div>
                  </button>

                  {/* Sub-Category Dropdown */}
                  <button
                    className={`flex flex-row items-center gap-2 ${
                      selectedCategoryId ? "bg-white" : "bg-[#F9FAFB]"
                    } border border-[#EAEAEA] rounded-md py-2 px-3 text-[#192024] relative transition-all ease-in-out duration-200 shadow-sm ${
                      openSubCategory ? "rounded-b-none" : ""
                    }`}
                    onClick={() => {
                      if (!selectedCategoryId) {
                        toast.warn("Kindly select a category first.");
                        return;
                      }
                      setOpenSubCategory(!openSubCategory);
                      setOpenCategory(false);
                    }}
                  >
                    <span
                      className={`text-xs leading-normal md:w-[200px] max-w-[160px] line-clamp-1 ${
                        selectedSubCategoryId ? "font-medium" : "text-[#6b7280]"
                      }`}
                    >
                      {selectedSubCategoryName}
                    </span>
                    <MdKeyboardArrowDown
                      className={`w-4 h-4 transition-transform ${
                        openSubCategory ? "rotate-180" : "rotate-0"
                      }`}
                    />
                    {selectedSubCategoryId && (
                      <span
                        className="ml-2  text-[#ab0101] underline cursor-pointer text-xl "
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedSubCategoryId("");
                          setSelectedSubCategoryName("Sub-Category");
                          try {
                            localStorage.removeItem("selectedSubCategory");
                          } catch (e) {}
                          setFilterOccurred(true);
                        }}
                      >
                        <CiCircleRemove />
                      </span>
                    )}
                    <div
                      className={`absolute top-full left-0 bg-white w-full overflow-y-auto overflow-x-hidden h-fit flex flex-col no_scrollbar z-[1000]  transition-all duration-200 linear  shadow-md ${
                        openSubCategory
                          ? "max-h-[300px] md:max-h-[360px]"
                          : "max-h-0"
                      }`}
                    >
                      {subCategories?.map((sub, idx) => (
                        <div
                          key={`sub-${sub?._id}`}
                          className={`py-2 px-3 text-sm flex items-center justify-between cursor-pointer hover:bg-[#F7F7F7] ${
                            selectedSubCategoryId === sub?._id
                              ? "bg-[#F2F8FF]"
                              : ""
                          }`}
                          title={sub?.name}
                          onClick={() => {
                            setSelectedSubCategoryId(sub?._id);
                            setSelectedSubCategoryName(sub?.name);

                            localStorage.setItem(
                              "selectedSubCategory",
                              JSON.stringify({
                                id: sub?._id,
                                name: sub?.name,
                              })
                            );

                            setOpenSubCategory(false);
                            setFilterOccurred(true);
                          }}
                        >
                          <span className="truncate pr-2">{sub?.name}</span>
                          {selectedSubCategoryId === sub?._id && (
                            <FaCheck className="w-3.5 h-3.5 text-[#0161ab]" />
                          )}
                        </div>
                      ))}
                    </div>
                  </button>
                </div>
                <div
                  className="md:hidden w-[32px] h-[32px] ml-[14px] flex justify-center text-white cursor-pointer rounded-sm items-center"
                  style={{
                    background:
                      "linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%)",
                  }}
                  onClick={() => setShowMap(!showMap)}
                >
                  <FaMapLocation />
                </div>

                <div className="hidden  md:flex gap-[8px] md:ml-auto">
                  <button
                    className={`border-[0.7px] border-[#EFEFEF] w-[46px] h-[46px] flex items-center justify-center transition-colors duration-200 ease-in-out ${
                      isList ? `bg-[#211F54]` : "bg-white"
                    } `}
                    onClick={() => setIsList(true)}
                  >
                    <FaListUl
                      className="w-3.5 h-3.5"
                      fill={isList ? "#ffffff" : "#858585"}
                    />
                  </button>

                  <button
                    className={`border-[0.7px] border-[#EFEFEF] w-[46px] h-[46px] flex items-center justify-center transition-colors duration-200 ease-in-out ${
                      !isList ? `bg-[#211F54]` : "bg-white"
                    }`}
                    onClick={() => setIsList(false)}
                  >
                    <LuLayoutGrid
                      className="w-3.5 h-3.5"
                      stroke={!isList ? "#ffffff" : "#858585"}
                    />
                  </button>
                </div>
              </div>
            </div>
          </section>

          {/* list of books */}
          {isList ? (
            <section className="px-2.5 pb-10 mt-5 flex flex-col lg:flex-row gap-5 md:pb-[70px]">
              {/* List View */}
              {booksLoading ? (
                <ListViewSkeleton />
              ) : (
                <div className="flex flex-col w-full lg:w-[65%] xl:w-[60%] gap-5 lg:gap-[30px] order-2 lg:order-1">
                  {bookList?.length > 0
                    ? bookList.map((item, idx) => (
                        <div
                          key={`book-list-${idx}`}
                          className="border border-[#EAEAEA] p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 h-fit cursor-pointer"
                          onClick={() =>
                            router.push(`/book-detail?id=${item._id}`)
                          }
                        >
                          {/* Images with action buttons */}
                          <div className="col-span-1 w-full aspect-[3/4] sm:aspect-[2/3] md:aspect-auto overflow-hidden bg-amber-100  rounded relative">
                            {/* Bookmark and Share Buttons (top-right corner) */}
                            <div className="absolute top-2 right-2 z-1 flex gap-2">
                              {
                                <button
                                  className={`p-2 rounded-full shadow-md ${
                                    item.hasBookmarked
                                      ? "bg-white text-red-500"
                                      : "global_linear_gradient text-white hover:bg-white"
                                  } transition-colors`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (!userData?._id) {
                                      router.push("/login");
                                      return;
                                    }
                                    if (
                                      item.hasBookmarked &&
                                      item.bookmarkDoc[0].itemId
                                    ) {
                                      deleteBookMarkItem(
                                        item.bookmarkDoc[0]._id
                                      );
                                    } else {
                                      globleBookmarkFunc(
                                        item.createdByDoc._id,
                                        () => bookTheItemMark(item._id)
                                      );
                                    }
                                  }}
                                >
                                  <FaHeart size={16} />
                                </button>
                              }
                              <button
                                className="p-2 rounded-full bg-white/80 global_linear_gradient text-white hover:bg-white transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setselectedId(item._id);
                                  document
                                    .getElementById("myShareModal")
                                    ?.classList.remove("hidden");
                                }}
                              >
                                <PiShareFatFill size={16} />
                              </button>
                            </div>

                            <CustomSlider
                              sliderSettings={settings}
                              className="h-full "
                            >
                              {item.images.map((src, i) => (
                                <div
                                  key={i}
                                  className="relative w-full h-full flex items-center  justify-center"
                                >
                                  <div className="w-full h-0 pb-[133.33%] relative">
                                    <img
                                      src={src || "/images/book_1.jpg"}
                                      alt={`book image ${i + 1}`}
                                      className="absolute inset-0 object-cover w-full h-full rounded"
                                      sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                                    />
                                  </div>
                                </div>
                              ))}
                            </CustomSlider>
                          </div>

                          {/* Details */}
                          <div className="col-span-1 sm:col-span-1 md:col-span-3 flex flex-col justify-between">
                            <div>
                              <h3 className="text-base md:text-xl font-semibold leading-tight line-clamp-2">
                                {item.title || "Untitled"}
                              </h3>
                              <p className="mt-1 text-sm md:text-base capitalize">
                                Category:{" "}
                                <span className="font-medium">
                                  {returnCategorySubCat(item)}
                                </span>
                              </p>
                              <ItemRespectiveDetails item={item} />
                              <div className="mt-2 flex flex-wrap items-center text-xs md:text-sm gap-2">
                                <span className="truncate max-w-[60%]">
                                  Seller:{" "}
                                  <span className="font-medium">
                                    {item.createdByDoc?.firstName}
                                  </span>
                                </span>
                                {
                                  <div className="flex items-center gap-1">
                                    {item?.reviewDoc[0]?.averageRating && (
                                      <span className="bg-[#14884C] text-white text-[10px] px-2 py-[2px] rounded-sm">
                                        {item.reviewDoc[0]?.averageRating}
                                      </span>
                                    )}
                                    <div className="flex">
                                      <div className="flex items-center">
                                        {item?.reviewDoc[0]?.averageRating
                                          ? Array.from({
                                              length:
                                                item?.reviewDoc[0]
                                                  ?.averageRating,
                                            }).map((item) => {
                                              return (
                                                <div>
                                                  <FaStar
                                                    size={12}
                                                    fill={"#14884C"}
                                                    className={
                                                      item?.reviewDoc[0]
                                                        ?.averageRating
                                                        ? "text-yellow-600"
                                                        : "text-gray-300"
                                                    }
                                                  />
                                                </div>
                                              );
                                            })
                                          : Array.from({
                                              length: 5,
                                            }).map((item) => {
                                              return (
                                                <div>
                                                  <FaStar
                                                    size={12}
                                                    fill={"gray"}
                                                    className={
                                                      item?.reviewDoc[0]
                                                        ?.averageRating
                                                        ? "text-yellow-600"
                                                        : "text-gray-300"
                                                    }
                                                  />
                                                </div>
                                              );
                                            })}
                                        {/* {item?.reviewDoc[0]?.averageRating ? "" : "(No Review)"} */}
                                      </div>
                                    </div>
                                  </div>
                                }
                              </div>
                              <p
                                className="mt-2 text-xs md:text-sm truncate"
                                title={
                                  item.address?.formatted_address ||
                                  item.address
                                }
                              >
                                Location:{" "}
                                {item.address?.formatted_address ||
                                  item.address}
                              </p>

                              <div className="text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]">
                                <span
                                  title={item?.address}
                                  className="line-clamp-1"
                                >
                                  Parish: {item?.address?.parish || "NA"}
                                </span>
                              </div>
                              <p className="mt-2 text-xs md:text-sm">
                                Date:{" "}
                                <span className="font-medium">
                                  {moment(item.createdAt).format("DD-MM-YYYY")}
                                </span>
                              </p>
                            </div>

                            {/* Price and Chat Button */}
                            <div className="mt-4 flex flex-wrap justify-between items-center gap-2">
                              <p className="text-base md:text-3xl font-bold truncate">
                                J$ {formatWithCommas(item.price)}
                              </p>
                              <button
                                className="py-2 px-4 text-xs md:text-sm font-semibold rounded-full text-white global_linear_gradient"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  let user = userDataFromLocal() || "{}";

                                  if (item.createdByDoc?._id === user._id) {
                                    toast.error("You cannot message yourself");
                                  } else {
                                    chatNavigationHandler(e, item._id);
                                  }
                                }}
                              >
                                Chat Now
                              </button>
                            </div>
                          </div>
                        </div>
                      ))
                    : !booksLoading && (
                        <div className="flex flex-col items-center justify-center space-y-5">
                          <Image
                            src={No_result_found_search_page}
                            alt="No Result Found"
                            width={300}
                            height={300}
                            objectFit="cover"
                          />
                          <div className="text-center space-y-2">
                            <h4 className="text-2xl font-medium">
                              No Results Found
                            </h4>
                            <p className="text-lg text-[#838282]">
                              Nothing on our list yet.
                              <br />
                              It's never too late to change it 😊
                            </p>
                          </div>
                          <button
                            className="mt-4 py-3 px-8 text-base font-medium text-white rounded-full global_linear_gradient"
                            onClick={() => router.push("/become-seller")}
                          >
                            Create A New Listing
                          </button>
                        </div>
                      )}

                  {/* Load More Skeletons for List View */}
                  {pagination.showLoader && <ListViewSkeleton count={4} />}
                </div>
              )}

              {/* Map */}
              <div
                className={`w-full md:block lg:w-[35%] xl:w-[40%] order-1 lg:order-2 ${
                  !showMap ? " hidden" : ""
                }`}
              >
                <MapView
                  width="100%"
                  height="500px"
                  data={bookList}
                  center={
                    userLocationData?.isParishSelection
                      ? null // Let MapView handle parish selection internally
                      : locationData?.latitude && locationData?.longitude
                      ? {
                          lat: locationData.latitude,
                          lng: locationData.longitude,
                        }
                      : null
                  }
                  searchCoordinates={searchCoordinates}
                  fullScreen={showMap}
                  onExitFullScreen={() => setShowMap(false)}
                />
              </div>
            </section>
          ) : (
            // Grid View
            <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[33px] mt-5 pb-10 md:pb-[70px]">
              {/* book card */}

              {booksLoading ? (
                <GridViewSkeletonContainer />
              ) : bookList?.length > 0 ? (
                bookList?.map((item, index) => (
                  <div
                    key={`grid-card-${index}`}
                    className="relative p-[13px] pb-4 border border-[#EAEAEA]"
                  >
                    <div className="absolute top-0 left-[-8] z-10">
                      {/* <div className="global_linear_gradient text-white text-xs font-bold px-3 py-1 rounded-tr-md shadow-md relative">
                        Featured
                        <div className="absolute -bottom-2 left-0 w-0 h-0 border-t-[8px] border-t-blue-700 border-l-[8px] border-l-transparent"></div>
                      </div> */}
                    </div>

                    <CustomSlider sliderSettings={settings} className={""}>
                      {
                        // [
                        //   item?.backCoverImage || item?.coverImage,
                        //   item?.frontCoverImage,
                        //   item?.middlePageImage,
                        //   item?.spineImage,
                        // ]
                        item?.images.map((src, imageIdx) => (
                          <div
                            key={`image-${imageIdx}`}
                            // className="relative  h-[218px] aspect-[3/4] bg-[#73877B] py-2.5 !flex justify-center items-center box-border"
                            className="relative  h-[218px] aspect-[3/4] bg-[#FFFBF6] py-2.5 !flex justify-center items-center box-border"
                          >
                            {userData?._id && (
                              <span
                                className={`p-2.5 rounded-full absolute top-12 right-2.5 z-1 bg-[#fff6] cursor-pointer ${bookListingScss.boxShadow}`}
                                onClick={(e) => {
                                  // e.preventDefault()
                                  // e.stopPropagation()
                                  if (userData?._id && !item.hasBookmarked) {
                                    globleBookmarkFunc(
                                      item?.createdBy?._id,
                                      () => bookTheItemMark(item._id)
                                    );
                                  } else {
                                    if (item.bookmarkDoc.length) {
                                      deleteBookMarkItem(
                                        item.bookmarkDoc[0]._id
                                      );
                                    }
                                  }
                                }}
                              >
                                <HiOutlineHeart
                                  stroke="#5F5F5F"
                                  fill={
                                    item.hasBookmarked ? "#e4000d" : "white"
                                  }
                                  width="43"
                                  height="43"
                                />
                                {/* <FaHeart fill='#e60513' /> */}
                              </span>
                            )}
                            <button
                              className={`p-2.5 rounded-full absolute top-2 right-2.5 z-1 bg-[#fff6] cursor-pointer ${bookListingScss.boxShadow}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                setselectedId(item._id);
                                document
                                  .getElementById("myShareModal")
                                  ?.classList.remove("hidden");
                              }}
                            >
                              <PiShareFatFill size={16} />
                            </button>
                            <div
                              className="relative  h-full flex items-center justify-center cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/book-detail?id=${item?._id}`);
                              }}
                            >
                              <img
                                // src={item?.coverImage || item?.backCoverImage}
                                src={src}
                                alt={`book image-${imageIdx}`}
                                // fill
                                className="object-cover w-[90%] h-[80%] rounded "
                              />
                            </div>
                          </div>
                        ))
                      }
                    </CustomSlider>

                    {/* content */}
                    <div className="mt-[11px]">
                      <div
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/book-detail?id=${item?._id}`);
                        }}
                      >
                        <h1
                          className="text-xl leading-normal font-semibold line-clamp-1 overflow-hidden"
                          title={item?.title}
                        >
                          {item?.title}
                        </h1>

                        <p className="mt-0.5 text-[15px] font-light leading-normal line-clamp-1">
                          Category: {returnCategorySubCat(item)}
                        </p>
                        <ItemRespectiveDetails item={item} />

                        <div className="text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]">
                          <span title={item?.address} className="line-clamp-1">
                            Location:{" "}
                            {item?.address.formatted_address || item?.address}
                          </span>
                        </div>
                        <div className="text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]">
                          <span title={item?.address} className="line-clamp-1">
                            Parish: {item?.address.parish || "NA"}
                          </span>
                        </div>

                        <div className="text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]">
                          Date:&nbsp;
                          <span className="font-medium">
                            {moment(item?.createdAt).format("DD-MM-YYYY")}
                          </span>
                        </div>

                        <div className="flex flex-wrap gap-2 mt-[11.3px] text-base leading-5 items-center">
                          <span className="line-clamp-1 max-w-[60%] md:max-w-[80%]">
                            Seller:{" "}
                            <span className="font-medium">
                              {item?.createdByDoc?.firstName +
                                " " +
                                item?.createdByDoc?.lastName}
                            </span>
                          </span>
                          {
                            <div className="flex items-center gap-1">
                              {item?.reviewDoc[0]?.averageRating && (
                                <span className="bg-[#14884C] text-white text-[10px] px-2 py-[2px] rounded-sm">
                                  {item.reviewDoc[0]?.averageRating}
                                </span>
                              )}
                              <div className="flex">
                                <div className="flex items-center">
                                  {item?.reviewDoc[0]?.averageRating
                                    ? Array.from({
                                        length:
                                          item?.reviewDoc[0]?.averageRating,
                                      }).map((item) => {
                                        return (
                                          <div>
                                            <FaStar
                                              size={12}
                                              fill={"#14884C"}
                                              className={
                                                item?.reviewDoc[0]
                                                  ?.averageRating
                                                  ? "text-yellow-600"
                                                  : "text-gray-300"
                                              }
                                            />
                                          </div>
                                        );
                                      })
                                    : Array.from({
                                        length: 5,
                                      }).map((item) => {
                                        return (
                                          <div>
                                            <FaStar
                                              size={12}
                                              fill={"gray"}
                                              className={
                                                item?.reviewDoc[0]
                                                  ?.averageRating
                                                  ? "text-yellow-600"
                                                  : "text-gray-300"
                                              }
                                            />
                                          </div>
                                        );
                                      })}
                                  {/* {
                                    Array.from({
                                      length: item?.reviewDoc[0]?.averageRating ,
                                    }).map((item) => {

                                      return <div><FaStar size={12} fill={item?.reviewDoc[0]?.averageRating?"#14884C":"gray"}  className={item?.reviewDoc[0]?.averageRating ? "text-yellow-600" : "text-gray-300"} /></div>
                                    })} */}
                                  {/* {item?.reviewDoc[0]?.averageRating ? "" : "(No Review)"} */}
                                </div>
                              </div>
                            </div>
                          }
                        </div>
                        <div className="mt-[11px] text-[15px] leading-normal flex gap-[11px] items-center"></div>
                      </div>
                      <div className="mt-4 flex gap-[34px] justify-between items-center">
                        <button
                          className="py-[9px] w-[120px] px-[20px] text-base font-semibold leading-6 text-center text-white rounded-full global_linear_gradient"
                          onClick={(e) => {
                            e.stopPropagation();

                            let userData = userDataFromLocal();

                            if (item?.createdByDoc?._id == userData?._id) {
                              toast.error(
                                "You can not send message to your self"
                              );
                            } else {
                              chatNavigationHandler(e, item?._id);
                            }
                          }}
                          // chatNavigationHandler(e, item?._id)}}
                        >
                          Chat Now
                        </button>
                        <p
                          title={item?.price}
                          className="text-xl font-semibold leading-normal line-clamp-1 truncate max-w-[180px] md:max-w-[120px] text-ellipsis"
                        >
                          J${item?.price}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                // No Data Found
                <div className="flex flex-col col-span-full justify-center items-center">
                  <div className="relative overflow-hidden w-[372px] h-[372px]">
                    <Image
                      src={No_result_found_search_page}
                      alt="No Result Found"
                      fill
                      objectFit="cover"
                      sizes="100w"
                    />
                  </div>

                  <div className="flex flex-col gap-2.5 mt-5 justify-center items-center">
                    <h4 className="text-2xl leading-normal font-medium">
                      No Result Found
                    </h4>
                    <p className="text-center text-lg leading-normal text-[#838282]">
                      Nothing on our list yet.
                      <br />
                      It's never too late to change it 😊
                    </p>
                  </div>

                  <button
                    type="button"
                    className="mt-[30px] global_linear_gradient text-white py-3 px-[30px] rounded-full text-[15px] font-medium -tracking-[0.3px] leading-[21px]"
                    onClick={() => router.push("/become-seller")}
                  >
                    Create A New Listing
                  </button>
                </div>
              )}

              {/* Load More Skeletons for Grid View */}
              {pagination.showLoader && <GridViewSkeleton count={8} />}
            </section>
          )}
          {pagination?.page < pagination?.totalPages && (
            <div className="flex justify-center my-8">
              <button
                className="py-4 px-8 text-xs md:text-sm font-semibold rounded-full text-white global_linear_gradient flex items-center justify-center min-w-[120px]"
                onClick={() => {
                  if (!pagination.showLoader) {
                    setPagination((prev) => ({
                      ...prev,
                      page: prev.page + 1,
                      showLoader: true,
                    }));
                  }
                }}
                disabled={pagination.showLoader}
              >
                {pagination.showLoader ? (
                  <span className="flex items-center gap-2">
                    <svg
                      className="animate-spin h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                      ></path>
                    </svg>
                    Loading...
                  </span>
                ) : (
                  "Load More"
                )}
              </button>
            </div>
          )}
          <ShareCard url={`/book-detail?id=${selectedId}`} />

          <div className="px-2.5">
            <BuyAndSellComponent />
          </div>
        </div>
      </section>
    </div>
  );
}

export default BookListing;
