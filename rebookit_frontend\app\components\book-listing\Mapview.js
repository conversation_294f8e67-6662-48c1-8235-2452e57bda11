"use client";

import React, { useEffect, useState, useRef } from "react";
import { AdvancedMarker, APIProvider, Map } from "@vis.gl/react-google-maps";
import "./mapview.scss";

import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { ItemKindEnum } from "@/app/config/constant";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { updateUserLocationData } from "@/app/redux/slices/storeSlice";
import {
  formatWithCommas,
  getCurrentLocationAndAddress,
  parseGeocodeResponse,
} from "../../utils/utils";
// const JAMAICA_CENTER = { lat: 17.9714, lng: -76.7931 };
const JAMAICA_ZOOM = 12;

const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY;

const Mapview = ({
  width,
  height,
  data,
  fullScreen = false,
  center,
  isSingleBookDetails,
  onExitFullScreen = () => {},
}) => {
  const userLocationData = useSelector(
    (state) => state.storeData.userLocationData
  );

  const [loading, setLoading] = useState(true);
  const [zoom, setZoom] = useState(JAMAICA_ZOOM);
  const [locationAllowed, setLocationAllowed] = useState(false);
  const [mapCenter, setMapCenter] = useState(
    userLocationData &&
      typeof userLocationData.latitude === "number" &&
      typeof userLocationData.longitude === "number"
      ? {
          lat: userLocationData.latitude,
          lng: userLocationData.longitude,
        }
      : null
  );
  const [mapKey, setMapKey] = useState(0);
  const router = useRouter();
  const mapRef = useRef(null);
  const dispatch = useDispatch();
  const locations = Array.isArray(data) ? data : [data];

  const toShoInlocation = (text) => {
    if (!text) return "";
    if (text.__t == ItemKindEnum.BookItem) {
      return "Book";
    } else if (text.__t == ItemKindEnum.EventItem) {
      return "Event";
    } else if (text.__t == ItemKindEnum.ExtracurricularActivityItem) {
      return "Activity";
    } else if (text.__t == ItemKindEnum.ScholarshipAwardItem) {
      return "Award";
    } else if (text.__t == ItemKindEnum.SchoolItem) {
      return "School";
    } else if (text.__t == ItemKindEnum.TutorItem) {
      return "Tutor";
    } else {
      return "";
    }
  };

  // Initial load: get current location and set in redux and local state
  useEffect(() => {
    let isMounted = true;
    const fetchAndSetLocation = async () => {
      try {
        // If we have valid coordinates in userLocationData, use them
        if (
          userLocationData &&
          typeof userLocationData.latitude === "number" &&
          typeof userLocationData.longitude === "number"
        ) {
          if (isMounted) {
            setMapCenter({
              lat: userLocationData.latitude,
              lng: userLocationData.longitude,
            });
            setZoom(12);
            setLocationAllowed(true);
            setLoading(false);
          }
          return;
        }

        // Only fetch location if we don't have coordinates and it's not parish selection
        if (!userLocationData?.isParishSelection) {
          const addressData = await getCurrentLocationAndAddress(
            "getfullAddress"
          );
          const parsedAddress = parseGeocodeResponse(addressData);

          if (parsedAddress && isMounted) {
            // Save in redux in the required format
            const locationData = {
              locality: parsedAddress.locality || "",
              latitude: parsedAddress.latitude || "",
              longitude: parsedAddress.longitude || "",
              currentLocation: true,
            };
            dispatch(updateUserLocationData(locationData));

            // Set map center for the map and marker
            setMapCenter({
              lat: parsedAddress.latitude,
              lng: parsedAddress.longitude,
            });
            setZoom(12);
            setLocationAllowed(true);
          } else if (isMounted) {
            setLocationAllowed(false);
          }
        } else {
          // For parish selection, we need to set a default center
          // Use Jamaica center as fallback
          setMapCenter({
            lat: 17.9714,
            lng: -76.7931,
          });
          setZoom(10);
          setLocationAllowed(false);
        }
      } catch (err) {
        // fallback to Jamaica center
        if (isMounted) {
          setMapCenter({
            lat: 17.9714,
            lng: -76.7931,
          });
          setZoom(10);
          setLocationAllowed(false);
        }
      } finally {
        if (isMounted) setLoading(false);
      }
    };

    fetchAndSetLocation();

    return () => {
      isMounted = false;
    };
  }, []);

  useEffect(() => {
    if (
      userLocationData &&
      typeof userLocationData.latitude === "number" &&
      typeof userLocationData.longitude === "number"
    ) {
      setMapCenter({
        lat: userLocationData.latitude,
        lng: userLocationData.longitude,
      });
      setLocationAllowed(true);
      setMapKey((prev) => prev + 1);
    } else if (userLocationData?.isParishSelection) {
      // For parish selection, use Jamaica center
      setMapCenter({
        lat: 17.9714,
        lng: -76.7931,
      });
      setZoom(10);
      setLocationAllowed(false);
      setMapKey((prev) => prev + 1);
    } else {
      setLocationAllowed(false);
    }
  }, [userLocationData]);

  // Handler to update mapCenter when the user moves the map
  const handleCenterChanged = () => {
    if (mapRef.current) {
      const center = mapRef.current.getCenter();
      if (center) {
        setMapCenter({
          lat: center.lat(),
          lng: center.lng(),
        });
      }
    }
  };

  if (loading) {
    return (
      <div className="loader-container">
        <Skeleton height={height} />
      </div>
    );
  }

  const mapStyle = fullScreen
    ? {
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: 9999,
        borderRadius: 0,
      }
    : {
        width,
        height,
        borderRadius: "15px",
      };

  console.log("===", isSingleBookDetails, center, mapCenter);
  return (
    <APIProvider apiKey={googleMapsApiKey}>
      <Map
        key={mapKey}
        defaultCenter={isSingleBookDetails ? center : mapCenter}
        defaultZoom={zoom}
        // Remove the center prop to allow the map to be movable
        style={mapStyle}
        mapId="DEMO_MAP_ID"
        gestureHandling={"greedy"}
        onLoad={(map) => {
          mapRef.current = map;
        }}
        onCenterChanged={handleCenterChanged}
        options={{
          draggable: true,
          scrollwheel: true,
          clickableIcons: true,
          zoomControl: true,
          disableDoubleClickZoom: false,
        }}
        disableDefaultUI={false}
      >
        {!isSingleBookDetails && locationAllowed && mapCenter && userLocationData?.currentLocation && (
          <AdvancedMarker
            key="user-location"
            position={mapCenter}
            title="Your Location"
          >
            <div className="custom-marker p-2 bg-blue-500 text-white rounded-full border-2 border-white shadow-lg">
              <span role="img" aria-label="You">
                📍
              </span>
            </div>
          </AdvancedMarker>
        )}

        {locations.map((list, index) => {
          const coords = list?.address?.geometry?.location?.coordinates;
          if (!coords || coords.length < 2) return null;

          return (
            <AdvancedMarker
              key={index}
              position={{
                lat: coords[1],
                lng: coords[0],
              }}
              title={list?.createdByDoc?.firstName}
            >
              <div
                className="custom-marker relative z-10 hover:z-[9999] p-3 bg-white rounded shadow-lg"
                onClick={() => router.push(`/book-detail?id=${list._id}`)}
              >
                <div className="w-fit mx-auto">
                  <img
                    className="border aspect-[3/4] mx-auto w-[50px]"
                    src={list.images[0]}
                    alt={list.title}
                  />
                </div>
                <div className="text-md mt-2">{toShoInlocation(list)}</div>
                <div className="extra-content text-md mt-2 max-w-[100px]">
                  {list.title}
                </div>
                <div className="text-md mt-2">
                  J${formatWithCommas(list?.price)}
                </div>
                <div className="triangle absolute bottom-[-15px] left-[30%]"></div>
              </div>
            </AdvancedMarker>
          );
        })}
      </Map>
      {fullScreen && (
        <button
          className="fixed top-[10px] right-[10px] z-[10000] h-[40px] w-[40px] border-0 rounded-none font-black text-white flex items-center justify-center shadow"
          style={{
            background:
              "linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%)",
          }}
          onClick={onExitFullScreen}
        >
          ✕
        </button>
      )}
    </APIProvider>
  );
};

export default Mapview;
