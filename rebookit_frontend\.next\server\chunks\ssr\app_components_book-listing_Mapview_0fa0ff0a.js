module.exports = {

"[project]/app/components/book-listing/Mapview.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/constant.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/slices/storeSlice.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
// const JAMAICA_CENTER = { lat: 17.9714, lng: -76.7931 };
const JAMAICA_ZOOM = 12;
const googleMapsApiKey = ("TURBOPACK compile-time value", "AIzaSyC3VuARMjQFSzXhddmwP7vqzlhGcNf7UPA");
const Mapview = ({ width, height, data, fullScreen = false, center, isSingleBookDetails, onExitFullScreen = ()=>{} })=>{
    const userLocationData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])((state)=>state.storeData.userLocationData);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [zoom, setZoom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(JAMAICA_ZOOM);
    const [locationAllowed, setLocationAllowed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [mapCenter, setMapCenter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(userLocationData && typeof userLocationData.latitude === "number" && typeof userLocationData.longitude === "number" ? {
        lat: userLocationData.latitude,
        lng: userLocationData.longitude
    } : null);
    const [mapKey, setMapKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const mapRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDispatch"])();
    const locations = Array.isArray(data) ? data : [
        data
    ];
    const toShoInlocation = (text)=>{
        if (!text) return "";
        if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemKindEnum"].BookItem) {
            return "Book";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemKindEnum"].EventItem) {
            return "Event";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemKindEnum"].ExtracurricularActivityItem) {
            return "Activity";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemKindEnum"].ScholarshipAwardItem) {
            return "Award";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemKindEnum"].SchoolItem) {
            return "School";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemKindEnum"].TutorItem) {
            return "Tutor";
        } else {
            return "";
        }
    };
    // Initial load: get current location and set in redux and local state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let isMounted = true;
        const fetchAndSetLocation = async ()=>{
            try {
                // If we have valid coordinates in userLocationData, use them
                if (userLocationData && typeof userLocationData.latitude === "number" && typeof userLocationData.longitude === "number") {
                    if (isMounted) {
                        setMapCenter({
                            lat: userLocationData.latitude,
                            lng: userLocationData.longitude
                        });
                        setZoom(12);
                        setLocationAllowed(true);
                        setLoading(false);
                    }
                    return;
                }
                // Only fetch location if we don't have coordinates and it's not parish selection
                if (!userLocationData?.isParishSelection) {
                    const addressData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCurrentLocationAndAddress"])("getfullAddress");
                    const parsedAddress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseGeocodeResponse"])(addressData);
                    if (parsedAddress && isMounted) {
                        // Save in redux in the required format
                        const locationData = {
                            locality: parsedAddress.locality || "",
                            latitude: parsedAddress.latitude || "",
                            longitude: parsedAddress.longitude || "",
                            currentLocation: true
                        };
                        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateUserLocationData"])(locationData));
                        // Set map center for the map and marker
                        setMapCenter({
                            lat: parsedAddress.latitude,
                            lng: parsedAddress.longitude
                        });
                        setZoom(12);
                        setLocationAllowed(true);
                    } else if (isMounted) {
                        setLocationAllowed(false);
                    }
                } else {
                    // For parish selection, we need to set a default center
                    // Use Jamaica center as fallback
                    setMapCenter({
                        lat: 17.9714,
                        lng: -76.7931
                    });
                    setZoom(10);
                    setLocationAllowed(false);
                }
            } catch (err) {
                // fallback to Jamaica center
                if (isMounted) {
                    setMapCenter({
                        lat: 17.9714,
                        lng: -76.7931
                    });
                    setZoom(10);
                    setLocationAllowed(false);
                }
            } finally{
                if (isMounted) setLoading(false);
            }
        };
        fetchAndSetLocation();
        return ()=>{
            isMounted = false;
        };
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (userLocationData && typeof userLocationData.latitude === "number" && typeof userLocationData.longitude === "number") {
            setMapCenter({
                lat: userLocationData.latitude,
                lng: userLocationData.longitude
            });
            setLocationAllowed(true);
            setMapKey((prev)=>prev + 1);
        } else if (userLocationData?.isParishSelection) {
            // For parish selection, use Jamaica center
            setMapCenter({
                lat: 17.9714,
                lng: -76.7931
            });
            setZoom(10);
            setLocationAllowed(false);
            setMapKey((prev)=>prev + 1);
        } else {
            setLocationAllowed(false);
        }
    }, [
        userLocationData
    ]);
    // Handler to update mapCenter when the user moves the map
    const handleCenterChanged = ()=>{
        if (mapRef.current) {
            const center = mapRef.current.getCenter();
            if (center) {
                setMapCenter({
                    lat: center.lat(),
                    lng: center.lng()
                });
            }
        }
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "loader-container",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                height: height
            }, void 0, false, {
                fileName: "[project]/app/components/book-listing/Mapview.js",
                lineNumber: 198,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/components/book-listing/Mapview.js",
            lineNumber: 197,
            columnNumber: 7
        }, this);
    }
    const mapStyle = fullScreen ? {
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: 9999,
        borderRadius: 0
    } : {
        width,
        height,
        borderRadius: "15px"
    };
    console.log("===", isSingleBookDetails, center, mapCenter);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["APIProvider"], {
        apiKey: googleMapsApiKey,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Map"], {
                defaultCenter: isSingleBookDetails ? center : mapCenter,
                defaultZoom: zoom,
                // Remove the center prop to allow the map to be movable
                style: mapStyle,
                mapId: "DEMO_MAP_ID",
                gestureHandling: "greedy",
                onLoad: (map)=>{
                    mapRef.current = map;
                },
                onCenterChanged: handleCenterChanged,
                options: {
                    draggable: true,
                    scrollwheel: true,
                    clickableIcons: true,
                    zoomControl: true,
                    disableDoubleClickZoom: false
                },
                disableDefaultUI: false,
                children: [
                    !isSingleBookDetails && locationAllowed && mapCenter && userLocationData?.currentLocation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AdvancedMarker"], {
                        position: mapCenter,
                        title: "Your Location",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "custom-marker p-2 bg-blue-500 text-white rounded-full border-2 border-white shadow-lg",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                role: "img",
                                "aria-label": "You",
                                children: "📍"
                            }, void 0, false, {
                                fileName: "[project]/app/components/book-listing/Mapview.js",
                                lineNumber: 250,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/components/book-listing/Mapview.js",
                            lineNumber: 249,
                            columnNumber: 13
                        }, this)
                    }, "user-location", false, {
                        fileName: "[project]/app/components/book-listing/Mapview.js",
                        lineNumber: 244,
                        columnNumber: 11
                    }, this),
                    locations.map((list, index)=>{
                        const coords = list?.address?.geometry?.location?.coordinates;
                        if (!coords || coords.length < 2) return null;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AdvancedMarker"], {
                            position: {
                                lat: coords[1],
                                lng: coords[0]
                            },
                            title: list?.createdByDoc?.firstName,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "custom-marker relative z-10 hover:z-[9999] p-3 bg-white rounded shadow-lg",
                                onClick: ()=>router.push(`/book-detail?id=${list._id}`),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-fit mx-auto",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            className: "border aspect-[3/4] mx-auto w-[50px]",
                                            src: list.images[0],
                                            alt: list.title
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/book-listing/Mapview.js",
                                            lineNumber: 275,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 274,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-md mt-2",
                                        children: toShoInlocation(list)
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 281,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "extra-content text-md mt-2 max-w-[100px]",
                                        children: list.title
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 282,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-md mt-2",
                                        children: [
                                            "J$",
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatWithCommas"])(list?.price)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 285,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "triangle absolute bottom-[-15px] left-[30%]"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 288,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/book-listing/Mapview.js",
                                lineNumber: 270,
                                columnNumber: 15
                            }, this)
                        }, index, false, {
                            fileName: "[project]/app/components/book-listing/Mapview.js",
                            lineNumber: 262,
                            columnNumber: 13
                        }, this);
                    })
                ]
            }, mapKey, true, {
                fileName: "[project]/app/components/book-listing/Mapview.js",
                lineNumber: 222,
                columnNumber: 7
            }, this),
            fullScreen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                className: "fixed top-[10px] right-[10px] z-[10000] h-[40px] w-[40px] border-0 rounded-none font-black text-white flex items-center justify-center shadow",
                style: {
                    background: "linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%)"
                },
                onClick: onExitFullScreen,
                children: "✕"
            }, void 0, false, {
                fileName: "[project]/app/components/book-listing/Mapview.js",
                lineNumber: 295,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/book-listing/Mapview.js",
        lineNumber: 221,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Mapview;
}}),
"[project]/app/components/book-listing/Mapview.js [app-ssr] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/components/book-listing/Mapview.js [app-ssr] (ecmascript)"));
}}),

};

//# sourceMappingURL=app_components_book-listing_Mapview_0fa0ff0a.js.map