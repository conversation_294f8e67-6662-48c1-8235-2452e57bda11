{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/public/landing/testbook.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 101, height: 131, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAYAAADaxo44AAAA00lEQVR42gHIADf/AMcpHfDQNB350C0g+c4gIvnRKCL5zCEf8gDIIRr2zzIi/8pCOP/IQT7/23gr/8xGI/YAviAc9sQvKP/JQC//zjgl/9RJIf/KOBz2AHJlTPaIbDv/h35Q/6OTX/+noXj/o6J49gBZfm72e31E/1B5bv9pkHf/O3Z7/yt+j/YAWoZx9pR6NP9Ig37/YJN4/0NgW/87dYH2AH+ETvaqgCz/dYVb/26KZf9DZGT/SHWA9gC+oV31pqJy/tWuZf7BsYL+wdDQ/sfS1PZCXm9QAGlHSAAAAABJRU5ErkJggg==\", blurWidth: 6, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,sHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkY,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/components/common/BookCard.js"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect, useState } from \"react\"\r\n// import featuredCss from \"./featurebook.module.scss\"\r\nimport testBook from \"@/public/landing/testbook.png\"\r\nimport Image from \"next/image\"\r\n\r\nimport { MdArrowOutward } from \"react-icons/md\";\r\nimport { LuHeart } from \"react-icons/lu\";\r\nimport Link from \"next/link\"\r\n\r\n\r\nexport default function FeaturedBooks() {\r\n    const [book,setBooks]=useState({name:\"Psycology of moeny\",image:testBook,price:500})\r\n\r\n    const tempData = [\r\n        {\r\n            image: testBook,\r\n            name: \"Level 3 Geography Grade 3 2022\",\r\n            price: \"3500\",\r\n            link: \"\"\r\n        }\r\n        // ,\r\n        // {\r\n        //     image: testBook,\r\n        //     name: \"Level 3 Geography Grade 3 2022\",\r\n        //     price: \"3500\",\r\n        //     link: \"\"\r\n        // },\r\n        // {\r\n        //     image: testBook,\r\n        //     name: \"Level 3 Geography Grade 3 2022\",\r\n        //     price: \"3500\",\r\n        //     link: \"\"\r\n        // },\r\n        // {\r\n        //     image: testBook,\r\n        //     name: \"Level 3 Geography Grade 3 2022\",\r\n        //     price: \"3500\",\r\n        //     link: \"\"\r\n        // },\r\n\r\n    ]\r\n\r\n    const [featuredBooks, setFeaturedBooks] = useState([])\r\n    useEffect(() => {\r\n        setFeaturedBooks(tempData)\r\n    }, [])\r\n\r\n\r\n    return (\r\n        <section className={` px-2.5 md:px-[50px] lg:px-[100px]`}>\r\n\r\n            <div className=\"container-wrapper\">\r\n                {/* <section className=\"md:flex md:justify-between md:items-start\"> */}\r\n                    {/* <header>\r\n                        <h2 className=\"text-[22px] leading-normal uppercase font-semibold md:text-[36px] lg:text-[48px]\">Featured Books</h2>\r\n                        <p className=\"mt-2.5 font-light text-xs leading-[18px] md:text-[18px] md:leading-[27px] md:w-9/12\">\r\n                            Explore stories, knowledge, and imagination with <strong>ReBookIt.</strong> Find academic essentials, timeless novels, and rare gems—all in one place.\r\n                        </p>\r\n                    </header> */}\r\n{/* \r\n                    <footer className=\"hidden md:flex justify-center my-5\">\r\n                        <Link href=\"/\" aria-label=\"View all book categories\">\r\n                            <svg width=\"178\" height=\"72\" viewBox=\"0 0 178 72\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                <path d=\"M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285\" stroke=\"#211F54\" strokeWidth=\"11.679\" />\r\n                                <path d=\"M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017\" stroke=\"#0161AB\" strokeWidth=\"11.679\" />\r\n                                <path d=\"M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285\" stroke=\"#EFDC2A\" strokeWidth=\"11.679\" />\r\n                                <path d=\"M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285\" stroke=\"#0161AB\" strokeWidth=\"11.679\" />\r\n                                <path d=\"M140.693 6L36.937 5.99999\" stroke=\"#FF0009\" strokeWidth=\"11.679\" />\r\n                                <path d=\"M140.693 65.457L36.937 65.457\" stroke=\"#4A8B40\" strokeWidth=\"11.679\" />\r\n                                <rect x=\"11.6016\" y=\"7.93848\" width=\"154.01\" height=\"54.6036\" rx=\"27.3018\" fill=\"white\" />\r\n                                <text x=\"50%\" y=\"50%\" dominantBaseline=\"middle\" textAnchor=\"middle\" fontSize=\"20\" fill=\"#211F54\" fontFamily=\"Poppins, sans-serif\">\r\n                                    View All\r\n                                </text>\r\n                            </svg>\r\n                        </Link>\r\n                    </footer>\r\n                </section> */}\r\n\r\n                <div className=\"my-5 grid grid-cols-2 lg:grid-cols-4 flex-wrap gap-2.5 md:my-14 md:gap-8 md:justify-between lg:mt-[30px]\">\r\n                    \r\n                        <article  className=\"border border-[#80808017] pt-1 px-[3px] pb-[15px] lg:border-0\">\r\n                            <div className=\"relative flex justify-center items-center py-1.5 bg-[#f1f1f1] lg:h-[224px] lg:py-2.5 lg:px-2.5\">\r\n                                <Image src={book.image} alt=\"book image\" objectFit=\"cover\" sizes=\"50w\" className=\"w-1/2\" />\r\n                                <div className=\"h-7 w-7 p-1 rounded-full border border-gray-200 bg-white flex justify-center items-center absolute top-1 right-1 cursor-pointer\">\r\n                                    <LuHeart size={17} color=\"#000\" className=\"opacity-90\" />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"pt-2 pl-2 pr-2.5 md:text-[20px] lg:pt-5 lg:pb-[28px]\">\r\n                                <p className=\"text-sm leading-[19px] font-semibold lg:text-[20px] lg:leading-[26px] lg:w-11/12\">{book.name}</p>\r\n\r\n                                <div className=\"flex justify-between items-center my-2.5\">\r\n                                    <p className=\"font-bold self-end\">\r\n                                        <span className=\"text-[#4D7906] lg:text-[20px] lg:leading-[22px]\">JMD </span>${book.price}\r\n                                    </p>\r\n                                    <div className=\"cursor-pointer global_linear_gradient p-2 rounded-full flex justify-center items-center lg:h-[46px] lg:w-[46px]\">\r\n                                        <MdArrowOutward className=\"h-[12px] w-[12px] lg:w-[16px] lg:h-[16px]\" color=\"#fff\" />\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </article>\r\n                    \r\n                </div>\r\n\r\n                {/* <footer className=\"flex md:hidden justify-center mb-8\">\r\n                    <nav aria-label=\"View all books\">\r\n                        <Link href=\"/\" aria-label=\"View all book categories\">\r\n                            <svg className=\"cursor-pointer\" width=\"101\" height=\"41\" viewBox=\"0 0 101 41\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                <path d=\"M97.5791 20.1641C97.5791 10.8877 89.7535 3.36779 80.1002 3.36779L21.4787 3.36779C11.8254 3.36779 3.99986 10.8877 3.99986 20.1641\" stroke=\"#211F54\" strokeWidth=\"6.59854\" />\r\n                                <path d=\"M97.5791 20.1641C97.5791 10.8877 89.7812 3.36779 80.162 3.36779L21.7476 3.36779\" stroke=\"#0161AB\" strokeWidth=\"6.59854\" />\r\n                                <path d=\"M4.00049 20.1641C4.00049 29.4404 11.826 36.9603 21.4794 36.9603H80.1009C89.7542 36.9603 97.5797 29.4404 97.5797 20.1641\" stroke=\"#EFDC2A\" strokeWidth=\"6.59854\" />\r\n                                <path d=\"M21.748 36.9603H80.1624C89.7816 36.9603 97.5795 29.4404 97.5795 20.1641\" stroke=\"#0161AB\" strokeWidth=\"6.59854\" />\r\n                                <path d=\"M80.1011 3.36719L21.4796 3.36718\" stroke=\"#FF0009\" strokeWidth=\"6.59854\" />\r\n                                <path d=\"M80.1011 36.9609L21.4796 36.9609\" stroke=\"#4A8B40\" strokeWidth=\"6.59854\" />\r\n                                <rect x=\"7.16504\" y=\"4.46094\" width=\"87.0145\" height=\"30.8506\" rx=\"15.4253\" fill=\"white\" />\r\n                                <text x=\"50%\" y=\"50%\" dominantBaseline=\"middle\" textAnchor=\"middle\" fontSize=\"12\" fontWeight=\"500\" fill=\"#211F54\" fontFamily=\"Poppins, sans-serif\">\r\n                                    View All\r\n                                </text>\r\n                            </svg>\r\n                        </Link>\r\n                    </nav>\r\n                </footer> */}\r\n            </div>\r\n        </section>\r\n\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA,sDAAsD;AACtD;AACA;AAEA;AACA;AACA;;;AATA;;;;;;;AAYe,SAAS;;IACpB,MAAM,CAAC,MAAK,SAAS,GAAC,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC,MAAK;QAAqB,OAAM,6RAAA,CAAA,UAAQ;QAAC,OAAM;IAAG;IAElF,MAAM,WAAW;QACb;YACI,OAAO,6RAAA,CAAA,UAAQ;YACf,MAAM;YACN,OAAO;YACP,MAAM;QACV;KAqBH;IAED,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,iBAAiB;QACrB;kCAAG,EAAE;IAGL,qBACI,6LAAC;QAAQ,WAAW,CAAC,kCAAkC,CAAC;kBAEpD,cAAA,6LAAC;YAAI,WAAU;sBA2BX,cAAA,6LAAC;gBAAI,WAAU;0BAEP,cAAA,6LAAC;oBAAS,WAAU;;sCAChB,6LAAC;4BAAI,WAAU;;8CACX,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,KAAK,KAAK;oCAAE,KAAI;oCAAa,WAAU;oCAAQ,OAAM;oCAAM,WAAU;;;;;;8CACjF,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC,iJAAA,CAAA,UAAO;wCAAC,MAAM;wCAAI,OAAM;wCAAO,WAAU;;;;;;;;;;;;;;;;;sCAIlD,6LAAC;4BAAI,WAAU;;8CACX,6LAAC;oCAAE,WAAU;8CAAoF,KAAK,IAAI;;;;;;8CAE1G,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAE,WAAU;;8DACT,6LAAC;oDAAK,WAAU;8DAAkD;;;;;;gDAAW;gDAAE,KAAK,KAAK;;;;;;;sDAE7F,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC,iJAAA,CAAA,iBAAc;gDAAC,WAAU;gDAA4C,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BpH;GApHwB;KAAA", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/book-detail/CircleRating.js"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst CircularRating = ({ rating = 4.5, maxRating = 5, size = 80 }) => {\r\n  const strokeWidth = 8;\r\n  const radius = (size - strokeWidth) / 2;\r\n  const circumference = 2 * Math.PI * radius;\r\n\r\n  // Calculate the progress as a percentage of circumference\r\n  const progress = (rating / maxRating) * circumference;\r\n  const progressPercent = (rating / maxRating) * 100;\r\n\r\n  return (\r\n    <div style={{ width: size, height: size }} className=\"relative\">\r\n      <svg\r\n        width={size}\r\n        height={size}\r\n        className=\"transform -rotate-90\"\r\n      >\r\n        {/* Background circle */}\r\n        <circle\r\n          stroke=\"#e5e7eb\" // Tailwind gray-200\r\n          fill=\"transparent\"\r\n          strokeWidth={strokeWidth}\r\n          r={radius}\r\n          cx={size / 2}\r\n          cy={size / 2}\r\n        />\r\n        {/* Progress circle */}\r\n        <circle\r\n          stroke=\"#fbbf24\" // Tailwind yellow-400\r\n          fill=\"transparent\"\r\n          strokeWidth={strokeWidth}\r\n          strokeLinecap=\"round\"\r\n          strokeDasharray={circumference}\r\n          strokeDashoffset={circumference - progress}\r\n          r={radius}\r\n          cx={size / 2}\r\n          cy={size / 2}\r\n        />\r\n      </svg>\r\n\r\n      {/* Rating text in center */}\r\n      <div className=\"absolute inset-0 flex items-center justify-center font-semibold text-xl text-yellow-600 select-none\">\r\n        {rating.toFixed(1)}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,iBAAiB,CAAC,EAAE,SAAS,GAAG,EAAE,YAAY,CAAC,EAAE,OAAO,EAAE,EAAE;IAChE,MAAM,cAAc;IACpB,MAAM,SAAS,CAAC,OAAO,WAAW,IAAI;IACtC,MAAM,gBAAgB,IAAI,KAAK,EAAE,GAAG;IAEpC,0DAA0D;IAC1D,MAAM,WAAW,AAAC,SAAS,YAAa;IACxC,MAAM,kBAAkB,AAAC,SAAS,YAAa;IAE/C,qBACE,6LAAC;QAAI,OAAO;YAAE,OAAO;YAAM,QAAQ;QAAK;QAAG,WAAU;;0BACnD,6LAAC;gBACC,OAAO;gBACP,QAAQ;gBACR,WAAU;;kCAGV,6LAAC;wBACC,QAAO,UAAU,oBAAoB;;wBACrC,MAAK;wBACL,aAAa;wBACb,GAAG;wBACH,IAAI,OAAO;wBACX,IAAI,OAAO;;;;;;kCAGb,6LAAC;wBACC,QAAO,UAAU,sBAAsB;;wBACvC,MAAK;wBACL,aAAa;wBACb,eAAc;wBACd,iBAAiB;wBACjB,kBAAkB,gBAAgB;wBAClC,GAAG;wBACH,IAAI,OAAO;wBACX,IAAI,OAAO;;;;;;;;;;;;0BAKf,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC;;;;;;;;;;;;AAIxB;KA7CM;uCA+CS", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/utils/axiosError.handler.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\r\nimport { getToken } from \"./utils\";\r\n// import history from \"./history\";\r\n\r\n\r\nexport const axiosErrorHandler = (error, action, checkUnauthorized = true) => {\r\n\r\n    const requestStatus = error?.request?.status;\r\n    const responseStatus = error?.response?.status;\r\n    const dataStatus = error?.data?.statusCode;\r\n    \r\n    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {\r\n        \r\n        // Clear local storage and redirect to /login\r\n        localStorage.clear();\r\n        toast.error(\r\n            error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n        );\r\n        window.location.href = \"/login\";\r\n    }\r\n    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {\r\n        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map(er => toast.error(er)) || error?.data?.error?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n            );\r\n    }\r\n    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {\r\n        // console.log(\"error log is\", error)\r\n        \r\n        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map(er => toast.error(er.message)) || error?.data?.message?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.message || error?.response?.data?.data || error?.data?.message,\r\n            );\r\n    }\r\n    if (\r\n        checkUnauthorized &&\r\n        (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)\r\n    ) {\r\n        if (getToken()) {\r\n            toast.error(error?.response?.data?.message);\r\n        }\r\n    }\r\n\r\n    if (action === \"uploadImage\") {\r\n        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {\r\n            if (getToken()) {\r\n                const message = error?.response?.data?.message;\r\n                message && toast.error(message);\r\n            } else history.push(\"/\");\r\n        }\r\n    }\r\n\r\n    if (error?.response) return error.response;\r\n    else if (error?.request) return error.request;\r\n    else return error?.message;\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIO,MAAM,oBAAoB,CAAC,OAAO,QAAQ,oBAAoB,IAAI;IAErE,MAAM,gBAAgB,OAAO,SAAS;IACtC,MAAM,iBAAiB,OAAO,UAAU;IACxC,MAAM,aAAa,OAAO,MAAM;IAEhC,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QAEvE,6CAA6C;QAC7C,aAAa,KAAK;QAClB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;QAEjF,OAAO,QAAQ,CAAC,IAAI,GAAG;IAC3B;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QACvE,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,UAAU,MAAM,OAAO,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,OAAO,MAAM,OAAO,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEzL,sJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;IAEzF;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,kBAAkB,KAAK;QAChG,qCAAqC;QAErC,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,WAAW,OAAO,QAAQ,OAAO,MAAM,SAAS,OAAO,UAAU,MAAM,QAAQ,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM,OAAO,MAAM,SAAS,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEtM,sJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,WAAW,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM;IAE1F;IACA,IACI,qBACA,CAAC,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,GAAG,GACxE;QACE,IAAI,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,KAAK;YACZ,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM;QACvC;IACJ;IAEA,IAAI,WAAW,eAAe;QAC1B,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;YACvE,IAAI,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,KAAK;gBACZ,MAAM,UAAU,OAAO,UAAU,MAAM;gBACvC,WAAW,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAC3B,OAAO,QAAQ,IAAI,CAAC;QACxB;IACJ;IAEA,IAAI,OAAO,UAAU,OAAO,MAAM,QAAQ;SACrC,IAAI,OAAO,SAAS,OAAO,MAAM,OAAO;SACxC,OAAO,OAAO;AACvB", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/services/axios.js"], "sourcesContent": ["const { default: axios } = require(\"axios\");\r\nconst { getToken } = require(\"../utils/utils\");\r\n\r\nconst BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst instance = axios.create({\r\n  baseURL: BASE_URL+\"/api\" ,\r\n\r\n  // Lets keep a check as default is 0 millisecond i.e. never\r\n  // Note: timeout is only for server response not network i.e. server reachability\r\n  timeout: 100000,\r\n\r\n  // Lets keep a check as default bytes- 2k\r\n  maxContentLength: 1000,\r\n\r\n  // Lets keep a check as default 5 seems high\r\n  maxRedirects: 2,\r\n});\r\n\r\ninstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = getToken();\r\n    console.log(\"token\", token)\r\n    \r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // Rate limiting: only fire a request every 2 sec from lodash.debounce\r\n    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });\r\n    return Promise.resolve(config);\r\n  },\r\n  function (error) {\r\n    const response = handleLogError(error); // log them\r\n\r\n    return Promise.reject(error);\r\n  }\r\n  // multiple options as to when and how to apply these interceptors\r\n  // , { synchronous: true, runWhen: onGetCall }\r\n);\r\n\r\n\r\nmodule.exports = instance;"], "names": [], "mappings": "AAGiB;AAHjB,MAAM,EAAE,SAAS,KAAK,EAAE;AACxB,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM;AAEN,MAAM,WAAW,MAAM,MAAM,CAAC;IAC5B,SAAS,WAAS;IAElB,2DAA2D;IAC3D,iFAAiF;IACjF,SAAS;IAET,yCAAyC;IACzC,kBAAkB;IAElB,4CAA4C;IAC5C,cAAc;AAChB;AAEA,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;IACC,MAAM,QAAQ;IACd,QAAQ,GAAG,CAAC,SAAS;IAErB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,sEAAsE;IACtE,8EAA8E;IAC9E,OAAO,QAAQ,OAAO,CAAC;AACzB,GACA,SAAU,KAAK;IACb,MAAM,WAAW,eAAe,QAAQ,WAAW;IAEnD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAMF,OAAO,OAAO,GAAG", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/services/profile.js"], "sourcesContent": ["import { USER_ROUTES } from \"../config/api\";\r\nimport { axiosErrorHandler } from \"../utils/axiosError.handler\";\r\nimport instance from \"./axios\";\r\n\r\n\r\nconst uri = {\r\n    login: \"/user/login\",\r\n    userInfo: \"/user\",\r\n    editProfile: \"/user/edit-profile\",\r\n    \r\n\r\n    item_by_name:`item/search`,\r\n    subscriptionPlan:\"/admin/subscription/plan\",\r\n    fetch_category:\"/master/category\",\r\n    fetchSubCategory:\"master/sub-category\",\r\n    fetchSubSubCategory:\"master/Sub-Sub-category\",\r\n    fetchSubSubSubCategory:\"master/sub-sub-sub-category\",\r\n    getPaymentIntent:\"/payment/payment-intent\",\r\n    verifyUserData:\"user/verify-otp\",\r\n    searchISBN:\"/books/isbn/{{ISBN}}\",\r\n    searchByName:\"/books/search?q={search}\",\r\n    \r\n    bookMarkItem_id:\"/item/bookmark\",\r\n    get_bookMark_by_user:\"/item/bookmarks\",\r\n    getItems: \"/item/search/current-user\",\r\n    getItemById:\"/item\",\r\n    createItem:\"/item\",\r\n    editItem:\"/item\",\r\n    deleteItemById:\"/item\",\r\n    itemBySeller:\"/item/user\",\r\n    addReview:\"/item/addReview\",\r\n    userReviews:\"/item/{:id}/reviews\",\r\n\r\n    uploadPhoto: \"admin/single-upload\",\r\n    history:\"/payment/history\",\r\n    supportRequest: \"/user/support-request\",\r\n    boostItem:\"/item/boost\",\r\n    getTestimonials: \"/user/testimonials\",\r\n    getFaq:\"/faqs/filter-search\",\r\n\r\n    // ad-management\r\n    getAdPlans : \"ad-management/getAdPlans\",\r\n    getAdPlanById : \"ad-management/getAdPlanById\",\r\n    \r\n};\r\nconst chat={\r\n    chat:\"/chat/all\",\r\n    chatById:\"/chat\"\r\n\r\n}\r\n// let data = await fetch(USER_ROUTES.LIST_ITEM, {\r\n//             headers: {\r\n//                 \"Content-Type\": \"application/json\",\r\n//                 \"Authorization\": `Bearer ${userToken}`\r\n//             }\r\n//         },)\r\n//         let response = await data.json()\r\n//         // console.log(\"data getAllBookOfUser\", await data.json())\r\n//         if (response.data) {\r\n//             setBookData(response.data)\r\n//         }\r\n\r\n\r\nexport const listItem = async (payload) => {\r\n    let response = await instance\r\n        .post(`${uri.createItem}`,payload)\r\n        .catch(axiosErrorHandler);\r\n    // console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const getAdPlans = async ({ type, position, page = 1, limit = 10 } = {}) => {\r\n    let response = await instance\r\n        .get(`${uri.getAdPlans}`, {\r\n            params: {\r\n                ...(type && { type }),\r\n                ...(position && { position }),\r\n                page,\r\n                limit\r\n            }\r\n        })\r\n        .catch(axiosErrorHandler);\r\n    return response;\r\n}\r\n\r\nexport const getAdPlanById = async (id) => {\r\n    let response = await instance\r\n        .get(`${uri.getAdPlanById}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    return response;\r\n}\r\n\r\nexport const editItem = async (payload,id) => {\r\n    let response = await instance\r\n        .put(`${uri.editItem}/${id}`,payload)\r\n        .catch(axiosErrorHandler);\r\n    // console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const addReviewForSeller = async (payload) => {\r\n    let response = await instance\r\n        .post(`${uri.addReview}`,payload)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\nexport const getMyBooks = async (data,queryString) => {\r\n    let response = await instance\r\n        .post(`${uri.getItems}`+queryString,data)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const deleteMyBooks = async (id, data) => {\r\n    let response = await instance\r\n        .put(`${uri.deleteItemById}/${id}`, data)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\nexport const getBooksById = async (id, userId) => {\r\n    let response = await instance\r\n        .get(`${uri.getItemById}/${id}`, {\r\n            params: { userId }\r\n        })\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\nexport const getItemBySeller = async (id) => {\r\n    \r\n    let response = await instance\r\n        .get(`${uri.itemBySeller}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const searchItemByName = async (data ,queryString) => {\r\n    let response = await instance\r\n        .post(`${uri.item_by_name}`+queryString,data)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const getsubscriptionPlans = async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.subscriptionPlan}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const getCategories= async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.fetch_category}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const getSubCategories= async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.fetchSubCategory}/${text}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const getSubSubCategories= async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.fetchSubSubCategory}/${text}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\nexport const getSubSubSubCategories= async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.fetchSubSubSubCategory}/${text}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const getAllChat= async (payloadData) => {\r\n    let response = await instance\r\n        .post(`${chat.chat}`,payloadData)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\n\r\nexport const getChatById= async (id) => {\r\n    let response = await instance\r\n        .get(`${chat.chatById}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\nexport const deleteChatById= async (id) => {\r\n    let response = await instance\r\n        .delete(`${chat.chatById}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    // console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const bookMarkItem=async(data)=>{\r\n    \r\n    let response = await instance\r\n        .post(`${uri.bookMarkItem_id}`,data)\r\n        .catch(axiosErrorHandler);\r\n    // console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const getReviewsOfUser=async(id)=>{\r\n    let response = await instance\r\n        .get(`${uri.userReviews}`.replace(\"{:id}\",id))\r\n        // .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\n\r\nexport const delete_bookMarkItem=async(id)=>{\r\n    let response = await instance\r\n        .delete(`${uri.bookMarkItem_id}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const get_bookMarkItems=async()=>{\r\n    let response = await instance\r\n        .get(`${uri.get_bookMark_by_user}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const getPaymentIntent =async(body)=>{\r\n    let response = await instance\r\n        .post(`${uri.getPaymentIntent}`,body)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const verifyUserData =async(body)=>{\r\n    let response = await instance\r\n        .post(`${uri.verifyUserData}`,body)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\nexport const searchISBN =async(ISBN)=>{\r\n    let response = await instance\r\n        .get(`${uri.searchISBN}`.replace(\"{{ISBN}}\",ISBN))\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\nexport const searchByName =async(search)=>{\r\n    let response = await instance\r\n        .get(`${uri.searchByName}`.replace(\"{search}\",search))\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\nexport const uploadPhotoSingle =async(data)=>{\r\n    let response = await instance\r\n        .post(`${uri.uploadPhoto}`,data)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\nexport const paymentHistory =async(data)=>{\r\n    let response = await instance\r\n        .get(`${uri.history}`,data)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\nexport const supportRequest =async(data)=>{\r\n    let response = await instance\r\n        .post(`${uri.supportRequest}`,data)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\n\r\nexport const boostItem =async(id)=>{\r\n    let response = await instance\r\n        .put(`${uri.boostItem}/`+id)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\nexport const getTestimonials =async(id)=>{\r\n    let response = await instance\r\n        .get(`${uri.getTestimonials}`)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\nexport const getFaq =async(data)=>{\r\n    let response = await instance\r\n        .post(`${uri.getFaq}`)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\n\r\n\r\n// let data = await fetch(USER_ROUTES.SEARCH_ITEM_BY_NAME.replace(\"itemName\", text), {\r\n//                 headers: {\r\n//                     \"Content-Type\": \"application/json\",\r\n//                     \"Authorization\": `Bearer ${userToken}`\r\n//                 },\r\n//             },)\r\n//             let response = await data.json()\r\n//             // console.log(\"data getAllBookOfUser\", await data.json())\r\n//             if (response.data) {\r\n//                 setBookData(response.data)\r\n//             }\r\n// export const login = async (payload, guestId) => {\r\n//     let response = await instance\r\n//         .post(`${uri.login}`,payload)\r\n//         .catch(axiosErrorHandler);\r\n//         console.log(\"login test response\",response)\r\n//         return response\r\n// };\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAGA,MAAM,MAAM;IACR,OAAO;IACP,UAAU;IACV,aAAa;IAGb,cAAa,CAAC,WAAW,CAAC;IAC1B,kBAAiB;IACjB,gBAAe;IACf,kBAAiB;IACjB,qBAAoB;IACpB,wBAAuB;IACvB,kBAAiB;IACjB,gBAAe;IACf,YAAW;IACX,cAAa;IAEb,iBAAgB;IAChB,sBAAqB;IACrB,UAAU;IACV,aAAY;IACZ,YAAW;IACX,UAAS;IACT,gBAAe;IACf,cAAa;IACb,WAAU;IACV,aAAY;IAEZ,aAAa;IACb,SAAQ;IACR,gBAAgB;IAChB,WAAU;IACV,iBAAiB;IACjB,QAAO;IAEP,gBAAgB;IAChB,YAAa;IACb,eAAgB;AAEpB;AACA,MAAM,OAAK;IACP,MAAK;IACL,UAAS;AAEb;AAcO,MAAM,WAAW,OAAO;IAC3B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,UAAU,EAAE,EAAC,SACzB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,+CAA+C;IAC/C,OAAO;AACX;AAEO,MAAM,aAAa,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC;IAC1E,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,UAAU,EAAE,EAAE;QACtB,QAAQ;YACJ,GAAI,QAAQ;gBAAE;YAAK,CAAC;YACpB,GAAI,YAAY;gBAAE;YAAS,CAAC;YAC5B;YACA;QACJ;IACJ,GACC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,gBAAgB,OAAO;IAChC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,aAAa,CAAC,CAAC,EAAE,IAAI,EAChC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,WAAW,OAAO,SAAQ;IACnC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAC,SAC5B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,+CAA+C;IAC/C,OAAO;AACX;AAEO,MAAM,qBAAqB,OAAO;IACrC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,SAAS,EAAE,EAAC,SACxB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AACO,MAAM,aAAa,OAAO,MAAK;IAClC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,QAAQ,EAAE,GAAC,aAAY,MACnC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,gBAAgB,OAAO,IAAI;IACpC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE,MACnC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,eAAe,OAAO,IAAI;IACnC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE;QAC7B,QAAQ;YAAE;QAAO;IACrB,GACC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AACO,MAAM,kBAAkB,OAAO;IAElC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,YAAY,CAAC,CAAC,EAAE,IAAI,EAC/B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,mBAAmB,OAAO,MAAM;IACzC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,YAAY,EAAE,GAAC,aAAY,MACvC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,uBAAuB,OAAO;IACvC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,gBAAgB,EAAE,EAC7B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,gBAAe,OAAO;IAC/B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,cAAc,EAAE,EAC3B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,mBAAkB,OAAO;IAClC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,gBAAgB,CAAC,CAAC,EAAE,MAAM,EACrC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,sBAAqB,OAAO;IACrC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,mBAAmB,CAAC,CAAC,EAAE,MAAM,EACxC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AACO,MAAM,yBAAwB,OAAO;IACxC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,sBAAsB,CAAC,CAAC,EAAE,MAAM,EAC3C,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,aAAY,OAAO;IAC5B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,EAAC,aACpB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAGO,MAAM,cAAa,OAAO;IAC7B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,IAAI,EAC5B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AACO,MAAM,iBAAgB,OAAO;IAChC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,MAAM,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,IAAI,EAC/B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,qDAAqD;IACrD,OAAO;AACX;AAEO,MAAM,eAAa,OAAM;IAE5B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,eAAe,EAAE,EAAC,MAC9B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,qDAAqD;IACrD,OAAO;AACX;AAEO,MAAM,mBAAiB,OAAM;IAChC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,WAAW,EAAE,CAAC,OAAO,CAAC,SAAQ;IAC1C,6BAA6B;IACjC,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAGO,MAAM,sBAAoB,OAAM;IACnC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,MAAM,CAAC,GAAG,IAAI,eAAe,CAAC,CAAC,EAAE,IAAI,EACrC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,oBAAkB;IAC3B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,oBAAoB,EAAE,EACjC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,mBAAkB,OAAM;IACjC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,gBAAgB,EAAE,EAAC,MAC/B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,iBAAgB,OAAM;IAC/B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,cAAc,EAAE,EAAC,MAC7B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAEO,MAAM,aAAY,OAAM;IAC3B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,UAAU,EAAE,CAAC,OAAO,CAAC,YAAW,OAC3C,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAEO,MAAM,eAAc,OAAM;IAC7B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,YAAY,EAAE,CAAC,OAAO,CAAC,YAAW,SAC7C,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAEO,MAAM,oBAAmB,OAAM;IAClC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,WAAW,EAAE,EAAC,MAC1B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAEO,MAAM,iBAAgB,OAAM;IAC/B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,OAAO,EAAE,EAAC,MACrB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,iBAAgB,OAAM;IAC/B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,cAAc,EAAE,EAAC,MAC7B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAGO,MAAM,YAAW,OAAM;IAC1B,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,GAAC,IACxB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,kBAAiB,OAAM;IAChC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,eAAe,EAAE,EAC5B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,SAAQ,OAAM;IACvB,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,MAAM,EAAE,EACpB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX,EAIA,sFAAsF;CACtF,6BAA6B;CAC7B,0DAA0D;CAC1D,6DAA6D;CAC7D,qBAAqB;CACrB,kBAAkB;CAClB,+CAA+C;CAC/C,yEAAyE;CACzE,mCAAmC;CACnC,6CAA6C;CAC7C,gBAAgB;CAChB,qDAAqD;CACrD,oCAAoC;CACpC,wCAAwC;CACxC,qCAAqC;CACrC,sDAAsD;CACtD,0BAA0B;CAC1B,KAAK", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/components/InitialAvatar/CreateInitialAvatar.js"], "sourcesContent": ["export function createInitialsAvatar(name, options = {}) {\r\n  // Default options\r\n  const {\r\n    size = 100,\r\n    bgColor = \"#cccccc\",\r\n    textColor = \"#000000\",\r\n    shape = \"circle\",\r\n  } = options;\r\n\r\n  // Get initials from name\r\n  const getInitials = (name) => {\r\n    if (!name || typeof name !== \"string\") return \"\";\r\n\r\n    const words = name\r\n      .trim()\r\n      .split(/\\s+/)\r\n      .filter((word) => word.length > 0);\r\n    if (words.length === 0) return \"\";\r\n\r\n    if (words.length === 1) {\r\n      return words[0].charAt(0).toUpperCase();\r\n    }\r\n    return `${words[0].charAt(0)}${words[words.length - 1].charAt(\r\n      0\r\n    )}`.toUpperCase();\r\n  };\r\n\r\n  const initials = getInitials(name);\r\n  const fontSize = size * 0.4;\r\n\r\n  // Create SVG based on shape\r\n  let svgContent;\r\n  if (shape === \"circle\") {\r\n    const radius = size / 2;\r\n    svgContent = `\r\n      <circle cx=\"${radius}\" cy=\"${radius}\" r=\"${radius}\" fill=\"${bgColor}\" />\r\n      <text x=\"50%\" y=\"50%\" dy=\"0.35em\" text-anchor=\"middle\" \r\n            font-family=\"Arial\" font-size=\"${fontSize}\" \r\n            fill=\"${textColor}\" font-weight=\"bold\">\r\n        ${initials}\r\n      </text>\r\n    `;\r\n  } else {\r\n    svgContent = `\r\n      <rect width=\"100%\" height=\"100%\" fill=\"${bgColor}\" />\r\n      <text x=\"50%\" y=\"50%\" dy=\"0.35em\" text-anchor=\"middle\" \r\n            font-family=\"Arial\" font-size=\"${fontSize}\" \r\n            fill=\"${textColor}\" font-weight=\"bold\">\r\n        ${initials}\r\n      </text>\r\n    `;\r\n  }\r\n\r\n  // Create full SVG\r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" \r\n         width=\"${size}\" \r\n         height=\"${size}\" \r\n         viewBox=\"0 0 ${size} ${size}\">\r\n      ${svgContent}\r\n    </svg>\r\n  `;\r\n\r\n  // Convert to data URL\r\n  return `data:image/svg+xml,${encodeURIComponent(svg)}`;\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,SAAS,qBAAqB,IAAI,EAAE,UAAU,CAAC,CAAC;IACrD,kBAAkB;IAClB,MAAM,EACJ,OAAO,GAAG,EACV,UAAU,SAAS,EACnB,YAAY,SAAS,EACrB,QAAQ,QAAQ,EACjB,GAAG;IAEJ,yBAAyB;IACzB,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;QAE9C,MAAM,QAAQ,KACX,IAAI,GACJ,KAAK,CAAC,OACN,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,GAAG;QAClC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;QACvC;QACA,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAC3D,IACC,CAAC,WAAW;IACjB;IAEA,MAAM,WAAW,YAAY;IAC7B,MAAM,WAAW,OAAO;IAExB,4BAA4B;IAC5B,IAAI;IACJ,IAAI,UAAU,UAAU;QACtB,MAAM,SAAS,OAAO;QACtB,aAAa,CAAC;kBACA,EAAE,OAAO,MAAM,EAAE,OAAO,KAAK,EAAE,OAAO,QAAQ,EAAE,QAAQ;;2CAE/B,EAAE,SAAS;kBACpC,EAAE,UAAU;QACtB,EAAE,SAAS;;IAEf,CAAC;IACH,OAAO;QACL,aAAa,CAAC;6CAC2B,EAAE,QAAQ;;2CAEZ,EAAE,SAAS;kBACpC,EAAE,UAAU;QACtB,EAAE,SAAS;;IAEf,CAAC;IACH;IAEA,kBAAkB;IAClB,MAAM,MAAM,CAAC;;gBAEC,EAAE,KAAK;iBACN,EAAE,KAAK;sBACF,EAAE,KAAK,CAAC,EAAE,KAAK;MAC/B,EAAE,WAAW;;EAEjB,CAAC;IAED,sBAAsB;IACtB,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,MAAM;AACxD", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/book-detail/AddRating.js"], "sourcesContent": ["\r\nimport { useRef, useState } from \"react\";\r\nimport { HiStar } from \"react-icons/hi\";\r\nimport { IoMdStar, IoIosStarOutline } from \"react-icons/io\";\r\nimport { MdStar, MdStarHalf, MdStarOutline } from \"react-icons/md\";\r\nimport { FaBold } from \"react-icons/fa6\";\r\nimport { FaItalic } from \"react-icons/fa6\";\r\nimport { IoLink } from \"react-icons/io5\";\r\nimport { addReviewForSeller } from \"../services/profile\";\r\nimport { toast } from \"react-toastify\";\r\nimport { userDataFromLocal } from \"../utils/utils\";\r\nimport { createInitialsAvatar } from \"../components/InitialAvatar/CreateInitialAvatar\";\r\n\r\nexport default function AddRating({ bookState, getReviews }) {\r\n  const [comment, setComment] = useState(\"\");\r\n  const [rating, setRating] = useState(0);\r\n  const [hoverRating, setHoverRating] = useState(0);\r\n  const [loadingCommentBtn, setLoadingCommentBtn] = useState(false);\r\n  const userData = userDataFromLocal();\r\n\r\n  const reviewAdd = async (e) => {\r\n    if (!bookState?.createdBy?._id) {\r\n      toast.error(\"User is not available to review\");\r\n      return;\r\n    }\r\n    if (comment.length < 1) {\r\n      toast.error(\"Please provide a comment\");\r\n      return;\r\n    }\r\n    if (rating === 0) {\r\n      toast.error(\"Please provide a rating\");\r\n      return;\r\n    }\r\n\r\n    setLoadingCommentBtn(true);\r\n    let payload = {\r\n      userId: bookState?.createdBy?._id,\r\n      comment: comment,\r\n      rating: rating,\r\n    };\r\n\r\n    try {\r\n      let response = await addReviewForSeller(payload);\r\n      if (response.status === 200) {\r\n        toast.success(\"Review Added Successfully\");\r\n        setComment(\"\");\r\n        setRating(0);\r\n        getReviews(bookState);\r\n      } else {\r\n        toast.error(response.data.message || \"Something went wrong\");\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"An error occurred while adding the review\");\r\n    } finally {\r\n      setLoadingCommentBtn(false);\r\n    }\r\n  };\r\n\r\n  // Create star rating component\r\n  const renderStars = () => {\r\n    return (\r\n      <div className=\"flex items-center\">\r\n        <span className=\"mr-3\">Rate Your Experience:</span>\r\n        <div className=\"flex items-center\">\r\n          {[1, 2, 3, 4, 5].map((star) => (\r\n            <button\r\n              key={star}\r\n              type=\"button\"\r\n              className=\"focus:outline-none\"\r\n              onClick={() => setRating(star)}\r\n              onMouseEnter={() => setHoverRating(star)}\r\n              onMouseLeave={() => setHoverRating(0)}\r\n              aria-label={`Rate ${star} star${star !== 1 ? \"s\" : \"\"}`}\r\n            >\r\n              {hoverRating >= star || (!hoverRating && rating >= star) ? (\r\n                <MdStar\r\n                  className=\"text-yellow-400 transition-all duration-150\"\r\n                  size={25}\r\n                />\r\n              ) : (\r\n                <MdStarOutline\r\n                  className=\"text-gray-300 hover:text-yellow-300 transition-all duration-150\"\r\n                  size={25}\r\n                />\r\n              )}\r\n            </button>\r\n          ))}\r\n        </div>\r\n        {/* <div className=\"ml-3 text-lg font-semibold\">\r\n          {rating > 0 && `${rating}/5`}\r\n        </div> */}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h1 className=\"md:text-[34px] font-bold md:text-3xl sm:text-[24px]\">\r\n        Add Review\r\n      </h1>\r\n      <div className=\"border rounded-lg p-3 border-[1.5px] border-[global_linear_gradiant] mt-3\">\r\n        <div className=\"flex items-center\">\r\n          <img\r\n            className=\"w-[50px] h-[50px] rounded-full\"\r\n            src={\r\n              userData?.profileImage ||\r\n              createInitialsAvatar(`${userData?.firstName}  ${userData?.lastName}`, {\r\n                                                      bgColor: \"#3f51b5\",\r\n                                                      textColor: \"#ffffff\",\r\n                                                    }) \r\n            }\r\n            alt=\"User profile\"\r\n          />\r\n          <span className=\"ml-2\">\r\n            {userData?.firstName}, {userData?.lastName}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Star rating component */}\r\n        <div className=\"flex items-center my-4\">{renderStars()}</div>\r\n\r\n        <textarea\r\n          placeholder=\"Enter your review here...\"\r\n          value={comment}\r\n          onChange={(e) => setComment(e.target.value)}\r\n          className=\"w-full min-h-[120px] p-3 border-none rounded-md mt-4 focus:outline-none\"\r\n        />\r\n\r\n        <div className=\"flex justify-end items-center mt-4\">\r\n          <button\r\n            className=\"flex gap-2 items-center py-3 px-6 rounded-full text-white justify-center global_linear_gradient  transition-colors duration-200 disabled:opacity-60 disabled:cursor-not-allowed\"\r\n            onClick={reviewAdd}\r\n            disabled={loadingCommentBtn}\r\n          >\r\n            {loadingCommentBtn ? (\r\n              <div className=\"flex items-center\">\r\n                <svg\r\n                  className=\"w-5 h-5 animate-spin text-white mr-2\"\r\n                  fill=\"none\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <circle\r\n                    className=\"opacity-25\"\r\n                    cx=\"12\"\r\n                    cy=\"12\"\r\n                    r=\"10\"\r\n                    stroke=\"currentColor\"\r\n                    strokeWidth=\"4\"\r\n                  ></circle>\r\n                  <path\r\n                    className=\"opacity-75\"\r\n                    fill=\"currentColor\"\r\n                    d=\"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\r\n                  ></path>\r\n                </svg>\r\n                Submitting...\r\n              </div>\r\n            ) : (\r\n              \"Submit Review\"\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAGA;AAIA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEe,SAAS,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE;;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD;IAEjC,MAAM,YAAY,OAAO;QACvB,IAAI,CAAC,WAAW,WAAW,KAAK;YAC9B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,IAAI,WAAW,GAAG;YAChB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qBAAqB;QACrB,IAAI,UAAU;YACZ,QAAQ,WAAW,WAAW;YAC9B,SAAS;YACT,QAAQ;QACV;QAEA,IAAI;YACF,IAAI,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;YACxC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW;gBACX,UAAU;gBACV,WAAW;YACb,OAAO;gBACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI;YACvC;QACF,EAAE,OAAO,OAAO;YACd,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,+BAA+B;IAC/B,MAAM,cAAc;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAK,WAAU;8BAAO;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;4BAEC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU;4BACzB,cAAc,IAAM,eAAe;4BACnC,cAAc,IAAM,eAAe;4BACnC,cAAY,CAAC,KAAK,EAAE,KAAK,KAAK,EAAE,SAAS,IAAI,MAAM,IAAI;sCAEtD,eAAe,QAAS,CAAC,eAAe,UAAU,qBACjD,6LAAC,iJAAA,CAAA,SAAM;gCACL,WAAU;gCACV,MAAM;;;;;qDAGR,6LAAC,iJAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,MAAM;;;;;;2BAhBL;;;;;;;;;;;;;;;;IA2BjB;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAG,WAAU;0BAAsD;;;;;;0BAGpE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,KACE,UAAU,gBACV,CAAA,GAAA,4JAAA,CAAA,uBAAoB,AAAD,EAAE,GAAG,UAAU,UAAU,EAAE,EAAE,UAAU,UAAU,EAAE;oCAC9B,SAAS;oCACT,WAAW;gCACb;gCAExC,KAAI;;;;;;0CAEN,6LAAC;gCAAK,WAAU;;oCACb,UAAU;oCAAU;oCAAG,UAAU;;;;;;;;;;;;;kCAKtC,6LAAC;wBAAI,WAAU;kCAA0B;;;;;;kCAEzC,6LAAC;wBACC,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wBAC1C,WAAU;;;;;;kCAGZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,UAAU;sCAET,kCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;;0DAER,6LAAC;gDACC,WAAU;gDACV,IAAG;gDACH,IAAG;gDACH,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,GAAE;;;;;;;;;;;;oCAEA;;;;;;uCAIR;;;;;;;;;;;;;;;;;;;;;;;AAOd;GAxJwB;KAAA", "debugId": null}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/components/book-listing/Mapview.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { AdvancedMarker, APIProvider, Map } from \"@vis.gl/react-google-maps\";\r\nimport \"./mapview.scss\";\r\n\r\nimport Skeleton from \"react-loading-skeleton\";\r\nimport \"react-loading-skeleton/dist/skeleton.css\";\r\nimport { ItemKindEnum } from \"@/app/config/constant\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { updateUserLocationData } from \"@/app/redux/slices/storeSlice\";\r\nimport {\r\n  formatWithCommas,\r\n  getCurrentLocationAndAddress,\r\n  parseGeocodeResponse,\r\n} from \"../../utils/utils\";\r\n// const JAMAICA_CENTER = { lat: 17.9714, lng: -76.7931 };\r\nconst JAMAICA_ZOOM = 12;\r\n\r\nconst googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY;\r\n\r\nconst Mapview = ({\r\n  width,\r\n  height,\r\n  data,\r\n  fullScreen = false,\r\n  center,\r\n  isSingleBookDetails,\r\n  onExitFullScreen = () => {},\r\n}) => {\r\n  const userLocationData = useSelector(\r\n    (state) => state.storeData.userLocationData\r\n  );\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [zoom, setZoom] = useState(JAMAICA_ZOOM);\r\n  const [locationAllowed, setLocationAllowed] = useState(false);\r\n  const [mapCenter, setMapCenter] = useState(\r\n    userLocationData &&\r\n      typeof userLocationData.latitude === \"number\" &&\r\n      typeof userLocationData.longitude === \"number\"\r\n      ? {\r\n          lat: userLocationData.latitude,\r\n          lng: userLocationData.longitude,\r\n        }\r\n      : null\r\n  );\r\n  const [mapKey, setMapKey] = useState(0);\r\n  const router = useRouter();\r\n  const mapRef = useRef(null);\r\n  const dispatch = useDispatch();\r\n  const locations = Array.isArray(data) ? data : [data];\r\n\r\n  const toShoInlocation = (text) => {\r\n    if (!text) return \"\";\r\n    if (text.__t == ItemKindEnum.BookItem) {\r\n      return \"Book\";\r\n    } else if (text.__t == ItemKindEnum.EventItem) {\r\n      return \"Event\";\r\n    } else if (text.__t == ItemKindEnum.ExtracurricularActivityItem) {\r\n      return \"Activity\";\r\n    } else if (text.__t == ItemKindEnum.ScholarshipAwardItem) {\r\n      return \"Award\";\r\n    } else if (text.__t == ItemKindEnum.SchoolItem) {\r\n      return \"School\";\r\n    } else if (text.__t == ItemKindEnum.TutorItem) {\r\n      return \"Tutor\";\r\n    } else {\r\n      return \"\";\r\n    }\r\n  };\r\n\r\n  // Initial load: get current location and set in redux and local state\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n    const fetchAndSetLocation = async () => {\r\n      try {\r\n        // If we have valid coordinates in userLocationData, use them\r\n        if (\r\n          userLocationData &&\r\n          typeof userLocationData.latitude === \"number\" &&\r\n          typeof userLocationData.longitude === \"number\"\r\n        ) {\r\n          if (isMounted) {\r\n            setMapCenter({\r\n              lat: userLocationData.latitude,\r\n              lng: userLocationData.longitude,\r\n            });\r\n            setZoom(12);\r\n            setLocationAllowed(true);\r\n            setLoading(false);\r\n          }\r\n          return;\r\n        }\r\n\r\n        // Only fetch location if we don't have coordinates and it's not parish selection\r\n        if (!userLocationData?.isParishSelection) {\r\n          const addressData = await getCurrentLocationAndAddress(\r\n            \"getfullAddress\"\r\n          );\r\n          const parsedAddress = parseGeocodeResponse(addressData);\r\n\r\n          if (parsedAddress && isMounted) {\r\n            // Save in redux in the required format\r\n            const locationData = {\r\n              locality: parsedAddress.locality || \"\",\r\n              latitude: parsedAddress.latitude || \"\",\r\n              longitude: parsedAddress.longitude || \"\",\r\n              currentLocation: true,\r\n            };\r\n            dispatch(updateUserLocationData(locationData));\r\n\r\n            // Set map center for the map and marker\r\n            setMapCenter({\r\n              lat: parsedAddress.latitude,\r\n              lng: parsedAddress.longitude,\r\n            });\r\n            setZoom(12);\r\n            setLocationAllowed(true);\r\n          } else if (isMounted) {\r\n            setLocationAllowed(false);\r\n          }\r\n        } else {\r\n          // For parish selection, we need to set a default center\r\n          // Use Jamaica center as fallback\r\n          setMapCenter({\r\n            lat: 17.9714,\r\n            lng: -76.7931,\r\n          });\r\n          setZoom(10);\r\n          setLocationAllowed(false);\r\n        }\r\n      } catch (err) {\r\n        // fallback to Jamaica center\r\n        if (isMounted) {\r\n          setMapCenter({\r\n            lat: 17.9714,\r\n            lng: -76.7931,\r\n          });\r\n          setZoom(10);\r\n          setLocationAllowed(false);\r\n        }\r\n      } finally {\r\n        if (isMounted) setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchAndSetLocation();\r\n\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (\r\n      userLocationData &&\r\n      typeof userLocationData.latitude === \"number\" &&\r\n      typeof userLocationData.longitude === \"number\"\r\n    ) {\r\n      setMapCenter({\r\n        lat: userLocationData.latitude,\r\n        lng: userLocationData.longitude,\r\n      });\r\n      setLocationAllowed(true);\r\n      setMapKey((prev) => prev + 1);\r\n    } else if (userLocationData?.isParishSelection) {\r\n      // For parish selection, use Jamaica center\r\n      setMapCenter({\r\n        lat: 17.9714,\r\n        lng: -76.7931,\r\n      });\r\n      setZoom(10);\r\n      setLocationAllowed(false);\r\n      setMapKey((prev) => prev + 1);\r\n    } else {\r\n      setLocationAllowed(false);\r\n    }\r\n  }, [userLocationData]);\r\n\r\n  // Handler to update mapCenter when the user moves the map\r\n  const handleCenterChanged = () => {\r\n    if (mapRef.current) {\r\n      const center = mapRef.current.getCenter();\r\n      if (center) {\r\n        setMapCenter({\r\n          lat: center.lat(),\r\n          lng: center.lng(),\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"loader-container\">\r\n        <Skeleton height={height} />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const mapStyle = fullScreen\r\n    ? {\r\n        position: \"fixed\",\r\n        top: 0,\r\n        left: 0,\r\n        width: \"100%\",\r\n        height: \"100%\",\r\n        zIndex: 9999,\r\n        borderRadius: 0,\r\n      }\r\n    : {\r\n        width,\r\n        height,\r\n        borderRadius: \"15px\",\r\n      };\r\n\r\n  console.log(\"===\", isSingleBookDetails, center, mapCenter);\r\n  return (\r\n    <APIProvider apiKey={googleMapsApiKey}>\r\n      <Map\r\n        key={mapKey}\r\n        defaultCenter={isSingleBookDetails ? center : mapCenter}\r\n        defaultZoom={zoom}\r\n        // Remove the center prop to allow the map to be movable\r\n        style={mapStyle}\r\n        mapId=\"DEMO_MAP_ID\"\r\n        gestureHandling={\"greedy\"}\r\n        onLoad={(map) => {\r\n          mapRef.current = map;\r\n        }}\r\n        onCenterChanged={handleCenterChanged}\r\n        options={{\r\n          draggable: true,\r\n          scrollwheel: true,\r\n          clickableIcons: true,\r\n          zoomControl: true,\r\n          disableDoubleClickZoom: false,\r\n        }}\r\n        disableDefaultUI={false}\r\n      >\r\n        {!isSingleBookDetails && locationAllowed && mapCenter && userLocationData?.currentLocation && (\r\n          <AdvancedMarker\r\n            key=\"user-location\"\r\n            position={mapCenter}\r\n            title=\"Your Location\"\r\n          >\r\n            <div className=\"custom-marker p-2 bg-blue-500 text-white rounded-full border-2 border-white shadow-lg\">\r\n              <span role=\"img\" aria-label=\"You\">\r\n                📍\r\n              </span>\r\n            </div>\r\n          </AdvancedMarker>\r\n        )}\r\n\r\n        {locations.map((list, index) => {\r\n          const coords = list?.address?.geometry?.location?.coordinates;\r\n          if (!coords || coords.length < 2) return null;\r\n\r\n          return (\r\n            <AdvancedMarker\r\n              key={index}\r\n              position={{\r\n                lat: coords[1],\r\n                lng: coords[0],\r\n              }}\r\n              title={list?.createdByDoc?.firstName}\r\n            >\r\n              <div\r\n                className=\"custom-marker relative z-10 hover:z-[9999] p-3 bg-white rounded shadow-lg\"\r\n                onClick={() => router.push(`/book-detail?id=${list._id}`)}\r\n              >\r\n                <div className=\"w-fit mx-auto\">\r\n                  <img\r\n                    className=\"border aspect-[3/4] mx-auto w-[50px]\"\r\n                    src={list.images[0]}\r\n                    alt={list.title}\r\n                  />\r\n                </div>\r\n                <div className=\"text-md mt-2\">{toShoInlocation(list)}</div>\r\n                <div className=\"extra-content text-md mt-2 max-w-[100px]\">\r\n                  {list.title}\r\n                </div>\r\n                <div className=\"text-md mt-2\">\r\n                  J${formatWithCommas(list?.price)}\r\n                </div>\r\n                <div className=\"triangle absolute bottom-[-15px] left-[30%]\"></div>\r\n              </div>\r\n            </AdvancedMarker>\r\n          );\r\n        })}\r\n      </Map>\r\n      {fullScreen && (\r\n        <button\r\n          className=\"fixed top-[10px] right-[10px] z-[10000] h-[40px] w-[40px] border-0 rounded-none font-black text-white flex items-center justify-center shadow\"\r\n          style={{\r\n            background:\r\n              \"linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%)\",\r\n          }}\r\n          onClick={onExitFullScreen}\r\n        >\r\n          ✕\r\n        </button>\r\n      )}\r\n    </APIProvider>\r\n  );\r\n};\r\n\r\nexport default Mapview;\r\n"], "names": [], "mappings": ";;;AAoByB;;AAlBzB;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAiBA,0DAA0D;AAC1D,MAAM,eAAe;AAErB,MAAM;AAEN,MAAM,UAAU,CAAC,EACf,KAAK,EACL,MAAM,EACN,IAAI,EACJ,aAAa,KAAK,EAClB,MAAM,EACN,mBAAmB,EACnB,mBAAmB,KAAO,CAAC,EAC5B;;IACC,MAAM,mBAAmB,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;iDACjC,CAAC,QAAU,MAAM,SAAS,CAAC,gBAAgB;;IAG7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC,oBACE,OAAO,iBAAiB,QAAQ,KAAK,YACrC,OAAO,iBAAiB,SAAS,KAAK,WACpC;QACE,KAAK,iBAAiB,QAAQ;QAC9B,KAAK,iBAAiB,SAAS;IACjC,IACA;IAEN,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,YAAY,MAAM,OAAO,CAAC,QAAQ,OAAO;QAAC;KAAK;IAErD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,QAAQ,EAAE;YACrC,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,SAAS,EAAE;YAC7C,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,2BAA2B,EAAE;YAC/D,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,oBAAoB,EAAE;YACxD,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,UAAU,EAAE;YAC9C,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,SAAS,EAAE;YAC7C,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,YAAY;YAChB,MAAM;yDAAsB;oBAC1B,IAAI;wBACF,6DAA6D;wBAC7D,IACE,oBACA,OAAO,iBAAiB,QAAQ,KAAK,YACrC,OAAO,iBAAiB,SAAS,KAAK,UACtC;4BACA,IAAI,WAAW;gCACb,aAAa;oCACX,KAAK,iBAAiB,QAAQ;oCAC9B,KAAK,iBAAiB,SAAS;gCACjC;gCACA,QAAQ;gCACR,mBAAmB;gCACnB,WAAW;4BACb;4BACA;wBACF;wBAEA,iFAAiF;wBACjF,IAAI,CAAC,kBAAkB,mBAAmB;4BACxC,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,+BAA4B,AAAD,EACnD;4BAEF,MAAM,gBAAgB,CAAA,GAAA,wHAAA,CAAA,uBAAoB,AAAD,EAAE;4BAE3C,IAAI,iBAAiB,WAAW;gCAC9B,uCAAuC;gCACvC,MAAM,eAAe;oCACnB,UAAU,cAAc,QAAQ,IAAI;oCACpC,UAAU,cAAc,QAAQ,IAAI;oCACpC,WAAW,cAAc,SAAS,IAAI;oCACtC,iBAAiB;gCACnB;gCACA,SAAS,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE;gCAEhC,wCAAwC;gCACxC,aAAa;oCACX,KAAK,cAAc,QAAQ;oCAC3B,KAAK,cAAc,SAAS;gCAC9B;gCACA,QAAQ;gCACR,mBAAmB;4BACrB,OAAO,IAAI,WAAW;gCACpB,mBAAmB;4BACrB;wBACF,OAAO;4BACL,wDAAwD;4BACxD,iCAAiC;4BACjC,aAAa;gCACX,KAAK;gCACL,KAAK,CAAC;4BACR;4BACA,QAAQ;4BACR,mBAAmB;wBACrB;oBACF,EAAE,OAAO,KAAK;wBACZ,6BAA6B;wBAC7B,IAAI,WAAW;4BACb,aAAa;gCACX,KAAK;gCACL,KAAK,CAAC;4BACR;4BACA,QAAQ;4BACR,mBAAmB;wBACrB;oBACF,SAAU;wBACR,IAAI,WAAW,WAAW;oBAC5B;gBACF;;YAEA;YAEA;qCAAO;oBACL,YAAY;gBACd;;QACF;4BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IACE,oBACA,OAAO,iBAAiB,QAAQ,KAAK,YACrC,OAAO,iBAAiB,SAAS,KAAK,UACtC;gBACA,aAAa;oBACX,KAAK,iBAAiB,QAAQ;oBAC9B,KAAK,iBAAiB,SAAS;gBACjC;gBACA,mBAAmB;gBACnB;yCAAU,CAAC,OAAS,OAAO;;YAC7B,OAAO,IAAI,kBAAkB,mBAAmB;gBAC9C,2CAA2C;gBAC3C,aAAa;oBACX,KAAK;oBACL,KAAK,CAAC;gBACR;gBACA,QAAQ;gBACR,mBAAmB;gBACnB;yCAAU,CAAC,OAAS,OAAO;;YAC7B,OAAO;gBACL,mBAAmB;YACrB;QACF;4BAAG;QAAC;KAAiB;IAErB,0DAA0D;IAC1D,MAAM,sBAAsB;QAC1B,IAAI,OAAO,OAAO,EAAE;YAClB,MAAM,SAAS,OAAO,OAAO,CAAC,SAAS;YACvC,IAAI,QAAQ;gBACV,aAAa;oBACX,KAAK,OAAO,GAAG;oBACf,KAAK,OAAO,GAAG;gBACjB;YACF;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;gBAAC,QAAQ;;;;;;;;;;;IAGxB;IAEA,MAAM,WAAW,aACb;QACE,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB,IACA;QACE;QACA;QACA,cAAc;IAChB;IAEJ,QAAQ,GAAG,CAAC,OAAO,qBAAqB,QAAQ;IAChD,qBACE,6LAAC,qLAAA,CAAA,cAAW;QAAC,QAAQ;;0BACnB,6LAAC,qLAAA,CAAA,MAAG;gBAEF,eAAe,sBAAsB,SAAS;gBAC9C,aAAa;gBACb,wDAAwD;gBACxD,OAAO;gBACP,OAAM;gBACN,iBAAiB;gBACjB,QAAQ,CAAC;oBACP,OAAO,OAAO,GAAG;gBACnB;gBACA,iBAAiB;gBACjB,SAAS;oBACP,WAAW;oBACX,aAAa;oBACb,gBAAgB;oBAChB,aAAa;oBACb,wBAAwB;gBAC1B;gBACA,kBAAkB;;oBAEjB,CAAC,uBAAuB,mBAAmB,aAAa,kBAAkB,iCACzE,6LAAC,qLAAA,CAAA,iBAAc;wBAEb,UAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,MAAK;gCAAM,cAAW;0CAAM;;;;;;;;;;;uBALhC;;;;;oBAYP,UAAU,GAAG,CAAC,CAAC,MAAM;wBACpB,MAAM,SAAS,MAAM,SAAS,UAAU,UAAU;wBAClD,IAAI,CAAC,UAAU,OAAO,MAAM,GAAG,GAAG,OAAO;wBAEzC,qBACE,6LAAC,qLAAA,CAAA,iBAAc;4BAEb,UAAU;gCACR,KAAK,MAAM,CAAC,EAAE;gCACd,KAAK,MAAM,CAAC,EAAE;4BAChB;4BACA,OAAO,MAAM,cAAc;sCAE3B,cAAA,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,GAAG,EAAE;;kDAExD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,KAAK,KAAK,MAAM,CAAC,EAAE;4CACnB,KAAK,KAAK,KAAK;;;;;;;;;;;kDAGnB,6LAAC;wCAAI,WAAU;kDAAgB,gBAAgB;;;;;;kDAC/C,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAI,WAAU;;4CAAe;4CACzB,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;;;;;;;;;;;;2BAzBZ;;;;;oBA6BX;;eArEK;;;;;YAuEN,4BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YACE;gBACJ;gBACA,SAAS;0BACV;;;;;;;;;;;;AAMT;GA7RM;;QASqB,4JAAA,CAAA,cAAW;QAkBrB,qIAAA,CAAA,YAAS;QAEP,4JAAA,CAAA,cAAW;;;KA7BxB;uCA+RS", "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/book-detail/ItemRespectiveDetails.js"], "sourcesContent": ["import React from \"react\";\r\nimport {ItemKindEnum, itemToKind, labelItemToKind} from \"../config/constant\";\r\nimport moment from \"moment\";\r\n\r\nexport default function ItemRespectiveDetails({bookState}) {\r\n\r\n  const attachXYZ=(string)=>{\r\n      if(string?.includes(\"http\")){\r\n        return string\r\n      }else{\r\n        return \"https://\"+string\r\n      }\r\n  }\r\n  if (bookState.__t == ItemKindEnum.BookItem) {\r\n    return (\r\n      <div>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px] truncate\">\r\n          Author:{\" \"}\r\n          <span className=\"font-medium\">\r\n            {bookState?.authors?.map((a) => a).join(\", \")}\r\n          </span>\r\n        </p>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px] truncate\">\r\n          ISBN: <span className=\"font-medium\">{bookState?.isbn_number}</span>\r\n        </p>\r\n        <p className=\"text-xs mb-2 text-[16px] md:text-[21px] leading-[27px] truncate\">\r\n          Condition: <span className=\"font-medium\">{bookState?.condition}</span>\r\n        </p>\r\n      </div>\r\n    );\r\n  } else if (bookState.__t == ItemKindEnum.TutorItem) {\r\n    return (\r\n      <div>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.TutorItem.experience]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.experience}</span>\r\n        </p>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.TutorItem.highestQualification]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.highestQualification}</span>\r\n        </p>\r\n        <p className=\"text-xs  text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.TutorItem.targetClasses]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.targetClasses}</span>\r\n        </p>\r\n        {/* <p className=\"text-xs mb-2 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.TutorItem.website]}:{\" \"}\r\n          <span className=\"font-medium text-blue-400\"><a href={attachXYZ(bookState?.website)} target=\"_blank\">{bookState?.website}</a></span>\r\n        </p> */}\r\n      </div>\r\n    );\r\n  } else if (bookState.__t == ItemKindEnum.EventItem) {\r\n    return (\r\n      <div>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.EventItem.eventStartDate]}:{\" \"}\r\n          <span className=\"font-medium\">\r\n            {moment(bookState?.eventStartDate).format(\"DD-MMM-YYYY HH:MM\")}\r\n          </span>\r\n        </p>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.EventItem.eventEndDate]}:{\" \"}\r\n          <span className=\"font-medium\">\r\n            {moment(bookState?.eventEndDate).format(\"DD-MMM-YYYY HH:MM\")}\r\n          </span>\r\n        </p>\r\n        <p className=\"text-xs  text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.EventItem.eventMode]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.eventMode}</span>\r\n        </p>\r\n        {/* <p className=\"text-xs mb-2 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.EventItem.website]}:{\" \"}\r\n          <span className=\"font-medium \"><a href={attachXYZ(bookState?.website)} target=\"_blank\">{bookState?.website}</a></span>\r\n        </p> */}\r\n      </div>\r\n    );\r\n  } else if (bookState.__t == ItemKindEnum.SchoolItem) {\r\n    return (\r\n      <div>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.SchoolItem.classesOffered]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.classesOffered}</span>\r\n        </p>\r\n        <p className=\"text-xs text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.SchoolItem.schoolType]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.schoolType}</span>\r\n        </p>\r\n        {/* <p className=\"text-xs mb-2 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.SchoolItem.website]}:{\" \"}\r\n          <span className=\"font-medium\"><a href={attachXYZ(bookState?.website)} target=\"_blank\">{bookState?.website}</a></span>\r\n        </p> */}\r\n      </div>\r\n    );\r\n  } else if (bookState.__t == ItemKindEnum.ScholarshipAwardItem) {\r\n    return (\r\n      <div>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.ScholarshipAwardItem.eligibilityCriteria]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.eligibilityCriteria}</span>\r\n        </p>\r\n        <p className=\"text-xs  text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.ScholarshipAwardItem.scholarshipType]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.scholarshipType}</span>\r\n        </p>\r\n        {/* <p className=\"text-xs  mb-2 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.ScholarshipAwardItem.website]}:{\" \"}\r\n          <span className=\"font-medium\"><a href={attachXYZ(bookState?.website)} target=\"_blank\">{bookState?.website}</a></span>\r\n        </p> */}\r\n      </div>\r\n    );\r\n  } else if (bookState.__t == ItemKindEnum.ExtracurricularActivityItem) {\r\n    return (\r\n      <div>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.ExtracurricularActivityItem.activityType]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.activityType}</span>\r\n        </p>\r\n        <p className=\"text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.ExtracurricularActivityItem.frequency]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.frequency}</span>\r\n        </p>\r\n        <p className=\"text-xs text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.ExtracurricularActivityItem.targetStudents]}:{\" \"}\r\n          <span className=\"font-medium\">{bookState?.targetStudents}</span>\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS,sBAAsB,EAAC,SAAS,EAAC;IAEvD,MAAM,YAAU,CAAC;QACb,IAAG,QAAQ,SAAS,SAAQ;YAC1B,OAAO;QACT,OAAK;YACH,OAAO,aAAW;QACpB;IACJ;IACA,IAAI,UAAU,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,QAAQ,EAAE;QAC1C,qBACE,6LAAC;;8BACC,6LAAC;oBAAE,WAAU;;wBAAkE;wBACrE;sCACR,6LAAC;4BAAK,WAAU;sCACb,WAAW,SAAS,IAAI,CAAC,IAAM,GAAG,KAAK;;;;;;;;;;;;8BAG5C,6LAAC;oBAAE,WAAU;;wBAAkE;sCACvE,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;8BAElD,6LAAC;oBAAE,WAAU;;wBAAkE;sCAClE,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;;;;;;;IAI7D,OAAO,IAAI,UAAU,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,SAAS,EAAE;QAClD,qBACE,6LAAC;;8BACC,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,UAAU;yBAAC;wBAAC;wBAAE;sCAC1C,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;8BAE5C,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,oBAAoB;yBAAC;wBAAC;wBAAE;sCACpD,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;8BAE5C,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,aAAa;yBAAC;wBAAC;wBAAE;sCAC7C,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;;;;;;;IAQlD,OAAO,IAAI,UAAU,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,SAAS,EAAE;QAClD,qBACE,6LAAC;;8BACC,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,cAAc;yBAAC;wBAAC;wBAAE;sCAC9C,6LAAC;4BAAK,WAAU;sCACb,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,WAAW,gBAAgB,MAAM,CAAC;;;;;;;;;;;;8BAG9C,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,YAAY;yBAAC;wBAAC;wBAAE;sCAC5C,6LAAC;4BAAK,WAAU;sCACb,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,WAAW,cAAc,MAAM,CAAC;;;;;;;;;;;;8BAG5C,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,SAAS;yBAAC;wBAAC;wBAAE;sCACzC,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;;;;;;;IAQlD,OAAO,IAAI,UAAU,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,UAAU,EAAE;QACnD,qBACE,6LAAC;;8BACC,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,UAAU,CAAC,cAAc;yBAAC;wBAAC;wBAAE;sCAC/C,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;8BAE5C,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,UAAU,CAAC,UAAU;yBAAC;wBAAC;wBAAE;sCAC3C,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;;;;;;;IAQlD,OAAO,IAAI,UAAU,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,oBAAoB,EAAE;QAC7D,qBACE,6LAAC;;8BACC,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,mBAAmB;yBAAC;wBAAC;wBAAE;sCAC9D,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;8BAE5C,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,eAAe;yBAAC;wBAAC;wBAAE;sCAC1D,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;;;;;;;IAQlD,OAAO,IAAI,UAAU,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,2BAA2B,EAAE;QACpE,qBACE,6LAAC;;8BACC,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,2BAA2B,CAAC,YAAY;yBAAC;wBAAC;wBAAE;sCAC9D,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;8BAE5C,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,2BAA2B,CAAC,SAAS;yBAAC;wBAAC;wBAAE;sCAC3D,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;8BAE5C,6LAAC;oBAAE,WAAU;;wBACV;4BAAC,4HAAA,CAAA,kBAAe,CAAC,2BAA2B,CAAC,cAAc;yBAAC;wBAAC;wBAAE;sCAChE,6LAAC;4BAAK,WAAU;sCAAe,WAAW;;;;;;;;;;;;;;;;;;IAIlD;AACF;KA5HwB", "debugId": null}}, {"offset": {"line": 1808, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/book-detail/bookDetailComponent.js"], "sourcesContent": ["\"use client\";\r\n\"use strict\";\r\nimport dynamic from \"next/dynamic\";\r\nimport Image from \"next/image\";\r\nimport React, { useRef, useState } from \"react\";\r\nimport \"./bookDetailComponent.scss\";\r\nimport { HiStar } from \"react-icons/hi\";\r\nimport { IoMdStar } from \"react-icons/io\";\r\nimport { GoBookmarkFill } from \"react-icons/go\";\r\nimport { useEffect } from \"react\";\r\nimport { USER_ROUTES } from \"../config/api\";\r\nimport { FaHeart } from \"react-icons/fa\";\r\n\r\nimport {\r\n  DecideItemAction,\r\n  editItemPayload,\r\n  getToken,\r\n  RedirectToLoginIfNot,\r\n  userDataFromLocal,\r\n} from \"../utils/utils\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport moment from \"moment\";\r\nimport FeaturedBooks from \"../components/common/BookCard\";\r\nimport testBook from \"@/public/landing/testbook.png\";\r\nimport { MdArrowOutward, MdStar } from \"react-icons/md\";\r\nimport { LuHeart } from \"react-icons/lu\";\r\nimport Link from \"next/link\";\r\nimport { FaStar } from \"react-icons/fa\";\r\nimport { FaRegStar } from \"react-icons/fa\";\r\nimport CircularRating from \"./CircleRating\";\r\nimport { FaRegThumbsUp } from \"react-icons/fa\";\r\nimport { FaRegThumbsDown } from \"react-icons/fa\";\r\nimport Select, { StylesConfig } from \"react-select\";\r\nimport AddRating from \"./AddRating\";\r\nimport { toast } from \"react-toastify\";\r\nimport { HiOutlineChatBubbleLeftRight } from \"react-icons/hi2\";\r\nimport { HiBookmark } from \"react-icons/hi\";\r\nimport {\r\n  editListingPrefillData,\r\n  updateItemId,\r\n  updateProfileComponentIndex,\r\n  updateUserListingData,\r\n} from \"../redux/slices/storeSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport {\r\n  bookMarkItem,\r\n  boostItem,\r\n  delete_bookMarkItem,\r\n  getBooksById,\r\n  getItemBySeller,\r\n  getReviewsOfUser,\r\n} from \"../services/profile\";\r\nimport { RxCross1 } from \"react-icons/rx\";\r\nimport { ItemListStatusEnum } from \"../config/constant\";\r\nimport Skeleton from \"react-loading-skeleton\";\r\nimport { Circles } from \"react-loader-spinner\";\r\nimport Mapview from \"../components/book-listing/Mapview\";\r\nimport ItemRespectiveDetails from \"./ItemRespectiveDetails\";\r\nimport { createInitialsAvatar } from \"../components/InitialAvatar/CreateInitialAvatar\";\r\nimport { formatWithCommas } from \"../utils/utils\"\r\n// Sample data structure for the book\r\nconst sampleBook = {\r\n  description:\r\n    \"This is a fascinating book about the history of programming languages and their evolution over time. The author explores how different languages shaped the way we think about software development and problem-solving.\",\r\n  averageRating: 4.5,\r\n  totalReviews: 24,\r\n  ratings: {\r\n    5: 12,\r\n    4: 8,\r\n    3: 3,\r\n    2: 1,\r\n    1: 0,\r\n  },\r\n  reviews: [\r\n    {\r\n      author: \"Alex Johnson\",\r\n      image: \"\",\r\n      date: \"July 2, 2020 03:29 PM\",\r\n      rating: 5,\r\n      comment:\r\n        \"Absolutely loved this book! It provided great insights into how programming languages evolved.\",\r\n    },\r\n    {\r\n      author: \"Sam Wilson\",\r\n      image: \"\",\r\n      date: \"July 2, 2020 03:29 PM\",\r\n      rating: 4,\r\n      comment:\r\n        \"Well-researched and engaging. Could have included more examples though.\",\r\n    },\r\n  ],\r\n  location: {\r\n    address: \"123 Book Street, Knowledge City, 54321\",\r\n    distance: \"2.5 miles\",\r\n  },\r\n};\r\n\r\n// import testBook from \"@/public/landing/testbook.png\"\r\nconst CustomSlider = dynamic(() => import(\"@/app/components/common/Slider\"));\r\n\r\nfunction BookDetailComponent() {\r\n  // const router = useRouter();\r\n  const dispatch = useDispatch();\r\n  const router = useRouter();\r\n\r\n  const query = useSearchParams();\r\n  const [bookState, setbookState] = useState({});\r\n  console.log(bookState, \"bookState\");\r\n\r\n  const [allBookSeller, setallBookSeller] = useState([]);\r\n  const [activeTab, setActiveTab] = useState(\"Descriptions\");\r\n\r\n  const [address, setaddress] = useState(\"\");\r\n  const [seeMore, setseeMore] = useState(false);\r\n  const contentRef = useRef(null);\r\n  const smallcontentRef = useRef(null);\r\n  const [isSeller, setisSeller] = useState(false);\r\n  const [isLoading, setisLoading] = useState(false);\r\n  const [allReviews, setallReviews] = useState({});\r\n  const [isImageLoaded, setisImageLoaded] = useState(false);\r\n  const [height, setHeight] = useState(\"50px\");\r\n  const [ratingData, setratingData] = useState({\r\n    5: 0,\r\n    4: 0,\r\n    3: 0,\r\n    2: 0,\r\n    1: 0,\r\n  });\r\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\r\n  // let isSeller=\"\"\r\n  console.log(\"query\", query.get(\"id\"));\r\n\r\n  let isLoggedIn = getToken();\r\n  const settings = {\r\n    dots: false,\r\n    dotsClass: \"custom_inside_dots slick-dots !bottom-4.5 md:!bottom-6\",\r\n    infinite: true,\r\n    speed: 500,\r\n    slidesToShow: 4,\r\n    slidesToScroll: 4,\r\n    arrows: false,\r\n    adaptiveHeight: true,\r\n  };\r\n  useEffect(() => {\r\n    if (seeMore && contentRef.current) {\r\n      // console.log()\r\n      setHeight(`${contentRef.current.scrollHeight}px`);\r\n    } else {\r\n      setHeight(\"50px\");\r\n    }\r\n  }, [seeMore]);\r\n  console.log(height, \"height\");\r\n\r\n  const userId = (() => {\r\n    try {\r\n      const userData = JSON.parse(localStorage.getItem(\"userData\"));\r\n      return userData?._id || null;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  })();\r\n\r\n  const fetchBookDetails = async (id) => {\r\n    try {\r\n      setisLoading(true);\r\n      if (!id) {\r\n        return;\r\n      }\r\n      let userToken = getToken();\r\n      let response = await getBooksById(id, userId);\r\n      // let data = await fetch(`${USER_ROUTES.LIST_ITEM}/${id}`, {\r\n      //     headers: {\r\n      //         \"Content-Type\": \"application/json\",\r\n      //         \"Authorization\": `Bearer ${userToken}`\r\n      //     },\r\n      // },)\r\n      // let response = await data.json()\r\n      console.log(\"data fetchBookDetails\", response);\r\n      if (response?.status == 200) {\r\n        setbookState(response?.data);\r\n        let userData = userDataFromLocal();\r\n        if (userData?._id == response?.data?.createdBy._id) {\r\n          setisSeller(true);\r\n        }\r\n      }\r\n      setisLoading(false);\r\n    } catch (err) {\r\n      console.log(\"fetchBookDetails err\", err);\r\n    }\r\n  };\r\n\r\n  console.log(bookState, \"bookState\");\r\n\r\n  const getItemsBySellerFunc = async (text) => {\r\n    try {\r\n      // let userToken = getToken()\r\n\r\n      // let data = await fetch(`${USER_ROUTES.LIST_ITEM}/user/${bookState.createdBy._id}`, {\r\n      //     headers: {\r\n      //         \"Content-Type\": \"application/json\",\r\n      //         \"Authorization\": `Bearer ${userToken}`\r\n      //     },\r\n      // },)\r\n\r\n      let response = await getItemBySeller(bookState.createdBy._id);\r\n\r\n      console.log(\"response in seller\", response);\r\n      // let response = await data.json()\r\n      // console.log(\"data getAllBookOfUser\", await data.json())\r\n      if (response.data.length) {\r\n        // setBookData(response.data)\r\n        setallBookSeller(\r\n          response.data.filter((item) => item._id != bookState?._id)\r\n        );\r\n      }\r\n    } catch (err) {\r\n      console.log(\"err\", err);\r\n    }\r\n    // setBookData()\r\n  };\r\n  console.log(\"allBookSeller\", allBookSeller);\r\n  console.log(bookState, \"bookState\");\r\n\r\n  useEffect(() => {\r\n    fetchBookDetails(query.get(\"id\"));\r\n  }, [query.get(\"id\")]);\r\n  let actionPayload = {};\r\n  useEffect(() => {\r\n    if (bookState.createdBy) {\r\n      getItemsBySellerFunc();\r\n      getReviews(bookState);\r\n      actionPayload = DecideItemAction(bookState.status);\r\n    }\r\n  }, [bookState]);\r\n\r\n  let ratings = \"1\";\r\n  const total = Object.values(ratings).reduce((acc, val) => acc + val, 0);\r\n  const StarReviewBreakdown = ({ ratings }) => {\r\n    // ratings is an object like {5: 1000, 4: 800, 3: 200, 2: 50, 1: 10}\r\n    // Calculate total reviews\r\n    const total = Object.values(ratings).reduce((acc, val) => acc + val, 0);\r\n\r\n    return (\r\n      <div className=\" mx-auto\">\r\n        {Object.entries(ratings)\r\n          .sort((a, b) => b[0] - a[0]) // Sort descending by star rating\r\n          .map(([star, count]) => {\r\n            const percentage = (count / total) * 100;\r\n            return (\r\n              <div key={star} className=\"flex items-center space-x-3 mb-2\">\r\n                {/* Star number */}\r\n                <span className=\"w-5 font-semibold flex\">\r\n                  {star}{\" \"}\r\n                  <span className=\"text-yellow\">\r\n                    <MdStar fill=\"#FFD700\" size={24} />\r\n                  </span>\r\n                </span>\r\n\r\n                {/* Bar container */}\r\n                <div className=\"flex-1 bg-gray-300 rounded-full h-3 overflow-hidden\">\r\n                  <div\r\n                    className=\"bg-yellow-400 h-3\"\r\n                    style={{\r\n                      width: `${percentage}%`,\r\n                      backgroundColor: \"#292929\",\r\n                    }}\r\n                  ></div>\r\n                </div>\r\n                {/* Count display */}\r\n                <span className=\"w-12 text-right font-medium text-gray-700\">\r\n                  {count >= 1000 ? (count / 1000).toFixed(1) + \"k\" : count}\r\n                </span>\r\n              </div>\r\n            );\r\n          })}\r\n        {/* <p className=\"text-sm text-gray-500 mt-4\">Total reviews: {total}</p> */}\r\n      </div>\r\n    );\r\n  };\r\n  // const ratingData = {\r\n  //     5: 1000,\r\n  //     4: 800,\r\n  //     3: 200,\r\n  //     2: 50,\r\n  //     1: 10,\r\n  // };\r\n\r\n  // let allRatingData = [\r\n  //     { name: \"Kashish Akansha\", disLikeCount: 5, likeCount: 10, date: \"2025-05-16T11:25:26.961Z\", rating: 5, review: \"Smooth buying experience on ReBookIt! Book was in great condition.\" },\r\n  //     { name: \"Tony Stark\", disLikeCount: 30, likeCount: 1, date: \"2025-05-08T11:29:26.961Z\", rating: 4, review: \"Smooth buying experience on ReBookIt! Book was in great condition.\" },\r\n  //     { name: \"Huge Jakcman\", disLikeCount: 8, likeCount: 56, date: \"2025-05-17T11:29:45.961Z\", rating: 4, review: \"Smooth buying experience on ReBookIt! Book was in great condition.\" },\r\n  //     { name: \"Richard Branson\", disLikeCount: 45, likeCount: 100, date: \"2025-05-16T11:54:26.961Z\", rating: 3, review: \"Smooth buying experience on ReBookIt! Book was in great condition.\" },\r\n  //     { name: \"Captain\", disLikeCount: 2, likeCount: 11, date: \"2025-05-09T11:29:26.961Z\", rating: 5, review: \"Smooth buying experience on ReBookIt! Book was in great condition.\" },\r\n  //     { name: \"Kashish Akansha\", disLikeCount: 3, likeCount: 5, date: \"2025-05-16T11:29:26.961Z\", rating: 1, review: \"Smooth buying experience on ReBookIt! Book was in great condition.\" }\r\n  // ]\r\n\r\n  const getLocationFromCoordinates = async (cordinates) => {\r\n    console.log(\"cordinates check \", cordinates);\r\n\r\n    // return\r\n    try {\r\n      if (!cordinates?.length) {\r\n        return;\r\n      }\r\n      // let [lat, lon] = cordinates\r\n      const response = await fetch(\r\n        `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${cordinates[0]}&lon=${cordinates[1]}`\r\n      );\r\n      const data = await response.json();\r\n      console.log(data, \"location search\"); // Full human-readable address\r\n      setaddress(data?.display_name || \"NA\");\r\n      return data?.display_name || \"NA\";\r\n    } catch (err) {\r\n      console.error(\"Error fetching location:\", err);\r\n      setaddress(bookState.address);\r\n    }\r\n  };\r\n  // console.log(\"address\", address);\r\n  console.log(\r\n    \"bookState?.geometry?.location?.coordinates\",\r\n    bookState?.address?.geometry?.location?.coordinates\r\n  );\r\n  useEffect(() => {\r\n    if (bookState?.address?.geometry?.location?.coordinates?.length)\r\n      getLocationFromCoordinates(\r\n        bookState?.address?.geometry?.location?.coordinates\r\n      );\r\n  }, [bookState?.address?.geometry?.location?.coordinates]);\r\n  // getLocationFromCoordinates(\"33.425125\", \"-94.04768820000001\")\r\n  console.log(\"bookState.description\", bookState.description?.length);\r\n  console.log(\"see More\", seeMore);\r\n\r\n  // Navigation of chat button\r\n  const chatNavigationHandler = (e, itemId) => {\r\n\r\n    e.stopPropagation();\r\n    const profileIndex = 3;\r\n    dispatch(updateProfileComponentIndex(profileIndex));\r\n    dispatch(updateItemId(itemId));\r\n    if (getToken()) {\r\n      router.push(`/profile/messages?itemId=${itemId}`);\r\n    } else {\r\n      RedirectToLoginIfNot(\r\n        `${window.location.pathname}${window.location.search}`,\r\n        router\r\n      );\r\n    }\r\n  };\r\n  console.log(\"window.location.\", window.location);\r\n  console.log(\r\n    \"bookState.authors[0]\",\r\n    bookState?.authors?.length && bookState?.authors[0]\r\n  );\r\n\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const bookTheItemMark = async (id) => {\r\n    setLoading(true);\r\n\r\n    if (!getToken()) {\r\n      let url = `${window.location.href}`;\r\n      url = url.replace(window.location.origin, \"\");\r\n      router.push(`/login?redirect=${url}`);\r\n    }\r\n    if (!isSeller) {\r\n      try {\r\n        let bookMarkResponse = await bookMarkItem({\r\n          itemId: bookState._id || id,\r\n          itemId: bookState._id || id,\r\n        });\r\n        // console.log(\"bookMarkResponse\", bookMarkResponse)\r\n        if (bookMarkResponse.data?._id) {\r\n          toast.success(\"Item added\");\r\n        }\r\n        setbookState({\r\n          ...bookState,\r\n          isBookmarked: true,\r\n          bookmarkDoc: {\r\n            _id: bookMarkResponse.data?._id,\r\n          },\r\n        });\r\n        setLoading(false);\r\n      } catch (err) {\r\n        setLoading(false);\r\n\r\n        console.log(\"err bookTheItemMark\", err);\r\n      }\r\n    } else {\r\n      if (bookState.status == ItemListStatusEnum.MARKED_AS_SOLD) {\r\n        return;\r\n      }\r\n      RedirectToLoginIfNot(\"/become-seller\", router);\r\n      let payloadToSet = editItemPayload(bookState);\r\n      dispatch(editListingPrefillData(payloadToSet));\r\n    }\r\n  };\r\n\r\n  function Spinner() {\r\n    return (\r\n      <span\r\n        className=\"inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\r\n        style={{ verticalAlign: \"middle\" }}\r\n        aria-label=\"Loading\"\r\n      />\r\n    );\r\n  }\r\n\r\n  const deleteBookMarkItem = async (id) => {\r\n    setLoading(true);\r\n    let response = await delete_bookMarkItem(id);\r\n    if (response.status == 200) {\r\n      setLoading(false);\r\n      toast.success(\"Removed Wishlist Item\");\r\n      setbookState({\r\n        ...bookState,\r\n        isBookmarked: false,\r\n        bookmarkDoc: {},\r\n      });\r\n    } else {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getReviews = async (bookState) => {\r\n    try {\r\n      let userReviewsData = await getReviewsOfUser(bookState?.createdBy?._id);\r\n      console.log(\"userReviewsData\", userReviewsData);\r\n      if (userReviewsData.status == 200) {\r\n        setallReviews(userReviewsData.data);\r\n        if (userReviewsData.data.reviews) {\r\n          let ratingDataObj = {\r\n            5: 0,\r\n            4: 0,\r\n            3: 0,\r\n            2: 0,\r\n            1: 0,\r\n          };\r\n          if (userReviewsData.data.reviews.length) {\r\n            userReviewsData.data.reviews.map((item) => {\r\n              if (item.rating == 5) {\r\n                ratingDataObj[5]++;\r\n              } else if (item.rating == 4) {\r\n                ratingDataObj[4]++;\r\n              } else if (item.rating == 3) {\r\n                ratingDataObj[3]++;\r\n              } else if (item.rating == 2) {\r\n                ratingDataObj[2]++;\r\n              } else {\r\n                ratingDataObj[1]++;\r\n              }\r\n            });\r\n\r\n            console.log(ratingDataObj, \"ratingDataObj\");\r\n            setratingData(ratingDataObj);\r\n          }\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.log(\"getReviews err\", err);\r\n    }\r\n  };\r\n  console.log(\"allReviews\", allReviews);\r\n\r\n  console.log(\"contentRef.current\", contentRef?.current?.scrollHeight);\r\n  const chatFunc = (e) => {\r\n    if (!isSeller) {\r\n      chatNavigationHandler(e, query.get(\"id\"));\r\n    } else {\r\n      if (DecideItemAction(bookState.status).boost) {\r\n        let docElement = document\r\n          .getElementById(\"myModal\")\r\n          .classList.remove(\"hidden\");\r\n      }\r\n    }\r\n  };\r\n\r\n  // const bookMarkfunc = () => {\r\n\r\n  //     if (!isSeller) {\r\n  //         toast.success(\"Work in progress\")\r\n  //     } else {\r\n  //         let payloadToSet = {\r\n  //             currentStep: 3,\r\n  //             completedStep: 2,\r\n  //             category: { _id: bookState.categoryId._id, name:bookStatecategoryId.name|| \"\" },\r\n  //             subCategory\r\n  //                 : { _id: bookState.subCategoryId._id||\"\", name:bookState.subCategoryId.name ||\"\" },\r\n  //             listData: {\r\n  //                 tags: bookState.tags||[],\r\n  //                 ISBN: bookState.isbn_number||\"\",\r\n  //                 bookName: bookState.itemName||\"\",\r\n  //                 desciption: bookState.description||\"\",\r\n  //                 bookAuthor: bookState.author||\"\",\r\n  //                 price: bookState.price||\"\",\r\n  //                 bookCondition: \"\",\r\n  //                 quantity: 0,\r\n  //                 address: bookState.address||\"\",\r\n  //                 locationCoordinates: {\r\n  //                     lat: bookState.location.coordinates[0], lng:bookState.location.coordinates[1]\r\n  //                 },\r\n  //                 OTP: \"\",\r\n  //                 isVerified: false,\r\n  //                 bookImages: {\r\n  //                     cover: bookState.coverImage||\"\",\r\n  //                     front: bookState.frontImage||\"\",\r\n  //                     middle: middleImage.middleImage|| \"\",\r\n  //                     spine: bookState.spineImage||\"\",\r\n  //                 }\r\n  //             }\r\n  //         }\r\n  //         // dispatch(updateUserListingData(payloadToSet))\r\n  //     }\r\n  // }\r\n\r\n  //    const bookTheItemMark = async (id) => {\r\n  //         try {\r\n\r\n  //             let bookMarkResponse = await bookMarkItem({ itemId: id })\r\n  //             console.log(\"bookMarkResponse\", bookMarkResponse)\r\n  //             if (bookMarkResponse.data?._id) {\r\n  //                 toast.success(\"Item added\")\r\n  //             }\r\n  //         } catch (err) {\r\n  //             console.log(\"err bookTheItemMark\", err)\r\n  //         }\r\n  //     }\r\n  const attachXYZ = (string) => {\r\n    if (string?.includes(\"http\")) {\r\n      return string\r\n    } else {\r\n      return \"https://\" + string\r\n    }\r\n  }\r\n  const btnTextBoostChat = () => {\r\n    if (isSeller) {\r\n      if (bookState.status == ItemListStatusEnum.ACCEPTED) {\r\n        return { text: \"Boost Item\", enable: true };\r\n      } else {\r\n        return { text: \"Boost Item\", enable: false };\r\n      }\r\n    } else {\r\n      return { text: \"Chat\", enable: true };\r\n    }\r\n  };\r\n  let starRate = Array.from({\r\n    length: Math.ceil(allReviews?.averageRating),\r\n  }).map((item) => \"hii\");\r\n  console.log(\r\n    \"array\",\r\n    Array.from({ length: Math.ceil(allReviews?.averageRating) }).map(\r\n      (item) => \"hii\"\r\n    )\r\n  );\r\n\r\n  console.log(\r\n    \"center check\",\r\n    bookState?.address?.geometry?.location?.coordinates\r\n  );\r\n\r\n  const boostItemFunc = async (id) => {\r\n\r\n    try {\r\n      let response = await boostItem(id);\r\n      console.log(response);\r\n      if (response.status == 200) {\r\n        document.getElementById(\"myModal\").classList.add(\"hidden\");\r\n        toast.success(`Boost Initiated Successfull`);\r\n      }\r\n    } catch (err) { }\r\n  };\r\n  console.log(\"setisLoading\", isLoading);\r\n  if (isLoading) {\r\n    return (\r\n      <div\r\n        style={{\r\n          display: \"flex\",\r\n          justifyContent: \"center\",\r\n          alignItems: \"center\",\r\n          height: \"100vh\",\r\n        }}\r\n      >\r\n        <Circles\r\n          height=\"80\"\r\n          width=\"80\"\r\n          color=\"#4fa94d\"\r\n          ariaLabel=\"circles-loading\"\r\n          wrapperStyle={{}}\r\n          wrapperClass=\"\"\r\n          visible={true}\r\n        />\r\n      </div>\r\n    );\r\n  } else {\r\n    return (\r\n      <div className=\"bookDetailContainer max-w-[1500px] mx-auto mt-[10px] md:mt-[50px] lg:mt-[90px] md-px-4 sm:px-12 md:px-8\">\r\n        <section className=\"w-full mb-12 flex flex-col md:flex-row gap-6\">\r\n          {/* LEFT: Images */}\r\n          <div className=\"w-full  md:w-[35%] flex flex-col 0 gap-3\">\r\n            {/* Main Image Display */}\r\n            <div className=\"relative w-full bg-[#FFFBF6] border-[1.42px] border-[#F1F1F1] overflow-hidden\">\r\n              {bookState?.images?.[currentImageIndex] && (\r\n                <img\r\n                  src={\r\n                    bookState.images[currentImageIndex] || \"/images/book_1.jpg\"\r\n                  }\r\n                  alt={`Book view ${currentImageIndex + 1}`}\r\n                  onLoad={() => setisImageLoaded(true)}\r\n                  className={`\r\n        w-full \r\n        h-auto \r\n        max-h-[300px]       /* constrain height if you like */\r\n        mx-auto \r\n        object-contain      /* show entire image */\r\n        transition-opacity \r\n        duration-300 \r\n        ${isImageLoaded ? \"opacity-100\" : \"opacity-0\"}\r\n      `}\r\n                />\r\n              )}\r\n            </div>\r\n\r\n            {/* Thumbnails */}\r\n            <div className=\"flex gap-2 overflow-x-auto no_scrollbar py-2\">\r\n              {bookState?.images?.map((src, idx) => (\r\n                <button\r\n                  key={idx}\r\n                  onClick={() => setCurrentImageIndex(idx)}\r\n                  className={`flex-shrink-0 w-[22%] sm:w-[18%] p-1 rounded bg-[#FFFBF6] ${idx === currentImageIndex ? \"\" : \"border-[#F1F1F1] border\"\r\n                    } transition`}\r\n                >\r\n                  <img\r\n                    src={src || \"/images/book_1.jpg\"}\r\n                    alt={`Thumbnail ${idx + 1}`}\r\n                    onLoad={() => setisImageLoaded(true)}\r\n                    className=\"w-full h-auto object-cover\"\r\n                    style={{ aspectRatio: \"3/4\" }}\r\n                  />\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* RIGHT: Details */}\r\n          <div className=\"w-full md:w-[60%] flex flex-col md:gap-4 pl-0 md:pl-[3%]\">\r\n            {/* Title and Price */}\r\n            <div className=\"space-y-2\">\r\n              <p className=\"text-[#212A30] text-[16px] font-medium md:text-[19px]\">\r\n                {bookState?.categoryId?.name || \"Sample Category\"}\r\n                {bookState?.subCategoryId?.name &&\r\n                  `/${bookState?.subCategoryId?.name}`}\r\n                {bookState?.subSubCategoryId?.name &&\r\n                  `/${bookState?.subSubCategoryId?.name}`}\r\n                {bookState?.subSubSubCategoryId?.name &&\r\n                  `/${bookState?.subSubSubCategoryId?.name}`}\r\n              </p>\r\n              <p className=\"text-[#212A30] text-2xl md:text-[30px] font-bold mb-2 md:mb-[20px]\">\r\n                {bookState?.title}\r\n              </p>\r\n\r\n              <div className=\"flex flex-wrap justify-between items-center gap-2 mb-2 md:mb-[20px]\">\r\n                <button className=\"text-white text-[20px] md:text-[33px] global_linear_gradient rounded-full py-1.5 px-5\">\r\n                  J${formatWithCommas(bookState?.price)}\r\n                </button>\r\n                <p className=\" md:text-[21px] text-lg leading-[27px]\">\r\n                  Date:{\" \"}\r\n                  <span className=\"font-medium\">\r\n                    {moment(bookState?.createdAt).format(\"DD-MM-YYYY\")}\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <hr className=\"border-t border-gray-300 hidden md:block\" />\r\n\r\n            {/* Seller Info */}\r\n            <div className=\"flex flex-col space-y-4\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <p className=\"text-lg md:text-[21px] leading-[27px]\">\r\n                  <span className=\"font-medium\">\r\n                    {`${bookState?.createdBy?.firstName} ${bookState?.createdBy?.lastName}`}\r\n                  </span>\r\n                </p>\r\n                <div className=\"flex items-center gap-1\">\r\n                  {allReviews?.averageRating && (\r\n                    <span className=\"bg-[#14884C] text-white text-[8px] md:text-[12px] py-[1px] px-2 rounded\">\r\n                      {allReviews?.averageRating}\r\n                    </span>\r\n                  )}\r\n                  <div className=\"flex\">\r\n                    {starRate.map((_, i) => (\r\n                      <IoMdStar key={i} size={16} />\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <ItemRespectiveDetails bookState={bookState} />\r\n              {/* <p className=\"text-xs  mb-2 text-[16px] md:text-[21px] leading-[27px]\">\r\n          {[labelItemToKind.ScholarshipAwardItem.website]}:{\" \"}\r\n          <span className=\"font-medium\"><a href={attachXYZ(bookState?.website)} target=\"_blank\">{bookState?.website}</a></span>\r\n        </p> */}\r\n\r\n              {bookState?.website && <p className=\"mb-4 text-md md:text-[21px] leading-[27px]\">\r\n                Website:{\" \"}\r\n                <span className=\"font-medium text-blue-500\"><a href={attachXYZ(bookState?.website)} target=\"_blank\">{bookState?.website}</a></span>\r\n              </p>}\r\n              <p className=\" text-md md:text-[21px] leading-[27px]\">\r\n                Location:{\" \"}\r\n                <span className=\"font-medium\">\r\n                  {bookState?.address?.formatted_address}\r\n                </span>\r\n              </p>\r\n            </div>\r\n\r\n            {/* Description on mobile */}\r\n            {/* <div className=\"md:hidden mt-4\">\r\n              <h4 className=\"font-semibold mb-2\">Description:</h4>\r\n              <p\r\n                ref={smallcontentRef}\r\n                className={`text-[16px] overflow-hidden transition-all duration-500 ease-in-out ${\r\n                  seeMore ? \"max-h-[300px]\" : \"max-h-[50px]\"\r\n                }`}\r\n              >\r\n                {bookState?.description}\r\n              </p>\r\n              {smallcontentRef.current?.scrollHeight > 50 && (\r\n                <button\r\n                  className=\"text-blue-500 text-xs mt-1\"\r\n                  onClick={() => setseeMore(!seeMore)}\r\n                >\r\n                  See {seeMore ? \"less\" : \"more\"}\r\n                </button>\r\n              )}\r\n            </div> */}\r\n\r\n            {/* Buttons */}\r\n            <div className=\"flex flex-wrap gap-3 mt-5\">\r\n              <button\r\n                disabled={!btnTextBoostChat().enable}\r\n                onClick={chatFunc}\r\n                className={`flex gap-2 items-center py-2 px-4 rounded-full text-lg md:px-20 md:text-[20px] lg:text-[24px] ${btnTextBoostChat().enable\r\n                  ? \"global_linear_gradient text-white cursor-pointer\"\r\n                  : \"bg-[gray] text-[white] border-[2px] cursor-not-allowed opacity-50 pointer-events-none\"\r\n                  }`}\r\n              >\r\n                {!isSeller && <HiOutlineChatBubbleLeftRight />}\r\n                <span>{btnTextBoostChat().text}</span>\r\n              </button>\r\n              {isSeller ? (\r\n                <button\r\n                  onClick={bookTheItemMark}\r\n                  className=\"flex gap-2 items-center py-2 px-4 md:px-15 rounded-full text-lg md:text-[20px] lg:text-[24px] bg-white border-2 border-[#211F54] text-[#211F54]\"\r\n                >\r\n                  <span>Edit</span>\r\n                </button>\r\n              ) : (\r\n                <button\r\n                  onClick={() =>\r\n                    bookState?.isBookmarked\r\n                      ? deleteBookMarkItem(bookState?.bookmarkDoc?._id)\r\n                      : bookTheItemMark()\r\n                  }\r\n                  className={`flex gap-2 items-center py-2 px-4 md:px-15 rounded-full text-lg md:text-[20px] lg:text-[24px] border-2 border-[#211F54] ${bookState?.isBookmarked\r\n                    ? \"bg-[#211F54] text-white\"\r\n                    : \"bg-white text-[#211F54]\"\r\n                    }`}\r\n                >\r\n                  {loading ? (\r\n                    <>\r\n                      <span\r\n                        className=\"inline-block w-5 h-5 border-2 border-blue-500 border-t-white rounded-full animate-spin\"\r\n                        style={{ verticalAlign: \"middle\" }}\r\n                        aria-label=\"Loading\"\r\n                      />\r\n                      <span className=\"ml-2 \"></span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      {/* <HiBookmark\r\n                        className=\"text-[20px]\"\r\n                        color={bookState?.isBookmarked ? \"#FFD700\" : \"black\"}\r\n                        fill={bookState?.isBookmarked ? \"#FFD700\" : \"red\"}\r\n                      /> */}\r\n                      <FaHeart size={20} />\r\n\r\n                    </>\r\n                  )}\r\n                  <span>Wishlist</span>\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        <div className=\"tabbed-interface\">\r\n          {/* Tab Navigation */}\r\n          <div className=\"tab-navigation flex overflow-x-auto whitespace-nowrap border-b border-[#DBDBDB] bg-[#FAFAFA]\">\r\n            {[\"Descriptions\", \"Reviews\", \"Location\"].map((tab) => (\r\n              <button\r\n                key={tab}\r\n                className={`tab-button flex-shrink-0 px-4 py-2 font-medium text-sm md:text-base relative ${activeTab === tab\r\n                  ? \"text-[#3C3C3C] border-b-2 border-[#3C3C3C]\"\r\n                  : \"text-gray-500 hover:text-gray-700\"\r\n                  }`}\r\n                onClick={() => {\r\n                  setActiveTab(tab);\r\n                  document\r\n                    .getElementById(`${tab.toLowerCase()}-content`)\r\n                    .scrollIntoView({ behavior: \"smooth\", block: \"start\" });\r\n                }}\r\n              >\r\n                {tab}\r\n                {activeTab === tab && (\r\n                  <div className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-[#3C3C3C]\" />\r\n                )}\r\n              </button>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Tab Content */}\r\n          <div className=\"tab-content\">\r\n            {/* Descriptions Tab */}\r\n            <div\r\n              className=\"description-content p-4 md:p-[24px]\"\r\n              id=\"descriptions-content\"\r\n            // hidden={activeTab !== \"Descriptions\"}\r\n            >\r\n              <p className=\"text-gray-700\">\r\n                {bookState?.description1 || (\r\n                  <div className=\"space-y-2\">\r\n                    {/* fallback content */}\r\n                    <p>{bookState.description}</p>\r\n                  </div>\r\n                )}\r\n              </p>\r\n            </div>\r\n\r\n            <hr\r\n              className=\"my-2 md:my-[20px] border-t border-gray-300\"\r\n            // hidden={activeTab === \"Reviews\" && \"Descriptions\"}\r\n            />\r\n\r\n            {/* Location Tab */}\r\n            <div\r\n              className=\"location-content px-4 md:px-[24px] py-4 md:py-[24px]\"\r\n              id=\"location-content\"\r\n            // hidden={activeTab !== \"Location\"}\r\n            >\r\n              <h3 className=\"text-xl md:text-[40px] font-bold mb-2\">\r\n                Seller&apos;s Location\r\n              </h3>\r\n              <Mapview\r\n                width=\"100%\"\r\n                height=\"300px\"\r\n                data={bookState}\r\n                isSingleBookDetails={true}\r\n                center={{\r\n                  lat: bookState?.address?.geometry?.location?.coordinates?.[1],\r\n                  lng: bookState?.address?.geometry?.location?.coordinates?.[0]\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            <hr\r\n              className=\"my-5 md:my-[40px] border-t border-gray-300\"\r\n            // hidden={activeTab !== \"Reviews\" || activeTab === \"Descriptions\"}\r\n            />\r\n\r\n            {/* Reviews Tab */}\r\n            <div\r\n              className=\"reviews-content px-4 md:px-[24px]\"\r\n              id=\"reviews-content\"\r\n            // hidden={activeTab !== \"Reviews\"}\r\n            >\r\n              {allReviews?._id && (\r\n                <section>\r\n                  <h2 className=\"text-2xl md:text-[45px] font-bold\">\r\n                    Seller Reviews\r\n                  </h2>\r\n                  <div className=\"mt-4 grid grid-cols-1 md:grid-cols-10 gap-4\">\r\n                    <div className=\"col-span-1 md:col-span-4 flex items-center\">\r\n                      <CircularRating\r\n                        rating={allReviews.averageRating || 0}\r\n                        maxRating={5}\r\n                        size={80}\r\n                      />\r\n                      <div className=\"ml-3\">\r\n                        <div className=\"flex gap-1\">\r\n                          {Array.from({ length: 5 }).map((_, i) => (\r\n                            <MdStar key={i} fill=\"#FFD700\" size={20} />\r\n                          ))}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-600\">\r\n                          From {allReviews?.reviews?.length || 0} Reviews\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"col-span-1 md:col-span-6\">\r\n                      <StarReviewBreakdown ratings={ratingData} />\r\n                    </div>\r\n                  </div>\r\n                </section>\r\n              )}\r\n\r\n              <section className=\"mt-6 space-y-6\">\r\n                <h2 className=\"text-xl md:text-[34px] font-bold\">\r\n                  Review Lists\r\n                </h2>\r\n                {allReviews?._id ? (\r\n                  allReviews.reviews.map((item) => (\r\n                    <div key={item._id} className=\"individual_review\">\r\n                      <div className=\"flex mb-2\">\r\n                        {Array.from({ length: item.rating }).map((_, idx) => (\r\n                          <MdStar key={idx} fill=\"#FFD700\" size={18} />\r\n                        ))}\r\n                      </div>\r\n                      <p className=\"font-semibold text-base mb-1.5\">\r\n                        \"{item.comment}\"\r\n                      </p>\r\n                      <p className=\"text-sm text-gray-500 mb-2\">\r\n                        {moment(item.createdAt).format(\"MMMM D, YYYY hh:mm a\")}\r\n                      </p>\r\n                      <div className=\"flex justify-between items-center\">\r\n                        <div className=\"flex items-center\">\r\n                          <img\r\n                            className=\"w-10 h-10 rounded-full\"\r\n                            src={item?.reviewer?.profileImage || createInitialsAvatar(`${item?.reviewer?.firstName}  ${item?.reviewer?.lastName || \"\"}`, {\r\n                              bgColor: \"#3f51b5\",\r\n                              textColor: \"#ffffff\",\r\n                            })}\r\n                            alt=\"Reviewer\"\r\n                          />\r\n                          <p className=\"ml-2 text-base font-semibold\">\r\n                            {item?.reviewer?.firstName}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      <hr className=\"my-4 border-t border-gray-300\" />\r\n                    </div>\r\n                  ))\r\n                ) : (\r\n                  <p>No Review Available</p>\r\n                )}\r\n\r\n                {isLoggedIn && !isSeller && (\r\n                  <AddRating bookState={bookState} getReviews={getReviews} />\r\n                )}\r\n              </section>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <hr className=\"my-5 border-t border-gray-300 md:my-[40px]\" />\r\n\r\n        {!isSeller && allBookSeller.length > 0 && (\r\n          <section>\r\n            <div className=\"flex justify-between\">\r\n              <div>\r\n                <text className=\"md:text-[45px]  font-bold md:text-3xl sm:text-[24px] \">\r\n                  BOOKS BY SAME SELLER\r\n                </text>\r\n                <p className=\"max-w-[700px] my-8\">\r\n                  Explore stories, knowledge, and imagination with ReBookIt.\r\n                  Find academic essentials, timeless novels, and rare gems—all\r\n                  in one place.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"hidden md:block\">\r\n                {/* <Link href=\"/\" aria-label=\"View all book categories\"> */}\r\n                <svg\r\n                  width=\"178\"\r\n                  height=\"72\"\r\n                  viewBox=\"0 0 178 72\"\r\n                  fill=\"none\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  className=\"cursor-pointer\"\r\n                  onClick={() => {\r\n                    router.push(\"/search\");\r\n                  }}\r\n                >\r\n                  <path\r\n                    d=\"M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285\"\r\n                    stroke=\"#211F54\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017\"\r\n                    stroke=\"#0161AB\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285\"\r\n                    stroke=\"#EFDC2A\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285\"\r\n                    stroke=\"#0161AB\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M140.693 6L36.937 5.99999\"\r\n                    stroke=\"#FF0009\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M140.693 65.457L36.937 65.457\"\r\n                    stroke=\"#4A8B40\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <rect\r\n                    x=\"11.6016\"\r\n                    y=\"7.93848\"\r\n                    width=\"154.01\"\r\n                    height=\"54.6036\"\r\n                    rx=\"27.3018\"\r\n                    fill=\"white\"\r\n                  />\r\n                  <text\r\n                    x=\"50%\"\r\n                    y=\"50%\"\r\n                    dominantBaseline=\"middle\"\r\n                    textAnchor=\"middle\"\r\n                    fontSize=\"20\"\r\n                    fill=\"#211F54\"\r\n                    fontFamily=\"Poppins, sans-serif\"\r\n                  >\r\n                    View All\r\n                  </text>\r\n                </svg>\r\n                {/* </Link> */}\r\n              </div>\r\n            </div>\r\n\r\n            <div\r\n              // className=\"my-5 grid grid-cols-2 lg:grid-cols-4 gap-2.5 md:my-14 md:gap-8 md:justify-between lg:mt-[30px]\"\r\n              className=\"\"\r\n            >\r\n              <CustomSlider sliderSettings={settings}>\r\n                {allBookSeller.map((item, idx) => (\r\n                  <article\r\n                    key={idx}\r\n                    className=\"border border-[#80808017] pt-1 px-[3px] pb-[15px] lg:border-0\"\r\n                  >\r\n                    <div className=\"bg-gray px-[30px] relative flex justify-center items-center py-1.5 bg-[#f1f1f1] w-full aspect-square lg:aspect-square lg:py-2.5 lg:px-2.5\">\r\n                      <img\r\n                        // src={item.coverImage || item.backCoverImage}\r\n                        src={item.images}\r\n                        alt=\"book image\"\r\n                        fill\r\n                        className={`object-cover px-[30px] max-h-[250px]  transition-opacity duration-300 ${isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n                          }`}\r\n                        sizes=\"(min-width: 1024px) 25vw, (min-width: 768px) 50vw, 100vw\"\r\n                        onLoad={() => setisImageLoaded(true)}\r\n                      />\r\n                      <div\r\n                        className=\"h-7 w-7 p-1 rounded-full border border-gray-200 bg-white flex justify-center items-center absolute top-1 right-1 cursor-pointer\"\r\n                        onClick={() => bookTheItemMark(item._id)}\r\n                      >\r\n                        <LuHeart\r\n                          size={17}\r\n                          color=\"#000\"\r\n                          className=\"opacity-90\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"pt-2 pl-2 pr-2.5 md:text-[20px] lg:pt-5 lg:pb-[28px]\">\r\n                      <p\r\n                        title={item.title}\r\n                        className=\"text-sm leading-[19px] font-semibold lg:text-[20px] lg:leading-[26px] lg:w-11/12 line-clamp-1\"\r\n                      >\r\n                        {item.title}\r\n                      </p>\r\n\r\n                      <div className=\"flex justify-between items-center my-2.5\">\r\n                        <p className=\"font-bold self-end\">\r\n                          <span className=\"text-[#4D7906] lg:text-[20px] lg:leading-[22px]\">\r\n                            JMD{\" \"}\r\n                          </span>\r\n                          ${formatWithCommas(item.price)}\r\n                        </p>\r\n                        <div className=\"cursor-pointer global_linear_gradient p-2 rounded-full flex justify-center items-center lg:h-[46px] lg:w-[46px]\">\r\n                          <Link\r\n                            href={{\r\n                              pathname: \"/book-detail\",\r\n                              query: { id: item._id },\r\n                            }}\r\n                            onClick={() => document.body.scrollTop()}\r\n                            aria-label=\"View all book categories\"\r\n                          >\r\n                            <MdArrowOutward\r\n                              className=\"h-[12px] w-[12px] lg:w-[16px] lg:h-[16px]\"\r\n                              color=\"#fff\"\r\n                            />\r\n                          </Link>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </article>\r\n                ))}\r\n              </CustomSlider>\r\n            </div>\r\n            <div className=\"block md:hidden mx-auto flex justify-center\">\r\n              <Link href=\"/\" aria-label=\"View all book categories\">\r\n                <svg\r\n                  width=\"178\"\r\n                  height=\"72\"\r\n                  viewBox=\"0 0 178 72\"\r\n                  fill=\"none\"\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                >\r\n                  <path\r\n                    d=\"M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285\"\r\n                    stroke=\"#211F54\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017\"\r\n                    stroke=\"#0161AB\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285\"\r\n                    stroke=\"#EFDC2A\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285\"\r\n                    stroke=\"#0161AB\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M140.693 6L36.937 5.99999\"\r\n                    stroke=\"#FF0009\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <path\r\n                    d=\"M140.693 65.457L36.937 65.457\"\r\n                    stroke=\"#4A8B40\"\r\n                    strokeWidth=\"11.679\"\r\n                  />\r\n                  <rect\r\n                    x=\"11.6016\"\r\n                    y=\"7.93848\"\r\n                    width=\"154.01\"\r\n                    height=\"54.6036\"\r\n                    rx=\"27.3018\"\r\n                    fill=\"white\"\r\n                  />\r\n                  <text\r\n                    x=\"50%\"\r\n                    y=\"50%\"\r\n                    dominantBaseline=\"middle\"\r\n                    textAnchor=\"middle\"\r\n                    fontSize=\"20\"\r\n                    fill=\"#211F54\"\r\n                    fontFamily=\"Poppins, sans-serif\"\r\n                  >\r\n                    View All\r\n                  </text>\r\n                </svg>\r\n              </Link>\r\n            </div>\r\n          </section>\r\n        )}\r\n\r\n        {/* {!isSeller && (\r\n          <hr className=\"my-5 border-t border-gray-300 md:my-[100px]\" />\r\n        )} */}\r\n\r\n        <div\r\n          id=\"myModal\"\r\n          className=\"fixed inset-0 flex items-center justify-center z-50 bg-[#EAEAEA]/60 backdrop-blur-sm transition-opacity hidden duration-300 ease-in-out\"\r\n        >\r\n          <div className=\"bg-[#fdfdfd] p-[50px] w-[500px] rounded-lg shadow-lg relative transform transition-all duration-300 ease-in-out scale-95 opacity-0 animate-fadeIn\">\r\n            {/* Close Button */}\r\n            <div\r\n              className=\"absolute top-[-10px] right-[-10px] w-[50px] h-[50px] rounded-full bg-white flex items-center justify-center cursor-pointer\"\r\n              onClick={() => {\r\n                document.getElementById(\"myModal\").classList.add(\"hidden\");\r\n                setmodelForAnswer(false);\r\n              }}\r\n            >\r\n              <RxCross1 className=\"\" size={23} />\r\n            </div>\r\n\r\n            <div className=\"relative w-full h-full  bg-[#FFFBF6] bookDetailMainImageShadow\">\r\n              {bookState?.images?.length && (\r\n                <img\r\n                  src={bookState.images[0] || \"/images/book_1.jpg\"}\r\n                  alt=\"...loading\"\r\n                  fill\r\n                  className={`object-cover mx-auto min-w-350px  transition-opacity duration-300 ${isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n                    }`}\r\n                  onLoad={() => setisImageLoaded(true)}\r\n                  sizes=\"33w\"\r\n                />\r\n              )}\r\n            </div>\r\n            <p className=\"text-[#212A30] text-[14px] md:text-[28px] font-bold my-4\">\r\n              {bookState?.itemName}\r\n            </p>\r\n\r\n            <p className=\"text-[#212A30] text-[12px] md:text-[19px] text-sm my-2\">\r\n              Category:{\" \"}\r\n              <span className=\"font-medium\">\r\n                {bookState?.categoryId?.name || \"Sample Category\"}\r\n              </span>\r\n            </p>\r\n            <p className=\"text-[12px] md:text-[21px] leading-[12px] md:leading-[27px] my-2 \">\r\n              Location:{\" \"}\r\n              <span className=\"font-medium\">\r\n                {bookState.address?.formatted_address || address}\r\n              </span>\r\n            </p>\r\n\r\n            <div className=\"flex items-center my-4\">\r\n              <p className=\"text-[27px] font-semibold\">Price</p>\r\n              <button className=\"text-white text-[20px] ml-2 bg-[#1E2858] rounded-full py-1.5 px-3\">\r\n                J${formatWithCommas(bookState?.price)}\r\n              </button>\r\n            </div>\r\n            {/* Action Button */}\r\n            <div className=\"mt-6 flex justify-center\">\r\n              <button\r\n                onClick={() => boostItemFunc(bookState._id)}\r\n                className=\"py-[1rem] px-[30px] text-[25px]   leading-[18px] text-center text-white rounded-full global_linear_gradient\"\r\n              >\r\n                Confirm Boost\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n\r\nexport default BookDetailComponent;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AAGA;AACA;AAEA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAIA;AACA;AACA;AAEA;AAMA;AACA;AAQA;AACA;AAEA;AACA;AACA;AACA;;;;AA1DA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA,qCAAqC;AACrC,MAAM,aAAa;IACjB,aACE;IACF,eAAe;IACf,cAAc;IACd,SAAS;QACP,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,SAAS;QACP;YACE,QAAQ;YACR,OAAO;YACP,MAAM;YACN,QAAQ;YACR,SACE;QACJ;QACA;YACE,QAAQ;YACR,OAAO;YACP,MAAM;YACN,QAAQ;YACR,SACE;QACJ;KACD;IACD,UAAU;QACR,SAAS;QACT,UAAU;IACZ;AACF;AAEA,uDAAuD;AACvD,MAAM,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;KAAvB;AAEN,SAAS;;IACP,8BAA8B;IAC9B,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,QAAQ,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAC5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,QAAQ,GAAG,CAAC,WAAW;IAEvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,kBAAkB;IAClB,QAAQ,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC;IAE/B,IAAI,aAAa,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACxB,MAAM,WAAW;QACf,MAAM;QACN,WAAW;QACX,UAAU;QACV,OAAO;QACP,cAAc;QACd,gBAAgB;QAChB,QAAQ;QACR,gBAAgB;IAClB;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,WAAW,WAAW,OAAO,EAAE;gBACjC,gBAAgB;gBAChB,UAAU,GAAG,WAAW,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAClD,OAAO;gBACL,UAAU;YACZ;QACF;wCAAG;QAAC;KAAQ;IACZ,QAAQ,GAAG,CAAC,QAAQ;IAEpB,MAAM,SAAS,CAAC;QACd,IAAI;YACF,MAAM,WAAW,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC;YACjD,OAAO,UAAU,OAAO;QAC1B,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF,CAAC;IAED,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YACb,IAAI,CAAC,IAAI;gBACP;YACF;YACA,IAAI,YAAY,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;YACvB,IAAI,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,IAAI;YACtC,6DAA6D;YAC7D,iBAAiB;YACjB,8CAA8C;YAC9C,iDAAiD;YACjD,SAAS;YACT,MAAM;YACN,mCAAmC;YACnC,QAAQ,GAAG,CAAC,yBAAyB;YACrC,IAAI,UAAU,UAAU,KAAK;gBAC3B,aAAa,UAAU;gBACvB,IAAI,WAAW,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD;gBAC/B,IAAI,UAAU,OAAO,UAAU,MAAM,UAAU,KAAK;oBAClD,YAAY;gBACd;YACF;YACA,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,wBAAwB;QACtC;IACF;IAEA,QAAQ,GAAG,CAAC,WAAW;IAEvB,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,6BAA6B;YAE7B,uFAAuF;YACvF,iBAAiB;YACjB,8CAA8C;YAC9C,iDAAiD;YACjD,SAAS;YACT,MAAM;YAEN,IAAI,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,SAAS,CAAC,GAAG;YAE5D,QAAQ,GAAG,CAAC,sBAAsB;YAClC,mCAAmC;YACnC,0DAA0D;YAC1D,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;gBACxB,6BAA6B;gBAC7B,iBACE,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,GAAG,IAAI,WAAW;YAE1D;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,OAAO;QACrB;IACA,gBAAgB;IAClB;IACA,QAAQ,GAAG,CAAC,iBAAiB;IAC7B,QAAQ,GAAG,CAAC,WAAW;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,iBAAiB,MAAM,GAAG,CAAC;QAC7B;wCAAG;QAAC,MAAM,GAAG,CAAC;KAAM;IACpB,IAAI,gBAAgB,CAAC;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,UAAU,SAAS,EAAE;gBACvB;gBACA,WAAW;gBACX,gBAAgB,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,MAAM;YACnD;QACF;wCAAG;QAAC;KAAU;IAEd,IAAI,UAAU;IACd,MAAM,QAAQ,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;IACrE,MAAM,sBAAsB,CAAC,EAAE,OAAO,EAAE;QACtC,oEAAoE;QACpE,0BAA0B;QAC1B,MAAM,QAAQ,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,KAAK;QAErE,qBACE,6LAAC;YAAI,WAAU;sBACZ,OAAO,OAAO,CAAC,SACb,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,iCAAiC;aAC7D,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM;gBACjB,MAAM,aAAa,AAAC,QAAQ,QAAS;gBACrC,qBACE,6LAAC;oBAAe,WAAU;;sCAExB,6LAAC;4BAAK,WAAU;;gCACb;gCAAM;8CACP,6LAAC;oCAAK,WAAU;8CACd,cAAA,6LAAC,iJAAA,CAAA,SAAM;wCAAC,MAAK;wCAAU,MAAM;;;;;;;;;;;;;;;;;sCAKjC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,GAAG,WAAW,CAAC,CAAC;oCACvB,iBAAiB;gCACnB;;;;;;;;;;;sCAIJ,6LAAC;4BAAK,WAAU;sCACb,SAAS,OAAO,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK,MAAM;;;;;;;mBArB7C;;;;;YAyBd;;;;;;IAIR;IACA,uBAAuB;IACvB,eAAe;IACf,cAAc;IACd,cAAc;IACd,aAAa;IACb,aAAa;IACb,KAAK;IAEL,wBAAwB;IACxB,8LAA8L;IAC9L,yLAAyL;IACzL,2LAA2L;IAC3L,gMAAgM;IAChM,sLAAsL;IACtL,4LAA4L;IAC5L,IAAI;IAEJ,MAAM,6BAA6B,OAAO;QACxC,QAAQ,GAAG,CAAC,qBAAqB;QAEjC,SAAS;QACT,IAAI;YACF,IAAI,CAAC,YAAY,QAAQ;gBACvB;YACF;YACA,8BAA8B;YAC9B,MAAM,WAAW,MAAM,MACrB,CAAC,8DAA8D,EAAE,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE;YAEvG,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,MAAM,oBAAoB,8BAA8B;YACpE,WAAW,MAAM,gBAAgB;YACjC,OAAO,MAAM,gBAAgB;QAC/B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,WAAW,UAAU,OAAO;QAC9B;IACF;IACA,mCAAmC;IACnC,QAAQ,GAAG,CACT,8CACA,WAAW,SAAS,UAAU,UAAU;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,WAAW,SAAS,UAAU,UAAU,aAAa,QACvD,2BACE,WAAW,SAAS,UAAU,UAAU;QAE9C;wCAAG;QAAC,WAAW,SAAS,UAAU,UAAU;KAAY;IACxD,gEAAgE;IAChE,QAAQ,GAAG,CAAC,yBAAyB,UAAU,WAAW,EAAE;IAC5D,QAAQ,GAAG,CAAC,YAAY;IAExB,4BAA4B;IAC5B,MAAM,wBAAwB,CAAC,GAAG;QAEhC,EAAE,eAAe;QACjB,MAAM,eAAe;QACrB,SAAS,CAAA,GAAA,uIAAA,CAAA,8BAA2B,AAAD,EAAE;QACrC,SAAS,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAAE;QACtB,IAAI,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,KAAK;YACd,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,QAAQ;QAClD,OAAO;YACL,CAAA,GAAA,wHAAA,CAAA,uBAAoB,AAAD,EACjB,GAAG,OAAO,QAAQ,CAAC,QAAQ,GAAG,OAAO,QAAQ,CAAC,MAAM,EAAE,EACtD;QAEJ;IACF;IACA,QAAQ,GAAG,CAAC,oBAAoB,OAAO,QAAQ;IAC/C,QAAQ,GAAG,CACT,wBACA,WAAW,SAAS,UAAU,WAAW,OAAO,CAAC,EAAE;IAGrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,kBAAkB,OAAO;QAC7B,WAAW;QAEX,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,KAAK;YACf,IAAI,MAAM,GAAG,OAAO,QAAQ,CAAC,IAAI,EAAE;YACnC,MAAM,IAAI,OAAO,CAAC,OAAO,QAAQ,CAAC,MAAM,EAAE;YAC1C,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK;QACtC;QACA,IAAI,CAAC,UAAU;YACb,IAAI;gBACF,IAAI,mBAAmB,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;oBACxC,QAAQ,UAAU,GAAG,IAAI;oBACzB,QAAQ,UAAU,GAAG,IAAI;gBAC3B;gBACA,oDAAoD;gBACpD,IAAI,iBAAiB,IAAI,EAAE,KAAK;oBAC9B,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;gBACA,aAAa;oBACX,GAAG,SAAS;oBACZ,cAAc;oBACd,aAAa;wBACX,KAAK,iBAAiB,IAAI,EAAE;oBAC9B;gBACF;gBACA,WAAW;YACb,EAAE,OAAO,KAAK;gBACZ,WAAW;gBAEX,QAAQ,GAAG,CAAC,uBAAuB;YACrC;QACF,OAAO;YACL,IAAI,UAAU,MAAM,IAAI,4HAAA,CAAA,qBAAkB,CAAC,cAAc,EAAE;gBACzD;YACF;YACA,CAAA,GAAA,wHAAA,CAAA,uBAAoB,AAAD,EAAE,kBAAkB;YACvC,IAAI,eAAe,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE;YACnC,SAAS,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE;QAClC;IACF;IAEA,SAAS;QACP,qBACE,6LAAC;YACC,WAAU;YACV,OAAO;gBAAE,eAAe;YAAS;YACjC,cAAW;;;;;;IAGjB;IAEA,MAAM,qBAAqB,OAAO;QAChC,WAAW;QACX,IAAI,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE;QACzC,IAAI,SAAS,MAAM,IAAI,KAAK;YAC1B,WAAW;YACX,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;gBACX,GAAG,SAAS;gBACZ,cAAc;gBACd,aAAa,CAAC;YAChB;QACF,OAAO;YACL,WAAW;QACb;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,IAAI,kBAAkB,MAAM,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,WAAW;YACnE,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,IAAI,gBAAgB,MAAM,IAAI,KAAK;gBACjC,cAAc,gBAAgB,IAAI;gBAClC,IAAI,gBAAgB,IAAI,CAAC,OAAO,EAAE;oBAChC,IAAI,gBAAgB;wBAClB,GAAG;wBACH,GAAG;wBACH,GAAG;wBACH,GAAG;wBACH,GAAG;oBACL;oBACA,IAAI,gBAAgB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;wBACvC,gBAAgB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;4BAChC,IAAI,KAAK,MAAM,IAAI,GAAG;gCACpB,aAAa,CAAC,EAAE;4BAClB,OAAO,IAAI,KAAK,MAAM,IAAI,GAAG;gCAC3B,aAAa,CAAC,EAAE;4BAClB,OAAO,IAAI,KAAK,MAAM,IAAI,GAAG;gCAC3B,aAAa,CAAC,EAAE;4BAClB,OAAO,IAAI,KAAK,MAAM,IAAI,GAAG;gCAC3B,aAAa,CAAC,EAAE;4BAClB,OAAO;gCACL,aAAa,CAAC,EAAE;4BAClB;wBACF;wBAEA,QAAQ,GAAG,CAAC,eAAe;wBAC3B,cAAc;oBAChB;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,kBAAkB;QAChC;IACF;IACA,QAAQ,GAAG,CAAC,cAAc;IAE1B,QAAQ,GAAG,CAAC,sBAAsB,YAAY,SAAS;IACvD,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU;YACb,sBAAsB,GAAG,MAAM,GAAG,CAAC;QACrC,OAAO;YACL,IAAI,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,MAAM,EAAE,KAAK,EAAE;gBAC5C,IAAI,aAAa,SACd,cAAc,CAAC,WACf,SAAS,CAAC,MAAM,CAAC;YACtB;QACF;IACF;IAEA,+BAA+B;IAE/B,uBAAuB;IACvB,4CAA4C;IAC5C,eAAe;IACf,+BAA+B;IAC/B,8BAA8B;IAC9B,gCAAgC;IAChC,+FAA+F;IAC/F,0BAA0B;IAC1B,sGAAsG;IACtG,0BAA0B;IAC1B,4CAA4C;IAC5C,mDAAmD;IACnD,oDAAoD;IACpD,yDAAyD;IACzD,oDAAoD;IACpD,8CAA8C;IAC9C,qCAAqC;IACrC,+BAA+B;IAC/B,kDAAkD;IAClD,yCAAyC;IACzC,oGAAoG;IACpG,qBAAqB;IACrB,2BAA2B;IAC3B,qCAAqC;IACrC,gCAAgC;IAChC,uDAAuD;IACvD,uDAAuD;IACvD,4DAA4D;IAC5D,uDAAuD;IACvD,oBAAoB;IACpB,gBAAgB;IAChB,YAAY;IACZ,2DAA2D;IAC3D,QAAQ;IACR,IAAI;IAEJ,6CAA6C;IAC7C,gBAAgB;IAEhB,wEAAwE;IACxE,gEAAgE;IAChE,gDAAgD;IAChD,8CAA8C;IAC9C,gBAAgB;IAChB,0BAA0B;IAC1B,sDAAsD;IACtD,YAAY;IACZ,QAAQ;IACR,MAAM,YAAY,CAAC;QACjB,IAAI,QAAQ,SAAS,SAAS;YAC5B,OAAO;QACT,OAAO;YACL,OAAO,aAAa;QACtB;IACF;IACA,MAAM,mBAAmB;QACvB,IAAI,UAAU;YACZ,IAAI,UAAU,MAAM,IAAI,4HAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE;gBACnD,OAAO;oBAAE,MAAM;oBAAc,QAAQ;gBAAK;YAC5C,OAAO;gBACL,OAAO;oBAAE,MAAM;oBAAc,QAAQ;gBAAM;YAC7C;QACF,OAAO;YACL,OAAO;gBAAE,MAAM;gBAAQ,QAAQ;YAAK;QACtC;IACF;IACA,IAAI,WAAW,MAAM,IAAI,CAAC;QACxB,QAAQ,KAAK,IAAI,CAAC,YAAY;IAChC,GAAG,GAAG,CAAC,CAAC,OAAS;IACjB,QAAQ,GAAG,CACT,SACA,MAAM,IAAI,CAAC;QAAE,QAAQ,KAAK,IAAI,CAAC,YAAY;IAAe,GAAG,GAAG,CAC9D,CAAC,OAAS;IAId,QAAQ,GAAG,CACT,gBACA,WAAW,SAAS,UAAU,UAAU;IAG1C,MAAM,gBAAgB,OAAO;QAE3B,IAAI;YACF,IAAI,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE;YAC/B,QAAQ,GAAG,CAAC;YACZ,IAAI,SAAS,MAAM,IAAI,KAAK;gBAC1B,SAAS,cAAc,CAAC,WAAW,SAAS,CAAC,GAAG,CAAC;gBACjD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,2BAA2B,CAAC;YAC7C;QACF,EAAE,OAAO,KAAK,CAAE;IAClB;IACA,QAAQ,GAAG,CAAC,gBAAgB;IAC5B,IAAI,WAAW;QACb,qBACE,6LAAC;YACC,OAAO;gBACL,SAAS;gBACT,gBAAgB;gBAChB,YAAY;gBACZ,QAAQ;YACV;sBAEA,cAAA,6LAAC,+JAAA,CAAA,UAAO;gBACN,QAAO;gBACP,OAAM;gBACN,OAAM;gBACN,WAAU;gBACV,cAAc,CAAC;gBACf,cAAa;gBACb,SAAS;;;;;;;;;;;IAIjB,OAAO;QACL,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAQ,WAAU;;sCAEjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,QAAQ,CAAC,kBAAkB,kBACrC,6LAAC;wCACC,KACE,UAAU,MAAM,CAAC,kBAAkB,IAAI;wCAEzC,KAAK,CAAC,UAAU,EAAE,oBAAoB,GAAG;wCACzC,QAAQ,IAAM,iBAAiB;wCAC/B,WAAW,CAAC;;;;;;;;QAQtB,EAAE,gBAAgB,gBAAgB,YAAY;MAChD,CAAC;;;;;;;;;;;8CAMK,6LAAC;oCAAI,WAAU;8CACZ,WAAW,QAAQ,IAAI,CAAC,KAAK,oBAC5B,6LAAC;4CAEC,SAAS,IAAM,qBAAqB;4CACpC,WAAW,CAAC,0DAA0D,EAAE,QAAQ,oBAAoB,KAAK,0BACtG,WAAW,CAAC;sDAEf,cAAA,6LAAC;gDACC,KAAK,OAAO;gDACZ,KAAK,CAAC,UAAU,EAAE,MAAM,GAAG;gDAC3B,QAAQ,IAAM,iBAAiB;gDAC/B,WAAU;gDACV,OAAO;oDAAE,aAAa;gDAAM;;;;;;2CAVzB;;;;;;;;;;;;;;;;sCAkBb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;gDACV,WAAW,YAAY,QAAQ;gDAC/B,WAAW,eAAe,QACzB,CAAC,CAAC,EAAE,WAAW,eAAe,MAAM;gDACrC,WAAW,kBAAkB,QAC5B,CAAC,CAAC,EAAE,WAAW,kBAAkB,MAAM;gDACxC,WAAW,qBAAqB,QAC/B,CAAC,CAAC,EAAE,WAAW,qBAAqB,MAAM;;;;;;;sDAE9C,6LAAC;4CAAE,WAAU;sDACV,WAAW;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;wDAAwF;wDACrG,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;;;;;;;8DAEjC,6LAAC;oDAAE,WAAU;;wDAAyC;wDAC9C;sEACN,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,WAAW,WAAW,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,6LAAC;oCAAG,WAAU;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACX,cAAA,6LAAC;wDAAK,WAAU;kEACb,GAAG,WAAW,WAAW,UAAU,CAAC,EAAE,WAAW,WAAW,UAAU;;;;;;;;;;;8DAG3E,6LAAC;oDAAI,WAAU;;wDACZ,YAAY,+BACX,6LAAC;4DAAK,WAAU;sEACb,YAAY;;;;;;sEAGjB,6LAAC;4DAAI,WAAU;sEACZ,SAAS,GAAG,CAAC,CAAC,GAAG,kBAChB,6LAAC,iJAAA,CAAA,WAAQ;oEAAS,MAAM;mEAAT;;;;;;;;;;;;;;;;;;;;;;sDAMvB,6LAAC,iJAAA,CAAA,UAAqB;4CAAC,WAAW;;;;;;wCAMjC,WAAW,yBAAW,6LAAC;4CAAE,WAAU;;gDAA6C;gDACtE;8DACT,6LAAC;oDAAK,WAAU;8DAA4B,cAAA,6LAAC;wDAAE,MAAM,UAAU,WAAW;wDAAU,QAAO;kEAAU,WAAW;;;;;;;;;;;;;;;;;sDAElH,6LAAC;4CAAE,WAAU;;gDAAyC;gDAC1C;8DACV,6LAAC;oDAAK,WAAU;8DACb,WAAW,SAAS;;;;;;;;;;;;;;;;;;8CA2B3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,UAAU,CAAC,mBAAmB,MAAM;4CACpC,SAAS;4CACT,WAAW,CAAC,8FAA8F,EAAE,mBAAmB,MAAM,GACjI,qDACA,yFACA;;gDAEH,CAAC,0BAAY,6LAAC,kJAAA,CAAA,+BAA4B;;;;;8DAC3C,6LAAC;8DAAM,mBAAmB,IAAI;;;;;;;;;;;;wCAE/B,yBACC,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC;0DAAK;;;;;;;;;;iEAGR,6LAAC;4CACC,SAAS,IACP,WAAW,eACP,mBAAmB,WAAW,aAAa,OAC3C;4CAEN,WAAW,CAAC,wHAAwH,EAAE,WAAW,eAC7I,4BACA,2BACA;;gDAEH,wBACC;;sEACE,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,eAAe;4DAAS;4DACjC,cAAW;;;;;;sEAEb,6LAAC;4DAAK,WAAU;;;;;;;iFAGlB;8DAME,cAAA,6LAAC,iJAAA,CAAA,UAAO;wDAAC,MAAM;;;;;;;8DAInB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAgB;gCAAW;6BAAW,CAAC,GAAG,CAAC,CAAC,oBAC5C,6LAAC;oCAEC,WAAW,CAAC,6EAA6E,EAAE,cAAc,MACrG,+CACA,qCACA;oCACJ,SAAS;wCACP,aAAa;wCACb,SACG,cAAc,CAAC,GAAG,IAAI,WAAW,GAAG,QAAQ,CAAC,EAC7C,cAAc,CAAC;4CAAE,UAAU;4CAAU,OAAO;wCAAQ;oCACzD;;wCAEC;wCACA,cAAc,qBACb,6LAAC;4CAAI,WAAU;;;;;;;mCAdZ;;;;;;;;;;sCAqBX,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,WAAU;oCACV,IAAG;8CAGH,cAAA,6LAAC;wCAAE,WAAU;kDACV,WAAW,8BACV,6LAAC;4CAAI,WAAU;sDAEb,cAAA,6LAAC;0DAAG,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;8CAMjC,6LAAC;oCACC,WAAU;;;;;;8CAKZ,6LAAC;oCACC,WAAU;oCACV,IAAG;;sDAGH,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,6LAAC,kJAAA,CAAA,UAAO;4CACN,OAAM;4CACN,QAAO;4CACP,MAAM;4CACN,qBAAqB;4CACrB,QAAQ;gDACN,KAAK,WAAW,SAAS,UAAU,UAAU,aAAa,CAAC,EAAE;gDAC7D,KAAK,WAAW,SAAS,UAAU,UAAU,aAAa,CAAC,EAAE;4CAC/D;;;;;;;;;;;;8CAIJ,6LAAC;oCACC,WAAU;;;;;;8CAKZ,6LAAC;oCACC,WAAU;oCACV,IAAG;;wCAGF,YAAY,qBACX,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAGlD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,wIAAA,CAAA,UAAc;oEACb,QAAQ,WAAW,aAAa,IAAI;oEACpC,WAAW;oEACX,MAAM;;;;;;8EAER,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACZ,MAAM,IAAI,CAAC;gFAAE,QAAQ;4EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,iJAAA,CAAA,SAAM;oFAAS,MAAK;oFAAU,MAAM;mFAAxB;;;;;;;;;;sFAGjB,6LAAC;4EAAI,WAAU;;gFAAwB;gFAC/B,YAAY,SAAS,UAAU;gFAAE;;;;;;;;;;;;;;;;;;;sEAK7C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAoB,SAAS;;;;;;;;;;;;;;;;;;;;;;;sDAMtC,6LAAC;4CAAQ,WAAU;;8DACjB,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;gDAGhD,YAAY,MACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;wDAAmB,WAAU;;0EAC5B,6LAAC;gEAAI,WAAU;0EACZ,MAAM,IAAI,CAAC;oEAAE,QAAQ,KAAK,MAAM;gEAAC,GAAG,GAAG,CAAC,CAAC,GAAG,oBAC3C,6LAAC,iJAAA,CAAA,SAAM;wEAAW,MAAK;wEAAU,MAAM;uEAA1B;;;;;;;;;;0EAGjB,6LAAC;gEAAE,WAAU;;oEAAiC;oEAC1C,KAAK,OAAO;oEAAC;;;;;;;0EAEjB,6LAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,KAAK,SAAS,EAAE,MAAM,CAAC;;;;;;0EAEjC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,WAAU;4EACV,KAAK,MAAM,UAAU,gBAAgB,CAAA,GAAA,4JAAA,CAAA,uBAAoB,AAAD,EAAE,GAAG,MAAM,UAAU,UAAU,EAAE,EAAE,MAAM,UAAU,YAAY,IAAI,EAAE;gFAC3H,SAAS;gFACT,WAAW;4EACb;4EACA,KAAI;;;;;;sFAEN,6LAAC;4EAAE,WAAU;sFACV,MAAM,UAAU;;;;;;;;;;;;;;;;;0EAIvB,6LAAC;gEAAG,WAAU;;;;;;;uDA3BN,KAAK,GAAG;;;;8EA+BpB,6LAAC;8DAAE;;;;;;gDAGJ,cAAc,CAAC,0BACd,6LAAC,qIAAA,CAAA,UAAS;oDAAC,WAAW;oDAAW,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvD,6LAAC;oBAAG,WAAU;;;;;;gBAEb,CAAC,YAAY,cAAc,MAAM,GAAG,mBACnC,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAwD;;;;;;sDAGxE,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAOpC,6LAAC;oCAAI,WAAU;8CAEb,cAAA,6LAAC;wCACC,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,OAAM;wCACN,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC;wCACd;;0DAEA,6LAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,6LAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,6LAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,6LAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,6LAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,6LAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,6LAAC;gDACC,GAAE;gDACF,GAAE;gDACF,OAAM;gDACN,QAAO;gDACP,IAAG;gDACH,MAAK;;;;;;0DAEP,6LAAC;gDACC,GAAE;gDACF,GAAE;gDACF,kBAAiB;gDACjB,YAAW;gDACX,UAAS;gDACT,MAAK;gDACL,YAAW;0DACZ;;;;;;;;;;;;;;;;;;;;;;;sCAQP,6LAAC;4BACC,6GAA6G;4BAC7G,WAAU;sCAEV,cAAA,6LAAC;gCAAa,gBAAgB;0CAC3B,cAAc,GAAG,CAAC,CAAC,MAAM,oBACxB,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,+CAA+C;wDAC/C,KAAK,KAAK,MAAM;wDAChB,KAAI;wDACJ,IAAI;wDACJ,WAAW,CAAC,sEAAsE,EAAE,gBAAgB,gBAAgB,aAChH;wDACJ,OAAM;wDACN,QAAQ,IAAM,iBAAiB;;;;;;kEAEjC,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM,gBAAgB,KAAK,GAAG;kEAEvC,cAAA,6LAAC,iJAAA,CAAA,UAAO;4DACN,MAAM;4DACN,OAAM;4DACN,WAAU;;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,OAAO,KAAK,KAAK;wDACjB,WAAU;kEAET,KAAK,KAAK;;;;;;kEAGb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;;kFACX,6LAAC;wEAAK,WAAU;;4EAAkD;4EAC5D;;;;;;;oEACC;oEACL,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,KAAK;;;;;;;0EAE/B,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAM;wEACJ,UAAU;wEACV,OAAO;4EAAE,IAAI,KAAK,GAAG;wEAAC;oEACxB;oEACA,SAAS,IAAM,SAAS,IAAI,CAAC,SAAS;oEACtC,cAAW;8EAEX,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wEACb,WAAU;wEACV,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCApDX;;;;;;;;;;;;;;;sCA8Db,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,cAAW;0CACxB,cAAA,6LAAC;oCACC,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,OAAM;;sDAEN,6LAAC;4CACC,GAAE;4CACF,QAAO;4CACP,aAAY;;;;;;sDAEd,6LAAC;4CACC,GAAE;4CACF,QAAO;4CACP,aAAY;;;;;;sDAEd,6LAAC;4CACC,GAAE;4CACF,QAAO;4CACP,aAAY;;;;;;sDAEd,6LAAC;4CACC,GAAE;4CACF,QAAO;4CACP,aAAY;;;;;;sDAEd,6LAAC;4CACC,GAAE;4CACF,QAAO;4CACP,aAAY;;;;;;sDAEd,6LAAC;4CACC,GAAE;4CACF,QAAO;4CACP,aAAY;;;;;;sDAEd,6LAAC;4CACC,GAAE;4CACF,GAAE;4CACF,OAAM;4CACN,QAAO;4CACP,IAAG;4CACH,MAAK;;;;;;sDAEP,6LAAC;4CACC,GAAE;4CACF,GAAE;4CACF,kBAAiB;4CACjB,YAAW;4CACX,UAAS;4CACT,MAAK;4CACL,YAAW;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAaX,6LAAC;oBACC,IAAG;oBACH,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,WAAU;gCACV,SAAS;oCACP,SAAS,cAAc,CAAC,WAAW,SAAS,CAAC,GAAG,CAAC;oCACjD,kBAAkB;gCACpB;0CAEA,cAAA,6LAAC,iJAAA,CAAA,WAAQ;oCAAC,WAAU;oCAAG,MAAM;;;;;;;;;;;0CAG/B,6LAAC;gCAAI,WAAU;0CACZ,WAAW,QAAQ,wBAClB,6LAAC;oCACC,KAAK,UAAU,MAAM,CAAC,EAAE,IAAI;oCAC5B,KAAI;oCACJ,IAAI;oCACJ,WAAW,CAAC,kEAAkE,EAAE,gBAAgB,gBAAgB,aAC5G;oCACJ,QAAQ,IAAM,iBAAiB;oCAC/B,OAAM;;;;;;;;;;;0CAIZ,6LAAC;gCAAE,WAAU;0CACV,WAAW;;;;;;0CAGd,6LAAC;gCAAE,WAAU;;oCAAyD;oCAC1D;kDACV,6LAAC;wCAAK,WAAU;kDACb,WAAW,YAAY,QAAQ;;;;;;;;;;;;0CAGpC,6LAAC;gCAAE,WAAU;;oCAAoE;oCACrE;kDACV,6LAAC;wCAAK,WAAU;kDACb,UAAU,OAAO,EAAE,qBAAqB;;;;;;;;;;;;0CAI7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,6LAAC;wCAAO,WAAU;;4CAAoE;4CACjF,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;;;;;;;;;;;;;0CAInC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,cAAc,UAAU,GAAG;oCAC1C,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;AACF;GAnnCS;;QAEU,4JAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QAEV,qIAAA,CAAA,kBAAe;;;MALtB;uCAqnCM", "debugId": null}}]}