(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/public/images/testimonials/javon.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/javon.423120aa.svg");}}),
"[project]/public/images/testimonials/javon.svg.mjs { IMAGE => \"[project]/public/images/testimonials/javon.svg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$javon$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/testimonials/javon.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$javon$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 152,
    height: 152,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/testimonials/michelle.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/michelle.c43c54fa.svg");}}),
"[project]/public/images/testimonials/michelle.svg.mjs { IMAGE => \"[project]/public/images/testimonials/michelle.svg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$michelle$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/testimonials/michelle.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$michelle$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 151,
    height: 151,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/testimonials/andre.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/andre.615b981d.svg");}}),
"[project]/public/images/testimonials/andre.svg.mjs { IMAGE => \"[project]/public/images/testimonials/andre.svg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$andre$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/testimonials/andre.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$andre$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 151,
    height: 151,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/testimonials/david.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/david.ef8c2bd3.svg");}}),
"[project]/public/images/testimonials/david.svg.mjs { IMAGE => \"[project]/public/images/testimonials/david.svg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$david$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/testimonials/david.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$david$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 211,
    height: 211,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/testimonials/tiana.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/tiana.4995a9e2.svg");}}),
"[project]/public/images/testimonials/tiana.svg.mjs { IMAGE => \"[project]/public/images/testimonials/tiana.svg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$tiana$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/testimonials/tiana.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$tiana$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 151,
    height: 152,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/WidthChecker.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useElementWidth": (()=>useElementWidth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function useElementWidth() {
    _s();
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [width, setWidth] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    console.log("refd", ref);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useElementWidth.useEffect": ()=>{
            const updateWidth = {
                "useElementWidth.useEffect.updateWidth": ()=>{
                    if (ref.current) {
                        setWidth(ref.current.offsetWidth);
                    }
                }
            }["useElementWidth.useEffect.updateWidth"];
            updateWidth(); // Initial measure
            window.addEventListener("resize", updateWidth);
            return ({
                "useElementWidth.useEffect": ()=>window.removeEventListener("resize", updateWidth)
            })["useElementWidth.useEffect"];
        }
    }["useElementWidth.useEffect"], []);
    return [
        ref,
        width
    ];
}
_s(useElementWidth, "jogSCeqzj/gzLY6WYFFRlandjVY=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {
        // Clear local storage and redirect to /login
        localStorage.clear();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
        window.location.href = "/login";
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        // console.log("error log is", error)
        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.message)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://devapi.rebookit.club");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/profile.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addReviewForSeller": (()=>addReviewForSeller),
    "bookMarkItem": (()=>bookMarkItem),
    "boostItem": (()=>boostItem),
    "deleteChatById": (()=>deleteChatById),
    "deleteMyBooks": (()=>deleteMyBooks),
    "delete_bookMarkItem": (()=>delete_bookMarkItem),
    "editItem": (()=>editItem),
    "getAdPlanById": (()=>getAdPlanById),
    "getAdPlans": (()=>getAdPlans),
    "getAllChat": (()=>getAllChat),
    "getBooksById": (()=>getBooksById),
    "getCategories": (()=>getCategories),
    "getChatById": (()=>getChatById),
    "getFaq": (()=>getFaq),
    "getItemBySeller": (()=>getItemBySeller),
    "getMyBooks": (()=>getMyBooks),
    "getPaymentIntent": (()=>getPaymentIntent),
    "getReviewsOfUser": (()=>getReviewsOfUser),
    "getSubCategories": (()=>getSubCategories),
    "getSubSubCategories": (()=>getSubSubCategories),
    "getSubSubSubCategories": (()=>getSubSubSubCategories),
    "getTestimonials": (()=>getTestimonials),
    "get_bookMarkItems": (()=>get_bookMarkItems),
    "getsubscriptionPlans": (()=>getsubscriptionPlans),
    "listItem": (()=>listItem),
    "paymentHistory": (()=>paymentHistory),
    "searchByName": (()=>searchByName),
    "searchISBN": (()=>searchISBN),
    "searchItemByName": (()=>searchItemByName),
    "supportRequest": (()=>supportRequest),
    "uploadPhotoSingle": (()=>uploadPhotoSingle),
    "verifyUserData": (()=>verifyUserData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/axios.js [app-client] (ecmascript)");
;
;
;
const uri = {
    login: "/user/login",
    userInfo: "/user",
    editProfile: "/user/edit-profile",
    item_by_name: `item/search`,
    subscriptionPlan: "/admin/subscription/plan",
    fetch_category: "/master/category",
    fetchSubCategory: "master/sub-category",
    fetchSubSubCategory: "master/Sub-Sub-category",
    fetchSubSubSubCategory: "master/sub-sub-sub-category",
    getPaymentIntent: "/payment/payment-intent",
    verifyUserData: "user/verify-otp",
    searchISBN: "/books/isbn/{{ISBN}}",
    searchByName: "/books/search?q={search}",
    bookMarkItem_id: "/item/bookmark",
    get_bookMark_by_user: "/item/bookmarks",
    getItems: "/item/search/current-user",
    getItemById: "/item",
    createItem: "/item",
    editItem: "/item",
    deleteItemById: "/item",
    itemBySeller: "/item/user",
    addReview: "/item/addReview",
    userReviews: "/item/{:id}/reviews",
    uploadPhoto: "admin/single-upload",
    history: "/payment/history",
    supportRequest: "/user/support-request",
    boostItem: "/item/boost",
    getTestimonials: "/user/testimonials",
    getFaq: "/faqs/filter-search",
    // ad-management
    getAdPlans: "ad-management/getAdPlans",
    getAdPlanById: "ad-management/getAdPlanById"
};
const chat = {
    chat: "/chat/all",
    chatById: "/chat"
};
const listItem = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.createItem}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const getAdPlans = async ({ type, position, page = 1, limit = 10 } = {})=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlans}`, {
        params: {
            ...type && {
                type
            },
            ...position && {
                position
            },
            page,
            limit
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getAdPlanById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlanById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const editItem = async (payload, id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.editItem}/${id}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const addReviewForSeller = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.addReview}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getMyBooks = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getItems}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const deleteMyBooks = async (id, data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.deleteItemById}/${id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getBooksById = async (id, userId)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getItemById}/${id}`, {
        params: {
            userId
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getItemBySeller = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.itemBySeller}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const searchItemByName = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.item_by_name}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getsubscriptionPlans = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.subscriptionPlan}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetch_category}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getAllChat = async (payloadData)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${chat.chat}`, payloadData).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const deleteChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const bookMarkItem = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.bookMarkItem_id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const getReviewsOfUser = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.userReviews}`.replace("{:id}", id));
    // .catch(axiosErrorHandler);
    console.log("getSubCategories response", response);
    return response;
};
const delete_bookMarkItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${uri.bookMarkItem_id}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const get_bookMarkItems = async ()=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.get_bookMark_by_user}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getPaymentIntent = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getPaymentIntent}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const verifyUserData = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.verifyUserData}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchISBN = async (ISBN)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchISBN}`.replace("{{ISBN}}", ISBN)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchByName = async (search)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchByName}`.replace("{search}", search)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const uploadPhotoSingle = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.uploadPhoto}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const paymentHistory = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.history}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const supportRequest = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.supportRequest}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const boostItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.boostItem}/` + id).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getTestimonials = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getTestimonials}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getFaq = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getFaq}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
} // let data = await fetch(USER_ROUTES.SEARCH_ITEM_BY_NAME.replace("itemName", text), {
 //                 headers: {
 //                     "Content-Type": "application/json",
 //                     "Authorization": `Bearer ${userToken}`
 //                 },
 //             },)
 //             let response = await data.json()
 //             // console.log("data getAllBookOfUser", await data.json())
 //             if (response.data) {
 //                 setBookData(response.data)
 //             }
 // export const login = async (payload, guestId) => {
 //     let response = await instance
 //         .post(`${uri.login}`,payload)
 //         .catch(axiosErrorHandler);
 //         console.log("login test response",response)
 //         return response
 // };
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/landingPage/testimonials/testimonials.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Testimonials)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// import Slider from 'react-slick';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$javon$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$testimonials$2f$javon$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/testimonials/javon.svg.mjs { IMAGE => "[project]/public/images/testimonials/javon.svg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$michelle$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$testimonials$2f$michelle$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/testimonials/michelle.svg.mjs { IMAGE => "[project]/public/images/testimonials/michelle.svg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$andre$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$testimonials$2f$andre$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/testimonials/andre.svg.mjs { IMAGE => "[project]/public/images/testimonials/andre.svg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$david$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$testimonials$2f$david$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/testimonials/david.svg.mjs { IMAGE => "[project]/public/images/testimonials/david.svg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$testimonials$2f$tiana$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$testimonials$2f$tiana$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/testimonials/tiana.svg.mjs { IMAGE => "[project]/public/images/testimonials/tiana.svg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$WidthChecker$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/WidthChecker.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/profile.js [app-client] (ecmascript)");
;
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
const Slider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/app/components/common/Slider.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/app/components/common/Slider.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false,
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: "Loading slider..."
        }, void 0, false, {
            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
            lineNumber: 20,
            columnNumber: 18
        }, this)
});
_c = Slider;
function Testimonials() {
    _s();
    const [ref, width] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$WidthChecker$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useElementWidth"])();
    const sliderRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isDesktop, setIsDesktop] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    console.log("width", width);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Testimonials.useEffect": ()=>{
            setIsDesktop(width >= 768);
        }
    }["Testimonials.useEffect"], [
        width
    ]);
    const [testimonialData, setTestimonialData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const getTestimonialsFunc = async ()=>{
        let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTestimonials"])();
        console.log("response testimonials", response);
        if (response.status == 200) {
            setTestimonialData(response.data);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Testimonials.useEffect": ()=>{
            // setTestimonialData(dummyTesData);
            getTestimonialsFunc();
        }
    }["Testimonials.useEffect"], []);
    const settings = {
        dots: width < 720 ? true : false,
        infinite: true,
        speed: 500,
        slidesToShow: width < 720 ? 1 : 3,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000,
        // className: "center",
        // centerMode: true,
        // centerPadding: "0px",
        // afterChange: (current) => {
        //     // This ensures the next slide gets the slick-center class
        //     document.querySelector('.testimonials-slider .slick-current + .slick-active')?.classList.add("testimonials-slick-center");
        //     document.querySelector('.testimonials-slider .slick-current')?.classList.remove("testimonials-slick-center");
        // },
        responsive: [
            {
                breakpoint: 720,
                settings: {
                    slidesToShow: 1,
                    centerMode: false,
                    initialSlide: 0
                }
            }
        ],
        appendDots: (dots)=>{
            // Get active dot index
            const activeIndex = dots.findIndex((dot)=>dot.props.className.includes("slick-active"));
            let visibleDots = [];
            // Show max 3, center active dot if possible
            if (dots.length <= 3) {
                visibleDots = dots;
            } else {
                const start = Math.max(0, Math.min(activeIndex - 1, dots.length - 3));
                visibleDots = dots.slice(start, start + 3);
            }
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                    className: "flex justify-center gap-5",
                    children: visibleDots
                }, void 0, false, {
                    fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                    lineNumber: 95,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                lineNumber: 94,
                columnNumber: 9
            }, this);
        }
    };
    // // Initialize center slide on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Testimonials.useEffect": ()=>{
            if (sliderRef.current && width >= 768) {
                setTimeout({
                    "Testimonials.useEffect": ()=>{
                        const sliderParent = document.querySelector(".testimonials-slider");
                        const slides = sliderParent.querySelectorAll(".slick-slide");
                        slides[3]?.classList.remove("slick-current", "testimonials-slick-center");
                        slides[4]?.classList.add("slick-current", "testimonials-slick-center");
                    }
                }["Testimonials.useEffect"], 100);
            }
        }
    }["Testimonials.useEffect"], [
        width
    ]);
    // Initialize center slide on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Testimonials.useEffect": ()=>{
            if (!isDesktop || !sliderRef.current) return;
            const sliderParent = document.querySelector(".testimonials-slider");
            const slides = sliderParent.querySelectorAll(".slick-slide");
            if (slides.length > 4) {
                slides[3]?.classList.remove("slick-current", "testimonials-slick-center");
                slides[4]?.classList.add("slick-current", "testimonials-slick-center");
            }
        }
    }["Testimonials.useEffect"], [
        isDesktop
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        ref: ref,
        className: `testimonialContainer`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container-wrapper",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                    className: "md:flex md:justify-between md:items-center p-2.5 md:py-[40px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-[22px] font-semibold mt-5 leading-normal uppercase md:text-[48px]",
                            children: "What our users Say"
                        }, void 0, false, {
                            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                            lineNumber: 133,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mt-2.5 font-light leading-[18px] text-xs md:text-[18px] md:leading-[27px] md:w-5/12",
                            children: [
                                "Explore stories, knowledge, and imagination with",
                                " ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                    children: "ReBookIt.Club"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                    lineNumber: 138,
                                    columnNumber: 13
                                }, this),
                                " Find academic essentials, timeless novels, and rare gems—all in one place."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                            lineNumber: 136,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                    lineNumber: 132,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                    className: "my-5 md:my-10",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "slider-container ",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Slider, {
                            ref: sliderRef,
                            sliderSettings: settings,
                            className: "custom_slider_dots testimonials-slider w-full",
                            children: testimonialData?.map((data, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-[320px] md:h-[445px] overflow-hidden md:px-2.5 ",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "slide-content h-full w-full flex flex-col items-center justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `testimonial-card border border-gray-300`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    src: data.image,
                                                    width: 200,
                                                    height: 200,
                                                    alt: "user Image"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                                    lineNumber: 157,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiQuotes"], {
                                                    size: 20,
                                                    color: "#07559B",
                                                    className: "absolute right-5 top-46"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                                    lineNumber: 163,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "my-3 text-center text-[15px]",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "capitalize font-medium",
                                                            children: data.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                                            lineNumber: 169,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "my-2 font-light text-gray-900",
                                                            children: data?.title.slice(0, 60)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                                            lineNumber: 170,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-[13px] font-light text-gray-900 break-words",
                                                            children: [
                                                                " ",
                                                                data?.content?.slice(0, 200),
                                                                "..."
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                                            lineNumber: 173,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                                    lineNumber: 168,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                            lineNumber: 156,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                        lineNumber: 155,
                                        columnNumber: 19
                                    }, this)
                                }, `testimonial-${idx} `, false, {
                                    fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                                    lineNumber: 151,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                            lineNumber: 145,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                        lineNumber: 144,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
                    lineNumber: 143,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
            lineNumber: 131,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/landingPage/testimonials/testimonials.js",
        lineNumber: 130,
        columnNumber: 5
    }, this);
}
_s(Testimonials, "rY74BMnzuV1eIQELraUiyAl8WH4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$WidthChecker$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useElementWidth"]
    ];
});
_c1 = Testimonials;
var _c, _c1;
__turbopack_context__.k.register(_c, "Slider");
__turbopack_context__.k.register(_c1, "Testimonials");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/landingPage/testimonials/testimonials.js [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/components/landingPage/testimonials/testimonials.js [app-client] (ecmascript)"));
}}),
}]);

//# sourceMappingURL=_dd83236d._.js.map