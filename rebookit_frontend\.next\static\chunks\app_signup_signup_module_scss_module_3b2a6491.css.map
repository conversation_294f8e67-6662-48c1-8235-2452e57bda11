{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/signup/signup.module.scss.module.css"], "sourcesContent": [".signupContainer{padding:20px;overflow-x:hidden}.signupContainer .imageContainer{margin:3vh auto;width:100%;height:358px;flex-shrink:0}.signupContainer .imageContainer .imageElem{width:369.656px;height:358px}.signupContainer .fields{display:flex;height:70px;padding:6.602px 0px 6.602px 13.204px;align-items:center;flex-shrink:0;align-self:stretch;border-radius:3.301px 3.301px 0px 0px;border-radius:3.301px;border:.825px solid #79747e;background:#fff}.signupContainer .submitButton{display:flex;height:41px;padding:5.781px 11.563px;justify-content:center;align-items:center;gap:2.891px;align-self:stretch;border-radius:96.836px;background:var(--Linear, linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%));cursor:pointer;color:#fff;width:100%}@media(min-width: 769px){.signupContainer{padding:40px 100px;margin:40px 0;display:flex;justify-content:space-between}.signupContainer .imageContainer{margin:5vh auto;width:100%;height:724px;flex-shrink:0}.signupContainer .imageContainer .imageElem{height:617px}.signupContainer .fields{font-style:14px}.signupContainer .submitButton{display:flex;height:62px;padding:8px 16px;justify-content:center;align-items:center;gap:4px;align-self:stretch;border-radius:134px;background:var(--Linear, linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%))}}"], "names": [], "mappings": "AAAA;;;;;AAAgD;;;;;;;AAAuF;;;;;AAAyE;;;;;;;;;;;;AAAmP;;;;;;;;;;;;;;;AAAwS;EAAyB;;;;;;;EAA6F;;;;;;;EAAuF;;;;EAAyD;;;;EAAyC", "debugId": null}}]}