{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/profile/messages/messageComponent.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"borderGradient\": \"messageComponent-module-scss-module__r7VfNq__borderGradient\",\n  \"myMessagesContainer\": \"messageComponent-module-scss-module__r7VfNq__myMessagesContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/socket.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { io } from \"socket.io-client\";\r\nimport { getToken } from \"./utils/utils\";\r\n\r\nexport const socket = io(process.env.NEXT_PUBLIC_BASE_URL, {\r\n    extraHeaders: {\r\n        authorization: `<PERSON><PERSON> ${getToken()}`\r\n    },\r\n    // auth: {\r\n    //     token: `Bear<PERSON> ${getToken()}`\r\n    // },\r\n    // transports: ['websocket'],\r\n    // autoConnect: false,\r\n    // reconnection: true,\r\n    // reconnectionAttempts: 5,\r\n    // reconnectionDelay: 1000,\r\n})\r\n\r\nsocket.on(\"connect\", () => {\r\n    console.log(\"Connected to socket server:\", socket.id);\r\n});\r\n\r\nsocket.on(\"connect_error\", (err) => {\r\n    console.log(\"connecction err\",err)\r\n    console.error(\"Socket connection error:\", err.message);\r\n});\r\n"], "names": [], "mappings": ";;;AAEA;AAAA;AACA;AAHA;;;AAKO,MAAM,SAAS,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,oEAAoC;IACvD,cAAc;QACV,eAAe,CAAC,OAAO,EAAE,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,KAAK;IACzC;AASJ;AAEA,OAAO,EAAE,CAAC,WAAW;IACjB,QAAQ,GAAG,CAAC,+BAA+B,OAAO,EAAE;AACxD;AAEA,OAAO,EAAE,CAAC,iBAAiB,CAAC;IACxB,QAAQ,GAAG,CAAC,mBAAkB;IAC9B,QAAQ,KAAK,CAAC,4BAA4B,IAAI,OAAO;AACzD", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/utils/axiosError.handler.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\r\nimport { getToken } from \"./utils\";\r\n// import history from \"./history\";\r\n\r\n\r\nexport const axiosErrorHandler = (error, action, checkUnauthorized = true) => {\r\n\r\n    const requestStatus = error?.request?.status;\r\n    const responseStatus = error?.response?.status;\r\n    const dataStatus = error?.data?.statusCode;\r\n    \r\n    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {\r\n        \r\n        // Clear local storage and redirect to /login\r\n        localStorage.clear();\r\n        toast.error(\r\n            error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n        );\r\n        window.location.href = \"/login\";\r\n    }\r\n    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {\r\n        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map(er => toast.error(er)) || error?.data?.error?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n            );\r\n    }\r\n    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {\r\n        // console.log(\"error log is\", error)\r\n        \r\n        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map(er => toast.error(er.message)) || error?.data?.message?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.message || error?.response?.data?.data || error?.data?.message,\r\n            );\r\n    }\r\n    if (\r\n        checkUnauthorized &&\r\n        (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)\r\n    ) {\r\n        if (getToken()) {\r\n            toast.error(error?.response?.data?.message);\r\n        }\r\n    }\r\n\r\n    if (action === \"uploadImage\") {\r\n        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {\r\n            if (getToken()) {\r\n                const message = error?.response?.data?.message;\r\n                message && toast.error(message);\r\n            } else history.push(\"/\");\r\n        }\r\n    }\r\n\r\n    if (error?.response) return error.response;\r\n    else if (error?.request) return error.request;\r\n    else return error?.message;\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIO,MAAM,oBAAoB,CAAC,OAAO,QAAQ,oBAAoB,IAAI;IAErE,MAAM,gBAAgB,OAAO,SAAS;IACtC,MAAM,iBAAiB,OAAO,UAAU;IACxC,MAAM,aAAa,OAAO,MAAM;IAEhC,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QAEvE,6CAA6C;QAC7C,aAAa,KAAK;QAClB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;QAEjF,OAAO,QAAQ,CAAC,IAAI,GAAG;IAC3B;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QACvE,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,UAAU,MAAM,OAAO,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,OAAO,MAAM,OAAO,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEzL,mJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;IAEzF;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,kBAAkB,KAAK;QAChG,qCAAqC;QAErC,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,WAAW,OAAO,QAAQ,OAAO,MAAM,SAAS,OAAO,UAAU,MAAM,QAAQ,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM,OAAO,MAAM,SAAS,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEtM,mJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,WAAW,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM;IAE1F;IACA,IACI,qBACA,CAAC,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,GAAG,GACxE;QACE,IAAI,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,KAAK;YACZ,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM;QACvC;IACJ;IAEA,IAAI,WAAW,eAAe;QAC1B,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;YACvE,IAAI,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,KAAK;gBACZ,MAAM,UAAU,OAAO,UAAU,MAAM;gBACvC,WAAW,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAC3B,OAAO,QAAQ,IAAI,CAAC;QACxB;IACJ;IAEA,IAAI,OAAO,UAAU,OAAO,MAAM,QAAQ;SACrC,IAAI,OAAO,SAAS,OAAO,MAAM,OAAO;SACxC,OAAO,OAAO;AACvB", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/services/axios.js"], "sourcesContent": ["const { default: axios } = require(\"axios\");\r\nconst { getToken } = require(\"../utils/utils\");\r\n\r\nconst BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst instance = axios.create({\r\n  baseURL: BASE_URL+\"/api\" ,\r\n\r\n  // Lets keep a check as default is 0 millisecond i.e. never\r\n  // Note: timeout is only for server response not network i.e. server reachability\r\n  timeout: 100000,\r\n\r\n  // Lets keep a check as default bytes- 2k\r\n  maxContentLength: 1000,\r\n\r\n  // Lets keep a check as default 5 seems high\r\n  maxRedirects: 2,\r\n});\r\n\r\ninstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = getToken();\r\n    console.log(\"token\", token)\r\n    \r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // Rate limiting: only fire a request every 2 sec from lodash.debounce\r\n    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });\r\n    return Promise.resolve(config);\r\n  },\r\n  function (error) {\r\n    const response = handleLogError(error); // log them\r\n\r\n    return Promise.reject(error);\r\n  }\r\n  // multiple options as to when and how to apply these interceptors\r\n  // , { synchronous: true, runWhen: onGetCall }\r\n);\r\n\r\n\r\nmodule.exports = instance;"], "names": [], "mappings": "AAAA,MAAM,EAAE,SAAS,KAAK,EAAE;AACxB,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM;AAEN,MAAM,WAAW,MAAM,MAAM,CAAC;IAC5B,SAAS,WAAS;IAElB,2DAA2D;IAC3D,iFAAiF;IACjF,SAAS;IAET,yCAAyC;IACzC,kBAAkB;IAElB,4CAA4C;IAC5C,cAAc;AAChB;AAEA,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;IACC,MAAM,QAAQ;IACd,QAAQ,GAAG,CAAC,SAAS;IAErB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,sEAAsE;IACtE,8EAA8E;IAC9E,OAAO,QAAQ,OAAO,CAAC;AACzB,GACA,SAAU,KAAK;IACb,MAAM,WAAW,eAAe,QAAQ,WAAW;IAEnD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAMF,OAAO,OAAO,GAAG", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/services/profile.js"], "sourcesContent": ["import { USER_ROUTES } from \"../config/api\";\r\nimport { axiosErrorHandler } from \"../utils/axiosError.handler\";\r\nimport instance from \"./axios\";\r\n\r\n\r\nconst uri = {\r\n    login: \"/user/login\",\r\n    userInfo: \"/user\",\r\n    editProfile: \"/user/edit-profile\",\r\n    \r\n\r\n    item_by_name:`item/search`,\r\n    subscriptionPlan:\"/admin/subscription/plan\",\r\n    fetch_category:\"/master/category\",\r\n    fetchSubCategory:\"master/sub-category\",\r\n    fetchSubSubCategory:\"master/Sub-Sub-category\",\r\n    fetchSubSubSubCategory:\"master/sub-sub-sub-category\",\r\n    getPaymentIntent:\"/payment/payment-intent\",\r\n    verifyUserData:\"user/verify-otp\",\r\n    searchISBN:\"/books/isbn/{{ISBN}}\",\r\n    searchByName:\"/books/search?q={search}\",\r\n    \r\n    bookMarkItem_id:\"/item/bookmark\",\r\n    get_bookMark_by_user:\"/item/bookmarks\",\r\n    getItems: \"/item/search/current-user\",\r\n    getItemById:\"/item\",\r\n    createItem:\"/item\",\r\n    editItem:\"/item\",\r\n    deleteItemById:\"/item\",\r\n    itemBySeller:\"/item/user\",\r\n    addReview:\"/item/addReview\",\r\n    userReviews:\"/item/{:id}/reviews\",\r\n\r\n    uploadPhoto: \"admin/single-upload\",\r\n    history:\"/payment/history\",\r\n    supportRequest: \"/user/support-request\",\r\n    boostItem:\"/item/boost\",\r\n    getTestimonials: \"/user/testimonials\",\r\n    getFaq:\"/faqs/filter-search\",\r\n\r\n    // ad-management\r\n    getAdPlans : \"ad-management/getAdPlans\",\r\n    getAdPlanById : \"ad-management/getAdPlanById\",\r\n    \r\n};\r\nconst chat={\r\n    chat:\"/chat/all\",\r\n    chatById:\"/chat\"\r\n\r\n}\r\n// let data = await fetch(USER_ROUTES.LIST_ITEM, {\r\n//             headers: {\r\n//                 \"Content-Type\": \"application/json\",\r\n//                 \"Authorization\": `Bearer ${userToken}`\r\n//             }\r\n//         },)\r\n//         let response = await data.json()\r\n//         // console.log(\"data getAllBookOfUser\", await data.json())\r\n//         if (response.data) {\r\n//             setBookData(response.data)\r\n//         }\r\n\r\n\r\nexport const listItem = async (payload) => {\r\n    let response = await instance\r\n        .post(`${uri.createItem}`,payload)\r\n        .catch(axiosErrorHandler);\r\n    // console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const getAdPlans = async ({ type, position, page = 1, limit = 10 } = {}) => {\r\n    let response = await instance\r\n        .get(`${uri.getAdPlans}`, {\r\n            params: {\r\n                ...(type && { type }),\r\n                ...(position && { position }),\r\n                page,\r\n                limit\r\n            }\r\n        })\r\n        .catch(axiosErrorHandler);\r\n    return response;\r\n}\r\n\r\nexport const getAdPlanById = async (id) => {\r\n    let response = await instance\r\n        .get(`${uri.getAdPlanById}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    return response;\r\n}\r\n\r\nexport const editItem = async (payload,id) => {\r\n    let response = await instance\r\n        .put(`${uri.editItem}/${id}`,payload)\r\n        .catch(axiosErrorHandler);\r\n    // console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const addReviewForSeller = async (payload) => {\r\n    let response = await instance\r\n        .post(`${uri.addReview}`,payload)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\nexport const getMyBooks = async (data,queryString) => {\r\n    let response = await instance\r\n        .post(`${uri.getItems}`+queryString,data)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const deleteMyBooks = async (id, data) => {\r\n    let response = await instance\r\n        .put(`${uri.deleteItemById}/${id}`, data)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\nexport const getBooksById = async (id, userId) => {\r\n    let response = await instance\r\n        .get(`${uri.getItemById}/${id}`, {\r\n            params: { userId }\r\n        })\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\nexport const getItemBySeller = async (id) => {\r\n    \r\n    let response = await instance\r\n        .get(`${uri.itemBySeller}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const searchItemByName = async (data ,queryString) => {\r\n    let response = await instance\r\n        .post(`${uri.item_by_name}`+queryString,data)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const getsubscriptionPlans = async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.subscriptionPlan}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const getCategories= async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.fetch_category}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"login test response\", response)\r\n    return response\r\n}\r\n\r\nexport const getSubCategories= async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.fetchSubCategory}/${text}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const getSubSubCategories= async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.fetchSubSubCategory}/${text}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\nexport const getSubSubSubCategories= async (text) => {\r\n    let response = await instance\r\n        .get(`${uri.fetchSubSubSubCategory}/${text}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const getAllChat= async (payloadData) => {\r\n    let response = await instance\r\n        .post(`${chat.chat}`,payloadData)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\n\r\nexport const getChatById= async (id) => {\r\n    let response = await instance\r\n        .get(`${chat.chatById}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\nexport const deleteChatById= async (id) => {\r\n    let response = await instance\r\n        .delete(`${chat.chatById}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    // console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const bookMarkItem=async(data)=>{\r\n    \r\n    let response = await instance\r\n        .post(`${uri.bookMarkItem_id}`,data)\r\n        .catch(axiosErrorHandler);\r\n    // console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const getReviewsOfUser=async(id)=>{\r\n    let response = await instance\r\n        .get(`${uri.userReviews}`.replace(\"{:id}\",id))\r\n        // .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\n\r\nexport const delete_bookMarkItem=async(id)=>{\r\n    let response = await instance\r\n        .delete(`${uri.bookMarkItem_id}/${id}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const get_bookMarkItems=async()=>{\r\n    let response = await instance\r\n        .get(`${uri.get_bookMark_by_user}`)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const getPaymentIntent =async(body)=>{\r\n    let response = await instance\r\n        .post(`${uri.getPaymentIntent}`,body)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"getSubCategories response\", response)\r\n    return response\r\n}\r\n\r\nexport const verifyUserData =async(body)=>{\r\n    let response = await instance\r\n        .post(`${uri.verifyUserData}`,body)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\nexport const searchISBN =async(ISBN)=>{\r\n    let response = await instance\r\n        .get(`${uri.searchISBN}`.replace(\"{{ISBN}}\",ISBN))\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\nexport const searchByName =async(search)=>{\r\n    let response = await instance\r\n        .get(`${uri.searchByName}`.replace(\"{search}\",search))\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\nexport const uploadPhotoSingle =async(data)=>{\r\n    let response = await instance\r\n        .post(`${uri.uploadPhoto}`,data)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\nexport const paymentHistory =async(data)=>{\r\n    let response = await instance\r\n        .get(`${uri.history}`,data)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\nexport const supportRequest =async(data)=>{\r\n    let response = await instance\r\n        .post(`${uri.supportRequest}`,data)\r\n        .catch(axiosErrorHandler);\r\n    console.log(\"verifyUserData response\", response)\r\n    return response\r\n}\r\n\r\n\r\nexport const boostItem =async(id)=>{\r\n    let response = await instance\r\n        .put(`${uri.boostItem}/`+id)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\nexport const getTestimonials =async(id)=>{\r\n    let response = await instance\r\n        .get(`${uri.getTestimonials}`)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\nexport const getFaq =async(data)=>{\r\n    let response = await instance\r\n        .post(`${uri.getFaq}`)\r\n        .catch(axiosErrorHandler);\r\n    return response\r\n}\r\n\r\n\r\n\r\n// let data = await fetch(USER_ROUTES.SEARCH_ITEM_BY_NAME.replace(\"itemName\", text), {\r\n//                 headers: {\r\n//                     \"Content-Type\": \"application/json\",\r\n//                     \"Authorization\": `Bearer ${userToken}`\r\n//                 },\r\n//             },)\r\n//             let response = await data.json()\r\n//             // console.log(\"data getAllBookOfUser\", await data.json())\r\n//             if (response.data) {\r\n//                 setBookData(response.data)\r\n//             }\r\n// export const login = async (payload, guestId) => {\r\n//     let response = await instance\r\n//         .post(`${uri.login}`,payload)\r\n//         .catch(axiosErrorHandler);\r\n//         console.log(\"login test response\",response)\r\n//         return response\r\n// };\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAGA,MAAM,MAAM;IACR,OAAO;IACP,UAAU;IACV,aAAa;IAGb,cAAa,CAAC,WAAW,CAAC;IAC1B,kBAAiB;IACjB,gBAAe;IACf,kBAAiB;IACjB,qBAAoB;IACpB,wBAAuB;IACvB,kBAAiB;IACjB,gBAAe;IACf,YAAW;IACX,cAAa;IAEb,iBAAgB;IAChB,sBAAqB;IACrB,UAAU;IACV,aAAY;IACZ,YAAW;IACX,UAAS;IACT,gBAAe;IACf,cAAa;IACb,WAAU;IACV,aAAY;IAEZ,aAAa;IACb,SAAQ;IACR,gBAAgB;IAChB,WAAU;IACV,iBAAiB;IACjB,QAAO;IAEP,gBAAgB;IAChB,YAAa;IACb,eAAgB;AAEpB;AACA,MAAM,OAAK;IACP,MAAK;IACL,UAAS;AAEb;AAcO,MAAM,WAAW,OAAO;IAC3B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,UAAU,EAAE,EAAC,SACzB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,+CAA+C;IAC/C,OAAO;AACX;AAEO,MAAM,aAAa,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC;IAC1E,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,UAAU,EAAE,EAAE;QACtB,QAAQ;YACJ,GAAI,QAAQ;gBAAE;YAAK,CAAC;YACpB,GAAI,YAAY;gBAAE;YAAS,CAAC;YAC5B;YACA;QACJ;IACJ,GACC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,gBAAgB,OAAO;IAChC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,aAAa,CAAC,CAAC,EAAE,IAAI,EAChC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,WAAW,OAAO,SAAQ;IACnC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAC,SAC5B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,+CAA+C;IAC/C,OAAO;AACX;AAEO,MAAM,qBAAqB,OAAO;IACrC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,SAAS,EAAE,EAAC,SACxB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AACO,MAAM,aAAa,OAAO,MAAK;IAClC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,QAAQ,EAAE,GAAC,aAAY,MACnC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,gBAAgB,OAAO,IAAI;IACpC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE,MACnC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,eAAe,OAAO,IAAI;IACnC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE;QAC7B,QAAQ;YAAE;QAAO;IACrB,GACC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AACO,MAAM,kBAAkB,OAAO;IAElC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,YAAY,CAAC,CAAC,EAAE,IAAI,EAC/B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,mBAAmB,OAAO,MAAM;IACzC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,YAAY,EAAE,GAAC,aAAY,MACvC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,uBAAuB,OAAO;IACvC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,gBAAgB,EAAE,EAC7B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,gBAAe,OAAO;IAC/B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,cAAc,EAAE,EAC3B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACX;AAEO,MAAM,mBAAkB,OAAO;IAClC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,gBAAgB,CAAC,CAAC,EAAE,MAAM,EACrC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,sBAAqB,OAAO;IACrC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,mBAAmB,CAAC,CAAC,EAAE,MAAM,EACxC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AACO,MAAM,yBAAwB,OAAO;IACxC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,sBAAsB,CAAC,CAAC,EAAE,MAAM,EAC3C,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,aAAY,OAAO;IAC5B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,EAAC,aACpB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAGO,MAAM,cAAa,OAAO;IAC7B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,IAAI,EAC5B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AACO,MAAM,iBAAgB,OAAO;IAChC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,MAAM,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,IAAI,EAC/B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,qDAAqD;IACrD,OAAO;AACX;AAEO,MAAM,eAAa,OAAM;IAE5B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,eAAe,EAAE,EAAC,MAC9B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,qDAAqD;IACrD,OAAO;AACX;AAEO,MAAM,mBAAiB,OAAM;IAChC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,WAAW,EAAE,CAAC,OAAO,CAAC,SAAQ;IAC1C,6BAA6B;IACjC,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAGO,MAAM,sBAAoB,OAAM;IACnC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,MAAM,CAAC,GAAG,IAAI,eAAe,CAAC,CAAC,EAAE,IAAI,EACrC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,oBAAkB;IAC3B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,oBAAoB,EAAE,EACjC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,mBAAkB,OAAM;IACjC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,gBAAgB,EAAE,EAAC,MAC/B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,OAAO;AACX;AAEO,MAAM,iBAAgB,OAAM;IAC/B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,cAAc,EAAE,EAAC,MAC7B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAEO,MAAM,aAAY,OAAM;IAC3B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,UAAU,EAAE,CAAC,OAAO,CAAC,YAAW,OAC3C,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAEO,MAAM,eAAc,OAAM;IAC7B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,YAAY,EAAE,CAAC,OAAO,CAAC,YAAW,SAC7C,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAEO,MAAM,oBAAmB,OAAM;IAClC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,WAAW,EAAE,EAAC,MAC1B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAEO,MAAM,iBAAgB,OAAM;IAC/B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,OAAO,EAAE,EAAC,MACrB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,iBAAgB,OAAM;IAC/B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,cAAc,EAAE,EAAC,MAC7B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,QAAQ,GAAG,CAAC,2BAA2B;IACvC,OAAO;AACX;AAGO,MAAM,YAAW,OAAM;IAC1B,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,GAAC,IACxB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,kBAAiB,OAAM;IAChC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,eAAe,EAAE,EAC5B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX;AAEO,MAAM,SAAQ,OAAM;IACvB,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,MAAM,EAAE,EACpB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IAC5B,OAAO;AACX,EAIA,sFAAsF;CACtF,6BAA6B;CAC7B,0DAA0D;CAC1D,6DAA6D;CAC7D,qBAAqB;CACrB,kBAAkB;CAClB,+CAA+C;CAC/C,yEAAyE;CACzE,mCAAmC;CACnC,6CAA6C;CAC7C,gBAAgB;CAChB,qDAAqD;CACrD,oCAAoC;CACpC,wCAAwC;CACxC,qCAAqC;CACrC,sDAAsD;CACtD,0BAA0B;CAC1B,KAAK", "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/services/auth.js"], "sourcesContent": ["import { USER_ROUTES } from \"../config/api\";\r\nimport { axiosErrorHandler } from \"../utils/axiosError.handler\";\r\nimport { getToken } from \"../utils/utils\";\r\nimport instance from \"./axios\";\r\n\r\n\r\nconst uri = {\r\n    login: \"/user/login\",\r\n    userInfo: \"/user\",\r\n    editProfile:\"/user/edit-profile\",\r\n    verifyOtp:\"user/verify-otp\",\r\n    registerUser: \"/user/register\",\r\n    forgetPassword: \"user/forgot-password\",\r\n    createNewPassword: \"user/create-new-password\",\r\n    sendOtp: \"user/send-otp\",\r\n    verifyForgetPassword: \"/user/verify-forgot-otp\",\r\n    resetPassword:\"/user/reset-password\"\r\n};\r\n\r\n\r\n\r\n// fetch( {\r\n//     method: \"POST\",\r\n//     headers: {\r\n//         \"Content-Type\": \"application/json\"\r\n//     },\r\n//     body: JSON.stringify(data)\r\n// }).then(async res => {\r\n//     const response = await res.json();\r\n//     let authData = response\r\n//     console.log(\"response auth\", response)\r\n//     if (authData?.success) {\r\n//         setToken(authData?.token)\r\n//         toast.success(\"Logged in successfully!\")\r\n//         router.push(\"/\")\r\n//     } else {\r\n//         toast.error(response?.message || \"Incorrect credentials! Try again\")\r\n//     }\r\n// })\r\n\r\n\r\n// const login = async (data) => {\r\n//     try {\r\n//         let userToken = getToken()\r\n//         let response = await RestCall({\r\n//             method: \"get\",\r\n//             url: `${USER_ROUTES.LIST_ITEM}/${id}`,\r\n//             headers: {\r\n//                 \"Content-Type\": \"application/json\",\r\n//             }, data: data\r\n//         })\r\n//         return response\r\n//     } catch (err) {\r\n//         return { message: err.message, code: err.statusCode }\r\n//     }\r\n// }\r\n\r\n\r\nexport const login = async (payload, guestId) => {\r\n    let response = await instance\r\n        .post(`${uri.login}`,payload)\r\n        .catch(axiosErrorHandler);\r\n        console.log(\"login test response\",response)\r\n        return response \r\n};\r\nexport const userInfo_api = async () => {\r\n    if (getToken()) {\r\n    try{\r\n        let response = await instance\r\n            .get(`${uri.userInfo}`)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"login test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }}\r\n};\r\nexport const update_userInfo_api = async (userPayload) => {\r\n    try{\r\n        let response = await instance\r\n            .put(`${uri.editProfile}`,userPayload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"login test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\nexport const verifyOtp = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.verifyOtp}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"verifyOtp test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\nexport const registerUser = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.registerUser}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"registerUser test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\n\r\n\r\nexport const forgotPassword = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.forgetPassword}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"registerUser test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\n\r\nexport const createNewPassword = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.forgetPassword}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"registerUser test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\nexport const sendOTP = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.sendOtp}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"sendOTP test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\nexport const verifyForgetPassword = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.verifyForgetPassword}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"sendOTP test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\n\r\nexport const resetPassword = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.resetPassword}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"resetPassword test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\n\r\n\r\n\r\n// fetch(USER_ROUTES.EDIT_USER_INFO, {\r\n//                 method: \"put\",\r\n//                 headers: {\r\n//                     \"Authorization\": `Bearer ${userToken}`,\r\n//                     \"Content-Type\": \"application/json\"\r\n\r\n//                 },\r\n//                 body: JSON.stringify(userPayload)\r\n\r\n//             }).then(async res => {\r\n//                 const response = await res.json();\r\n//                 if (!response.error) {\r\n//                     toast.success(response.message)\r\n//                     setBtnDisabled(true)\r\n//                 } else {\r\n//                     response.message?.map(x => toast.error(x))\r\n//                     toast.error(response.message || \"No Info Found\")\r\n//                 }\r\n//             })\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAGA,MAAM,MAAM;IACR,OAAO;IACP,UAAU;IACV,aAAY;IACZ,WAAU;IACV,cAAc;IACd,gBAAgB;IAChB,mBAAmB;IACnB,SAAS;IACT,sBAAsB;IACtB,eAAc;AAClB;AAyCO,MAAM,QAAQ,OAAO,SAAS;IACjC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,KAAK,EAAE,EAAC,SACpB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;IACxB,QAAQ,GAAG,CAAC,uBAAsB;IAClC,OAAO;AACf;AACO,MAAM,eAAe;IACxB,IAAI,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,KAAK;QAChB,IAAG;YACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,QAAQ,EAAE,EACrB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;YACxB,QAAQ,GAAG,CAAC,uBAAsB;YAClC,OAAO;QACf,EAAC,OAAM,KAAI;YACP,QAAQ,GAAG,CAAC,uBAAsB;QACtC;IAAC;AACL;AACO,MAAM,sBAAsB,OAAO;IACtC,IAAG;QACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,WAAW,EAAE,EAAC,aACzB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,uBAAsB;QAClC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAEO,MAAM,YAAY,OAAO;IAC5B,IAAG;QACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,SAAS,EAAE,EAAC,SACxB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,2BAA0B;QACtC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAEO,MAAM,eAAe,OAAO;IAC/B,IAAG;QACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,YAAY,EAAE,EAAC,SAC3B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,8BAA6B;QACzC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAIO,MAAM,iBAAiB,OAAO;IACjC,IAAG;QACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,cAAc,EAAE,EAAC,SAC7B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,8BAA6B;QACzC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAGO,MAAM,oBAAoB,OAAO;IACpC,IAAG;QACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,cAAc,EAAE,EAAC,SAC7B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,8BAA6B;QACzC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAEO,MAAM,UAAU,OAAO;IAC1B,IAAG;QACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,OAAO,EAAE,EAAC,SACtB,KAAK,CAAC,qIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,yBAAwB;QACpC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAEO,MAAM,uBAAuB,OAAO;IACvC,IAAG;QACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,oBAAoB,EAAE,EAAC,SACnC,KAAK,CAAC,qIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,yBAAwB;QACpC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAGO,MAAM,gBAAgB,OAAO;IAChC,IAAG;QACC,IAAI,WAAW,MAAM,wHAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,aAAa,EAAE,EAAC,SAC5B,KAAK,CAAC,qIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,+BAA8B;QAC1C,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ,GAKA,sCAAsC;CACtC,iCAAiC;CACjC,6BAA6B;CAC7B,8DAA8D;CAC9D,yDAAyD;CAEzD,qBAAqB;CACrB,oDAAoD;CAEpD,qCAAqC;CACrC,qDAAqD;CACrD,yCAAyC;CACzC,sDAAsD;CACtD,2CAA2C;CAC3C,2BAA2B;CAC3B,iEAAiE;CACjE,uEAAuE;CACvE,oBAAoB;CACpB,iBAAiB", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/components/InitialAvatar/CreateInitialAvatar.js"], "sourcesContent": ["export function createInitialsAvatar(name, options = {}) {\r\n  // Default options\r\n  const {\r\n    size = 100,\r\n    bgColor = \"#cccccc\",\r\n    textColor = \"#000000\",\r\n    shape = \"circle\",\r\n  } = options;\r\n\r\n  // Get initials from name\r\n  const getInitials = (name) => {\r\n    if (!name || typeof name !== \"string\") return \"\";\r\n\r\n    const words = name\r\n      .trim()\r\n      .split(/\\s+/)\r\n      .filter((word) => word.length > 0);\r\n    if (words.length === 0) return \"\";\r\n\r\n    if (words.length === 1) {\r\n      return words[0].charAt(0).toUpperCase();\r\n    }\r\n    return `${words[0].charAt(0)}${words[words.length - 1].charAt(\r\n      0\r\n    )}`.toUpperCase();\r\n  };\r\n\r\n  const initials = getInitials(name);\r\n  const fontSize = size * 0.4;\r\n\r\n  // Create SVG based on shape\r\n  let svgContent;\r\n  if (shape === \"circle\") {\r\n    const radius = size / 2;\r\n    svgContent = `\r\n      <circle cx=\"${radius}\" cy=\"${radius}\" r=\"${radius}\" fill=\"${bgColor}\" />\r\n      <text x=\"50%\" y=\"50%\" dy=\"0.35em\" text-anchor=\"middle\" \r\n            font-family=\"Arial\" font-size=\"${fontSize}\" \r\n            fill=\"${textColor}\" font-weight=\"bold\">\r\n        ${initials}\r\n      </text>\r\n    `;\r\n  } else {\r\n    svgContent = `\r\n      <rect width=\"100%\" height=\"100%\" fill=\"${bgColor}\" />\r\n      <text x=\"50%\" y=\"50%\" dy=\"0.35em\" text-anchor=\"middle\" \r\n            font-family=\"Arial\" font-size=\"${fontSize}\" \r\n            fill=\"${textColor}\" font-weight=\"bold\">\r\n        ${initials}\r\n      </text>\r\n    `;\r\n  }\r\n\r\n  // Create full SVG\r\n  const svg = `\r\n    <svg xmlns=\"http://www.w3.org/2000/svg\" \r\n         width=\"${size}\" \r\n         height=\"${size}\" \r\n         viewBox=\"0 0 ${size} ${size}\">\r\n      ${svgContent}\r\n    </svg>\r\n  `;\r\n\r\n  // Convert to data URL\r\n  return `data:image/svg+xml,${encodeURIComponent(svg)}`;\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,SAAS,qBAAqB,IAAI,EAAE,UAAU,CAAC,CAAC;IACrD,kBAAkB;IAClB,MAAM,EACJ,OAAO,GAAG,EACV,UAAU,SAAS,EACnB,YAAY,SAAS,EACrB,QAAQ,QAAQ,EACjB,GAAG;IAEJ,yBAAyB;IACzB,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;QAE9C,MAAM,QAAQ,KACX,IAAI,GACJ,KAAK,CAAC,OACN,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,GAAG;QAClC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;QACvC;QACA,OAAO,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAC3D,IACC,CAAC,WAAW;IACjB;IAEA,MAAM,WAAW,YAAY;IAC7B,MAAM,WAAW,OAAO;IAExB,4BAA4B;IAC5B,IAAI;IACJ,IAAI,UAAU,UAAU;QACtB,MAAM,SAAS,OAAO;QACtB,aAAa,CAAC;kBACA,EAAE,OAAO,MAAM,EAAE,OAAO,KAAK,EAAE,OAAO,QAAQ,EAAE,QAAQ;;2CAE/B,EAAE,SAAS;kBACpC,EAAE,UAAU;QACtB,EAAE,SAAS;;IAEf,CAAC;IACH,OAAO;QACL,aAAa,CAAC;6CAC2B,EAAE,QAAQ;;2CAEZ,EAAE,SAAS;kBACpC,EAAE,UAAU;QACtB,EAAE,SAAS;;IAEf,CAAC;IACH;IAEA,kBAAkB;IAClB,MAAM,MAAM,CAAC;;gBAEC,EAAE,KAAK;iBACN,EAAE,KAAK;sBACF,EAAE,KAAK,CAAC,EAAE,KAAK;MAC/B,EAAE,WAAW;;EAEjB,CAAC;IAED,sBAAsB;IACtB,OAAO,CAAC,mBAAmB,EAAE,mBAAmB,MAAM;AACxD", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/profile/messages/page.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport messagesComponentScss from \"./messageComponent.module.scss\";\r\nimport { Circles, Discuss } from \"react-loader-spinner\";\r\nimport Link from \"next/link\";\r\nimport { IoIosArrowDown, IoMdSearch } from \"react-icons/io\";\r\nimport { HiOutlineDotsVertical } from \"react-icons/hi\";\r\nimport Image from \"next/image\";\r\nimport { LuClock3 } from \"react-icons/lu\";\r\nimport { IoArrowBack } from \"react-icons/io5\";\r\nimport { FaMapMarkerAlt } from \"react-icons/fa\";\r\nimport { MdKeyboardArrowDown } from \"react-icons/md\";\r\nimport { BiLogoTelegram } from \"react-icons/bi\";\r\nimport predefinedChats from \"./predefinedChats.json\";\r\nimport { socket } from \"@/app/socket\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { CHAT_ROUTES, USER_ROUTES } from \"@/app/config/api\";\r\nimport {\r\n  Debounce,\r\n  formatRelativeDate,\r\n  formatTo12HourTime,\r\n  getToken,\r\n  userDataFromLocal,\r\n} from \"@/app/utils/utils\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  deleteChatById,\r\n  getAllChat,\r\n  getBooksById,\r\n  getChatById,\r\n  uploadPhotoSingle,\r\n} from \"@/app/services/profile\";\r\nimport { update_userInfo_api, userInfo_api } from \"@/app/services/auth\";\r\nimport { MdDeleteOutline } from \"react-icons/md\";\r\nimport { FaPlusCircle } from \"react-icons/fa\";\r\nimport {\r\n  nullItemId,\r\n  updateChatId,\r\n  updateItemId,\r\n} from \"@/app/redux/slices/storeSlice\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { ItemListStatusEnum } from \"@/app/config/constant\";\r\nimport { createInitialsAvatar } from \"@/app/components/InitialAvatar/CreateInitialAvatar\";\r\n\r\nconst chatSuggestions = {\r\n  admin: [\r\n    \"Thank you for your interest! Would you like to buy it?\",\r\n    \"Hi! How can I help you with this item?\",\r\n    \"Yes, it’s still available. Would you like to buy it?\",\r\n    \"Sorry, it’s no longer available.\",\r\n    \"Yes, What you like to offer!\",\r\n  ],\r\n  client: [\r\n    \"Hi, I’m interested!\",\r\n    \"Is this item still available?\",\r\n    \"When would it be available for pick-up/delivery?\",\r\n    \"Do you offer delivery?\",\r\n    \"Are there any defect or issues I should know about?\",\r\n    \"Is the price negotiable?\",\r\n  ],\r\n};\r\n\r\nfunction myMessages() {\r\n  let userData = userDataFromLocal();\r\n  console.log(\"userData\", userData);\r\n  const itemId = useSelector((x) => x?.storeData?.itemId);\r\n  const storeData = useSelector((x) => x?.storeData);\r\n  const fileInputRef = useRef();\r\n  const menuRef = useRef(null);\r\n  const chatRef = useRef(null);\r\n  const messagesEndRef = useRef(null);\r\n  const router = useRouter();\r\n  const [loading, setLoading] = useState(true);\r\n  const [openChat, setOpenChat] = useState(false); // For Mobile purpose\r\n  const [openFilter, setOpenFilter] = useState(false);\r\n  const [chatToggle, setChatToggle] = useState(\"chats\"); // chats , make offer\r\n  const [openChatTemplate, setOpenChatTemplate] = useState(true);\r\n  const [predefinedChatsList, setPredefinedChatsList] = useState([\r\n    \"Hey! Yes, it’s still available 😊\",\r\n    \"No, it's not available\",\r\n  ]);\r\n  const [chatList, setChatList] = useState([]);\r\n  const [chatId, setChatId] = useState(null);\r\n  const [chatCount, setChatCount] = useState(0);\r\n  const [selectedChat, setSelectedChat] = useState({\r\n    itemId: null,\r\n    userId: userData?._id,\r\n    userName: \"\",\r\n    backCoverImage: \"\",\r\n    profileImage: \"\",\r\n    itemPrice: null,\r\n    isSeller: null,\r\n    status: \"\",\r\n  });\r\n\r\n  const [messages, setMessages] = useState([]);\r\n  const [sender, setSender] = useState(null);\r\n  const [searchText, setSearchText] = useState(null);\r\n  const [filter, setFilter] = useState(\"all\"); // all, buyers, sellers\r\n  const [chatListLoading, setChatListLoading] = useState(false);\r\n  const [makeOfferValue, setMakeOfferValue] = useState(\"\");\r\n  const [sellerMakeOfferShownComponent, setSellerMakeOfferShownComponent] =\r\n    useState(\"offer_pitch\"); //  seller -> offer_pitch, rejected_offer, accepted_offer\r\n  const [buyerMakeOfferShownComponent, setBuyerMakeOfferShownComponent] =\r\n    useState(\"send_offer\"); // buyer -> send_offer, edit_offer, counter_offer, rejected_counter_offer, offer_accepted,\r\n  const [itemOfConversation, setitemOfConversation] = useState({});\r\n  const debouncedFetchRef = useRef();\r\n  const [isImageLoaded, setisImageLoaded] = useState(false);\r\n  const [currentPosition, setCurrentPosition] = useState({});\r\n  const [showOptionThreeDot, setshowOptionThreeDot] = useState(false);\r\n  const menuBtnRef = useRef(null);\r\n  const dispatch = useDispatch();\r\n  const queryParams = new URLSearchParams(window.location.search);\r\n  let urlItemId = queryParams.get(\"itemId\");\r\n  console.log(\"urlItemId\", urlItemId);\r\n  // const [showChatBodyCondition,setshowChatBodyCondition]=useState(false)\r\n  console.log(\"itemOfConversation\", itemOfConversation);\r\n  const showMessageCondition =\r\n    chatId || itemId || chatList?.length > 0 || searchText?.length;\r\n  let showChatBodyCondition = chatId || itemId;\r\n  console.log(\"showMessageCondition\", showMessageCondition);\r\n  console.log(\"itemId\", itemId);\r\n  console.log(\"storeData\", storeData);\r\n  console.log(\"chatListLoading\", chatListLoading);\r\n  console.log(\"chatList\", chatList);\r\n  console.log(\"showOptionThreeDot\", showOptionThreeDot);\r\n\r\n  // Scroll Behavior\r\n  console.log(\"sender\", sender);\r\n  useEffect(() => {\r\n    if (showMessageCondition) {\r\n      // Scroll to the bottom when messages update\r\n      chatRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n\r\n    const container = messagesEndRef.current;\r\n    if (container) {\r\n      container.scrollTo({\r\n        top: container.scrollHeight,\r\n        behavior: \"smooth\",\r\n      });\r\n    }\r\n  }, [messages]);\r\n\r\n  // function to open chat in mobile and update the chat id\r\n  const openChatHandler = (chatId) => {\r\n    setChatId(chatId);\r\n    fetchChatById(chatId);\r\n    setOpenChat(true);\r\n  };\r\n\r\n  const closeChatHandler = () => {\r\n    setOpenChat(false);\r\n  };\r\n\r\n  // add the predefined list from the chat\r\n  useEffect(() => {\r\n    if (messages.length < 1) return;\r\n    // console.log(\"PredefinedChats\", PredefinedChats)\r\n    const char = [...messages].reverse().find((m) => {\r\n      if (m?.sender !== sender?._id) return m;\r\n    });\r\n    // console.log(\"char\", char)\r\n    const list = predefinedChats[char?.text];\r\n\r\n    if (list?.length > 0) {\r\n      setPredefinedChatsList(list);\r\n    }\r\n  }, [messages]);\r\n\r\n  // fetch user profile\r\n\r\n  const fetchCurrentLocation = () => {\r\n    let position = navigator.geolocation.getCurrentPosition((position) => {\r\n      console.log(\"position\", position);\r\n      setCurrentPosition({\r\n        lat: position.coords.latitude,\r\n        long: position.coords.longitude,\r\n      });\r\n    });\r\n  };\r\n\r\n  console.log(currentPosition, \"currentPosition\");\r\n\r\n  const fetchProfile = async () => {\r\n    try {\r\n      let userToken = getToken();\r\n      let userData = userDataFromLocal();\r\n      let responseUserInfo = await userInfo_api();\r\n\r\n      setSender(userData);\r\n      // fetch(USER_ROUTES.USER_INFO, {\r\n      //     method: \"get\",\r\n      //     headers: {\r\n      //         \"Authorization\": `Bearer ${userToken}`\r\n      //     }\r\n      // }).then(async res => {\r\n      //     const response = await res.json();\r\n      //     console.log(\"response is\", response)\r\n      //     if (!response.error) {\r\n      //         let userData = response.data;\r\n      //         setSender(userData)\r\n      //     } else {\r\n      //         toast.error(response.message || \"No Info Found\")\r\n      //     }\r\n      // })\r\n    } catch (error) {\r\n      console.log(error);\r\n      toast.error(error || \"Internal Server Error\");\r\n    }\r\n  };\r\n\r\n  // fetch item data\r\n  const fetchItemData = async () => {\r\n    try {\r\n      let userToken = getToken();\r\n      let searchData = await getBooksById(itemId);\r\n      console.log(\"searchData fetchItemData\", searchData);\r\n      if (searchData.status == 200) {\r\n        setitemOfConversation(searchData.data);\r\n\r\n        setSelectedChat((prev) => ({\r\n          ...prev,\r\n          userId: searchData?.data?.createdBy?._id,\r\n          itemId: searchData?.data?._id,\r\n          backCoverImage:\r\n            searchData?.data?.images.length && searchData?.data?.images[0],\r\n          profileImage: searchData?.data?.createdBy?.profileImage,\r\n          userName: `${searchData?.data?.createdBy?.firstName} ${searchData?.data?.createdBy?.lastName}`,\r\n          isSeller: userData._id == searchData.data?.createdBy?._id,\r\n          status: searchData?.data.status,\r\n        }));\r\n        // setChatToggle(userData._id == searchData.data?.createdBy?._id?\"chats\":\"make offer\")\r\n        setSelectedChat((prev) => {\r\n          return { ...prev, itemPrice: searchData.data.price };\r\n        });\r\n        setMakeOfferValue(`${searchData.data.price}`);\r\n        // setSelectedChat(searchData.data?.coverImage)\r\n      }\r\n    } catch (error) {\r\n      console.log(\"error\", error);\r\n    }\r\n  };\r\n\r\n  // fetch chat list in the left side [ search and filters ]\r\n  const fetchChatList = async (searchTerm, filterText) => {\r\n    try {\r\n      setChatListLoading(true);\r\n      let userToken = getToken();\r\n\r\n      const payloadData = {};\r\n      if (searchTerm) payloadData[\"searchTerm\"] = searchTerm;\r\n      if (filterText) payloadData[\"chatType\"] = filterText;\r\n      let allChat = await getAllChat(payloadData);\r\n\r\n      console.log(\"allChat\", allChat);\r\n      if (allChat.status == 200) {\r\n        let chatIdToOpen = \"\";\r\n        let nonDeletedData = allChat?.data?.filter((item) => {\r\n          if (itemId === item.sellerItemId) {\r\n            chatIdToOpen = item._id;\r\n          }\r\n          return !item.deletedBy.includes(userData._id);\r\n        });\r\n        console.log(\"chatIdToOpen\", chatIdToOpen);\r\n        if (chatIdToOpen) {\r\n          fetchChatById(chatIdToOpen);\r\n        }\r\n        setChatList(nonDeletedData);\r\n        if (itemId) {\r\n          for (let index = 0; index < nonDeletedData.length; index++) {\r\n            if (storeData.chatId == nonDeletedData[index]._id) {\r\n              // fetchChatById(nonDeletedData[index]._id)\r\n            }\r\n          }\r\n        }\r\n        setLoading(false);\r\n      } else {\r\n        toast.error(allChat?.message || \"Internal Server Error\");\r\n        setLoading(false);\r\n      }\r\n      setChatListLoading(false);\r\n      // fetch(CHAT_ROUTES.ALL_CHATS, {\r\n      //     method: \"POST\",\r\n      //     headers: {\r\n      //         \"Content-Type\": \"application/json\",\r\n      //         \"Authorization\": `Bearer ${userToken}`\r\n      //     },\r\n      //     body: JSON.stringify(payloadData)\r\n      // }).then(async res => {\r\n      //     const response = await res.json();\r\n      //     let chatData = response\r\n      //     if (!chatData?.error) {\r\n      //         console.log(\"chat list Data\", chatData)\r\n      //         setChatList(chatData?.data)\r\n      //         setLoading(false)\r\n      //         setChatListLoading(false)\r\n      //     } else {\r\n      //         toast.error(response?.message || \"Internal Server Error\")\r\n      //         setLoading(false)\r\n      //         setChatListLoading(false)\r\n      //     }\r\n      // })\r\n    } catch (error) {\r\n      console.log(\"error\", error);\r\n      setLoading(false);\r\n      setChatListLoading(false);\r\n    }\r\n  };\r\n  console.log(\"compare\", selectedChat?.userId, \"userId\", userData?._id);\r\n  // fetch the selected chat data like conversation, participants and messages data\r\n  const fetchChatById = async (chatId) => {\r\n    try {\r\n      // getAllChat()\r\n      let userToken = getToken();\r\n      let chatData = await getChatById(chatId);\r\n      console.log(\"chatData getChatById\", chatData);\r\n      if (chatData.status == 200) {\r\n        setitemOfConversation(chatData.data.conversation[0].item[0]);\r\n        setChatCount(chatData?.data?.count);\r\n        const conversation = chatData?.data?.conversation?.[0];\r\n\r\n        const participant1 = conversation?.participants_docs?.[0];\r\n        const participant2 = conversation?.participants_docs?.[1];\r\n        console.log(\"participant1\", participant1);\r\n        console.log(\"participant2\", participant2);\r\n\r\n        const receiver =\r\n          userData?._id == participant1._id ? participant2 : participant1;\r\n\r\n        console.log(\"receiver fetch\", receiver);\r\n\r\n        setSelectedChat((prev) => ({\r\n          ...prev,\r\n          conversationId: conversation?._id,\r\n          itemId: conversation?.sellerItemId,\r\n          userId: receiver?._id,\r\n          userName: receiver?.firstName + \" \" + receiver?.lastName,\r\n          profileImage: receiver?.profileImage,\r\n          backCoverImage:\r\n            conversation?.item?.[0]?.images.length &&\r\n            conversation?.item?.[0]?.images[0],\r\n          itemPrice: conversation?.item?.[0]?.price,\r\n          isSeller: conversation?.item?.[0]?.createdBy === userData._id,\r\n          status: conversation?.item?.[0]?.status,\r\n        }));\r\n        // setChatToggle(conversation?.item?.[0]?.createdBy === userData._id?\"chats\":\"make offer\")\r\n        // messages\r\n        setMessages(chatData?.data?.messages);\r\n      }\r\n    } catch (error) {\r\n      console.log(\"error\", error);\r\n    }\r\n  };\r\n\r\n  // update user profile of chat types consistency\r\n  const updateProfile = async (chatType) => {\r\n    try {\r\n      const token = getToken();\r\n      const payload = {};\r\n      if (chatType === \"sellers\") payload[\"chatType\"] = chatType;\r\n      else return;\r\n      //do not remove the comment\r\n      // await update_userInfo_api({ selectedChatFilters: payload });\r\n    } catch (error) {\r\n      console.log(\"error\", error);\r\n    }\r\n  };\r\n\r\n  // fetching data's\r\n  useEffect(() => {\r\n    // fetch item data only when the user came from the search path and there is no chat with that user\r\n    if (itemId) {\r\n      fetchItemData();\r\n      setOpenChat(true);\r\n    }\r\n\r\n    fetchCurrentLocation();\r\n    fetchChatList();\r\n    fetchProfile();\r\n    const handleClickOutside = (event) => {\r\n      if (menuRef.current && !menuRef.current.contains(event.target)) {\r\n        setshowOptionThreeDot(false);\r\n      }\r\n    };\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  // Initialize debounced function once\r\n  useEffect(() => {\r\n    debouncedFetchRef.current = Debounce(fetchChatList, 500);\r\n    const openCloseMenu = (e) => {\r\n      if (menuBtnRef.current && !menuBtnRef.current.contains(e.target)) {\r\n        setOpenFilter(false);\r\n      }\r\n    };\r\n    document.addEventListener(\"click\", openCloseMenu);\r\n    return () => {\r\n      debouncedFetchRef.current?.cancel();\r\n      document.removeEventListener(\"click\", openCloseMenu);\r\n\r\n      // dispatch(nullItemId())\r\n    };\r\n  }, []);\r\n\r\n  // filter or search chat list\r\n  useEffect(() => {\r\n    if (debouncedFetchRef.current) {\r\n      debouncedFetchRef.current(searchText, filter);\r\n    }\r\n  }, [searchText, filter]);\r\n\r\n  // socket connection check\r\n  useEffect(() => {\r\n    console.log(\"socket\", socket);\r\n    socket.on(\"connect\", () => {\r\n      console.log(\"Connected to socket\");\r\n      console.log(\"socket inside\", socket);\r\n    });\r\n    socket.on(\"disconnect\", () => {\r\n      console.log(\"disconnected to socket\");\r\n    });\r\n    socket.on(\"connect_error\", (err) => {\r\n      console.log(\"Connect error:\", err.message); // Example: \"xhr poll error\", \"WebSocket is closed before the connection is established\"\r\n    });\r\n  }, []);\r\n\r\n  // socket message receive\r\n  useEffect(() => {\r\n    const handleMessage = (data) => {\r\n      console.log(\"data on message\", data);\r\n      // appending the message in the message array\r\n      setMessages((prev) => [...prev, data.message]);\r\n      let nonDeletedData = data?.chats?.filter((item) => {\r\n        return !item.deletedBy.includes(userData._id);\r\n      });\r\n      setChatList(nonDeletedData);\r\n    };\r\n\r\n    socket.on(\"on-message\", handleMessage);\r\n    socket.on(\"connect_error\", (err) => {\r\n      console.error(\"Socket connection error:\", err.message);\r\n    });\r\n    socket.on(\"connect\", () => {\r\n      console.log(\"Connected to socket server:\", socket.id);\r\n    });\r\n\r\n    // Cleanup the listener on unmount or rerun\r\n    return () => {\r\n      socket.off(\"on-message\", handleMessage);\r\n    };\r\n  }, []);\r\n  const avatarUrl = (userName) => {\r\n    return createInitialsAvatar(`${userName}`, {\r\n      bgColor: \"#3f51b5\",\r\n      textColor: \"#ffffff\",\r\n    });\r\n  };\r\n  console.log(\"itemId\", itemId);\r\n  console.log(\"selectedChat\", selectedChat);\r\n  console.log(\"sender\", sender);\r\n  console.log(\"messages\", messages);\r\n\r\n  // socket event to send message\r\n  const sendMessage = (message, mediaType, mediaUrl) => {\r\n    if (!message || message.trim().length === 0) return;\r\n\r\n    let data = {\r\n      recipientId: selectedChat.userId || itemOfConversation.createdBy._id,\r\n      sellerItemId: selectedChat?.itemId || itemId,\r\n      message: {\r\n        text: message.trim(),\r\n      },\r\n    };\r\n\r\n    if (mediaUrl) {\r\n      data.message[mediaType] = mediaType;\r\n      data.message[mediaUrl] = mediaUrl;\r\n    }\r\n    socket.emit(\"send-message\", data);\r\n  };\r\n\r\n  const imageSend = (mediaUrl) => {\r\n    let data = {\r\n      recipientId: selectedChat.userId || itemOfConversation.createdBy._id,\r\n      sellerItemId: selectedChat?.itemId || itemId,\r\n      message: {\r\n        mediaType: \"image\",\r\n        mediaUrl: mediaUrl,\r\n        isMedia: true,\r\n      },\r\n    };\r\n    socket.emit(\"send-message\", data);\r\n  };\r\n\r\n  const locationSend = (mediaUrl) => {\r\n    console.log(\"inside locationSend\");\r\n    navigator.permissions\r\n      ?.query({ name: \"geolocation\" })\r\n      .then(function (result) {\r\n        console.log(\"Permission status:\", result.state);\r\n      });\r\n    let position = navigator.geolocation.getCurrentPosition((position) => {\r\n      console.log(\"position\", position);\r\n      if (!position.coords) {\r\n        return;\r\n      }\r\n      setCurrentPosition({\r\n        lat: position.coords.latitude,\r\n        long: position.coords.longitude,\r\n      });\r\n      let data = {\r\n        recipientId: selectedChat.userId || itemOfConversation.createdBy._id,\r\n        sellerItemId: selectedChat?.itemId || itemId,\r\n        message: {\r\n          mediaType: \"location\",\r\n          // \"mediaUrl\": mediaUrl,\r\n          // \"isMedia\": true,\r\n          text: JSON.stringify({\r\n            lat: position.coords.latitude,\r\n            long: position.coords.longitude,\r\n          }),\r\n        },\r\n      };\r\n      socket.emit(\"send-message\", data);\r\n    });\r\n  };\r\n\r\n  // sending message\r\n  const sendMessageHandler = (e) => {\r\n    e.preventDefault();\r\n\r\n    // Send message\r\n    sendMessage(e.target.message.value);\r\n\r\n    // clear the message\r\n    e.target.message.value = \"\";\r\n  };\r\n\r\n  const filterHandler = (filterText) => {\r\n    setFilter(filterText);\r\n    updateProfile(filterText);\r\n    setOpenFilter(false);\r\n    setChatId(null);\r\n    setOpenChat(false); // close chat for mobile\r\n  };\r\n\r\n  // chat template open handler\r\n  const chatTemplateOpenHandler = () => {\r\n    setOpenChatTemplate(!openChatTemplate);\r\n    const container = messagesEndRef.current;\r\n    // if (container) {\r\n    //   container.scrollTo({\r\n    //     top: container.scrollHeight,\r\n    //     behavior: \"smooth\",\r\n    //   });\r\n    // }\r\n  };\r\n\r\n  let prefix = \"J$ \"; // default prefix\r\n  // handling the default prefix will not get send, without price cannot send something\r\n  const makeOfferInputHandler = (e) => {\r\n    let inputValue = e.target.value;\r\n    // Remove prefix if user tries to type it again\r\n    if (inputValue.startsWith(prefix)) {\r\n      inputValue = inputValue.slice(prefix.length);\r\n    } else {\r\n      // If user tries to delete the prefix entirely, ignore that change\r\n      inputValue = inputValue.replace(/[^0-9]/g, \"\");\r\n    }\r\n\r\n    // Allow only digits\r\n    const numeric = inputValue.replace(/[^0-9]/g, \"\");\r\n\r\n    setMakeOfferValue(inputValue);\r\n  };\r\n  console.log(\"makeOfferValue\", makeOfferValue);\r\n  // Send Offer price by buyer\r\n  const sendMakeOfferHandler = (e) => {\r\n    e.preventDefault();\r\n    let sliceData;\r\n    if (makeOfferValue.includes(prefix)) {\r\n      sliceData = makeOfferValue.slice(prefix.length);\r\n    } else {\r\n      sliceData = makeOfferValue;\r\n    }\r\n    console.log(sliceData, \"sliceData\");\r\n    // if (sliceData < selectedChat.itemPrice) {\r\n    //     toast.error(\"Can Not Quote Less Then Price\")\r\n    //     return\r\n    // }\r\n\r\n    if (makeOfferValue === prefix) return;\r\n    let sendOfferText = \"J$\" + makeOfferValue;\r\n    sendMessage(sendOfferText);\r\n    // setMakeOfferValue(\"J$ \")\r\n  };\r\n\r\n  // handling the make offer component shown to the buyer or seller\r\n  // useEffect(() => {\r\n  //     if (selectedChat?.isSeller) {\r\n  //         // last message of price\r\n  //         const priceRegex = new RegExp(`^${prefix}/[^0-9]/g`);\r\n\r\n  //         const lastPriceMessageByBuyer = messages?.toReversed().find(m => {\r\n  //             const text = m.text || \"\"; // assuming messages have a `text` field\r\n  //             console.log(\"text\", text)\r\n  //             const isPrice = /^J\\$ \\d+(\\.\\d{1,2})?$/.test(text.trim())\r\n  //             console.log(\"isPrice\", isPrice)\r\n  //             if (isPrice) return m;\r\n  //         });\r\n\r\n  //         console.log(\"lastPriceMessageByBuyer\", lastPriceMessageByBuyer)\r\n\r\n  //         // To extract the actual price if found\r\n  //         const price = lastPriceMessageByBuyer\r\n  //             ? parseFloat(lastPriceMessageByBuyer.text.trim().match(priceRegex)[1])\r\n  //             : null;\r\n\r\n  //         console.log(\"price\", price)\r\n  //     }\r\n  // }, [messages])\r\n\r\n  const handshakeSvg = (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width=\"28\"\r\n      height=\"15\"\r\n      viewBox=\"0 0 34 20\"\r\n      fill=\"none\"\r\n      className=\"md:w-[34px] md:h-5\"\r\n    >\r\n      <path\r\n        d=\"M22.6324 0H18.1601C17.7436 0 17.3427 0.156193 17.0355 0.437341L11.9176 5.12314C11.9123 5.12835 11.9071 5.13876 11.9019 5.14397C11.0377 5.95617 11.0533 7.25258 11.7926 8.05958C12.4538 8.78327 13.8439 8.97591 14.7134 8.20015C14.7186 8.19494 14.729 8.19494 14.7342 8.18974L18.8942 4.37862C19.2326 4.07144 19.7637 4.09227 20.0708 4.43069C20.3832 4.7691 20.3572 5.29495 20.0188 5.60734L18.6599 6.85168L26.2405 13.0057C26.3915 13.1307 26.5268 13.266 26.6518 13.4066V3.33212L23.8091 0.489406C23.5019 0.177019 23.075 0 22.6324 0ZM28.3231 3.34254V14.9998C28.3231 15.9213 29.0676 16.6658 29.9891 16.6658H33.3212V3.34254H28.3231ZM30.8222 14.9998C30.364 14.9998 29.9891 14.6249 29.9891 14.1667C29.9891 13.7086 30.364 13.3337 30.8222 13.3337C31.2803 13.3337 31.6552 13.7086 31.6552 14.1667C31.6552 14.6249 31.2803 14.9998 30.8222 14.9998ZM0 16.6606H3.33212C4.25367 16.6606 4.99819 15.9161 4.99819 14.9946V3.34254H0V16.6606ZM2.49909 13.3337C2.95726 13.3337 3.33212 13.7086 3.33212 14.1667C3.33212 14.6249 2.95726 14.9998 2.49909 14.9998C2.04093 14.9998 1.66606 14.6249 1.66606 14.1667C1.66606 13.7034 2.04093 13.3337 2.49909 13.3337ZM25.194 14.3021L17.4208 7.99189L15.8588 9.42367C14.3125 10.8346 11.9436 10.6992 10.5639 9.19458C9.16334 7.66389 9.27268 5.29495 10.793 3.89963L15.0518 0H10.6888C10.2463 0 9.82456 0.177019 9.51217 0.489406L6.66425 3.33212V14.9894H7.61703L12.3289 19.2534C13.7554 20.4145 15.8536 20.1958 17.0147 18.7692L17.0251 18.7588L17.957 19.5658C18.7849 20.2427 20.0084 20.1125 20.68 19.2847L22.3148 17.275L22.596 17.5041C23.3093 18.082 24.361 17.9779 24.9389 17.2594L25.4335 16.6502C26.0166 15.9317 25.9073 14.8852 25.194 14.3021Z\"\r\n        fill=\"url(#paint0_linear_2930_11490)\"\r\n      />\r\n      <defs>\r\n        <linearGradient\r\n          id=\"paint0_linear_2930_11490\"\r\n          x1=\"0\"\r\n          y1=\"10\"\r\n          x2=\"33.3212\"\r\n          y2=\"10\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#E1020C\" />\r\n          <stop offset=\"1\" stopColor=\"#4D7906\" />\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  );\r\n\r\n  const defaultSellerComponent = (\r\n    title,\r\n    price,\r\n    para,\r\n    btn1,\r\n    btn2,\r\n    rejected = false,\r\n    rejectedPrice = \"\",\r\n    showBtn = true,\r\n    showIcon = false\r\n  ) => (\r\n    <div className=\"flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5\">\r\n      {rejected && (\r\n        <div className=\"flex flex-row gap-[5px] mb-[5px]\">\r\n          <p className=\"text-xs leading-normal uppercase\">\r\n            Buyer’s offer: J${rejectedPrice}\r\n          </p>\r\n          <span className=\"text-xs leading-normal uppercase text-[#FF2929]\">\r\n            Rejected\r\n          </span>\r\n        </div>\r\n      )}\r\n\r\n      <p className=\"text-xs leading-normal uppercase\">{title}</p>\r\n\r\n      <div className=\"flex justify-center items-center gap-2.5\">\r\n        <p className=\"my-[1px] text-[28px] capitalize font-bold leading-normal global_text_linear_gradient\">\r\n          J$ {price}\r\n        </p>\r\n        {showIcon && handshakeSvg}\r\n      </div>\r\n\r\n      <p className=\"capitalize text-xs leading-normal\">{para}</p>\r\n\r\n      {showBtn && (\r\n        <div className=\"w-full flex justify-between gap-2 mt-[5px]\">\r\n          <button className=\"py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full\">\r\n            {btn1}\r\n          </button>\r\n\r\n          <button className=\"py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full\">\r\n            {btn2}\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  const offerInput = (\r\n    <form onSubmit={sendMakeOfferHandler} className=\"flex justify-between\">\r\n      <div className=\"w-[50%]\">\r\n        <div className=\"w-full flex items-stretch my-2 \">\r\n          {/* Left Prefix */}\r\n          <div className=\"bg-[#C0DEFB] pl-2  text-[12px] rounded-l-md flex items-center text-sm font-medium\">\r\n            J$\r\n          </div>\r\n\r\n          {/* Input Field */}\r\n          <input\r\n            type=\"text\"\r\n            autoComplete=\"off\"\r\n            className=\"w-full  bg-[#C0DEFB] pr-2 py-2 text-xs font-medium leading-normal rounded-r-md focus:outline-none\"\r\n            value={makeOfferValue}\r\n            placeholder=\"Enter Price\"\r\n            min={itemOfConversation.price}\r\n            onChange={makeOfferInputHandler}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"w-full flex justify-between gap-2.5 items-center\">\r\n          {makeOfferValue < selectedChat.itemPrice ? (\r\n            <div className=\" w-full bg-[#f00711] py-[3px] px-[15px] rounded-sm\">\r\n              <p className=\"text-xs md:text-sm text-white font-medium leading-normal\">\r\n                Too low to offer\r\n              </p>\r\n              <p className=\"text-[10px] md:text-xs leading-normal text-[white]\">\r\n                Less chance of seller’s reply.\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"w-full global_linear_gradient py-[3px] px-[15px] rounded-sm\">\r\n              <p className=\"text-xs md:text-sm text-white font-medium leading-normal\">\r\n                Very good offer\r\n              </p>\r\n              <p className=\"text-[10px] md:text-xs leading-normal text-[#D8D8D8]\">\r\n                High chance of seller’s reply.\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <div className=\"flex flex-col justify-end align-end\">\r\n        <button\r\n          type=\"submit\"\r\n          className=\"uppercase text-xs md:text-sm text-white py-2.5 px-[30px] global_linear_gradient rounded-full\"\r\n        >\r\n          SEND\r\n        </button>\r\n      </div>\r\n    </form>\r\n  );\r\n\r\n  const makeOfferComponents = {\r\n    buyer: {\r\n      // Send Offer with input\r\n      send_offer: (\r\n        <div className=\"w-full py-2 px-5 md:px-7\">\r\n          <div className=\"overflow-x-auto flex gap-3.5\">\r\n            {new Array(4).fill(0).map((_, idx) => {\r\n              const basePrice = selectedChat?.itemPrice ?? 0;\r\n              const price = Math.round(basePrice * Math.pow(1.2, idx));\r\n\r\n              return (\r\n                <span\r\n                  key={idx}\r\n                  className=\"w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-sm text-white gradient-all-round-border border-2 lg:py-[6.5px] lg:px-[22px] lg:text-sm font-semibold cursor-pointer active:scale-95\"\r\n                  onClick={() => sendMessage(`J$ ${price}`)}\r\n                >\r\n                  <p className=\"global_text_linear_gradient\">J$ {price}</p>\r\n                </span>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {offerInput}\r\n        </div>\r\n      ),\r\n\r\n      // Edit Offer\r\n      edit_offer: (\r\n        <div className=\"pt-2 pb-1 px-5 flex flex-col justify-center items-center w-full\">\r\n          <p className=\"uppercase text-xs leading-normal\">YOUR OFFER</p>\r\n          <p className=\"py-[1px] text-[28px] capitalize font-bold global_text_linear_gradient\">\r\n            J$ 440\r\n          </p>\r\n          <p className=\"text-xs capitalize leading-normal\">\r\n            waiting for seller’s response.\r\n          </p>\r\n          <button className=\"uppercase text-sm leading-normal py-2.5 md:py-[13px] px-5 global_linear_gradient rounded-full w-full text-white mt-2.5 tracking-[0.7px]\">\r\n            Edit offer\r\n          </button>\r\n        </div>\r\n      ),\r\n\r\n      // counter offer\r\n      counter_offer: (\r\n        <div className=\"flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5\">\r\n          <p className=\"text-xs leading-normal uppercase\">\r\n            Seller’s counter offer\r\n          </p>\r\n          <p className=\"my-[1px] text-[28px] capitalize font-bold leading-normal global_text_linear_gradient\">\r\n            J$ 440\r\n          </p>\r\n          <p className=\"capitalize text-xs leading-normal\">\r\n            Buyer waiting for your response\r\n          </p>\r\n\r\n          <div className=\"w-full flex justify-between gap-2 mt-[5px]\">\r\n            <button className=\"py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full\">\r\n              Make new offer\r\n            </button>\r\n\r\n            <button className=\"py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full\">\r\n              Let’s go ahead\r\n            </button>\r\n          </div>\r\n        </div>\r\n      ),\r\n\r\n      // Rejected than make offer\r\n      rejected_counter_offer: (\r\n        <div className=\"py-2 px-5 md:px-7 w-full\">\r\n          <div className=\"flex gap-2 md:gap-3.5 w-full\">\r\n            <div className=\"global_linear_gradient py-[7px] px-2.5 md:px-4 text-center text-xs md:text-lg leading-normal font-semibold text-white rounded-sm w-[52%]\">\r\n              J$ 435{\" \"}\r\n              <span className=\"text-[#FF2929] font-medium\">Rejected</span>\r\n            </div>\r\n\r\n            <div className=\"global_linear_gradient py-[7px] px-2.5 md:px-4 text-center text-xs md:text-lg leading-normal font-semibold text-white rounded-sm w-1/2\">\r\n              Seller’s Offer J$ 440\r\n            </div>\r\n          </div>\r\n\r\n          {offerInput}\r\n        </div>\r\n      ),\r\n\r\n      // offer accepted\r\n      offer_accepted: (\r\n        <div className=\"flex flex-col items-center pt-2.5 pb-2 px-5 md:px-8\">\r\n          <p className=\"text-xs leading-normal uppercase\">offer accepted</p>\r\n          <div className=\"flex gap-2.5 items-center\">\r\n            <p className=\"text-[22px] md:text-[28px] leading-normal capitalize font-bold global_text_linear_gradient\">\r\n              J$ 440\r\n            </p>\r\n            {handshakeSvg}\r\n          </div>\r\n          <p className=\"text-xs leading-normal capitalize\">\r\n            now you’re one step closer to thr final deal.\r\n          </p>\r\n\r\n          <button className=\"global_linear_gradient mt-2.5 rounded-full border border-[#0161AB] w-full text-white py-[13px] px-5 uppercase text-sm tracking-[0.7px] leading-normal\">\r\n            let’s meet\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n\r\n    seller: {\r\n      offer_pitch:\r\n        // <div className='flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5'>\r\n        defaultSellerComponent(\r\n          \"Buyer’s Offer\",\r\n          440,\r\n          \"Buyer waiting for your response\",\r\n          \"Make new offer\",\r\n          \"Let’s go ahead\"\r\n        ),\r\n      // </div>\r\n\r\n      rejected_offer: defaultSellerComponent(\r\n        \"Your Counter offer\",\r\n        440,\r\n        \"waiting for buyer’s response\",\r\n        \"\",\r\n        \"\",\r\n        true,\r\n        440,\r\n        false\r\n      ),\r\n\r\n      accepted_offer: defaultSellerComponent(\r\n        \"offer accepted\",\r\n        440,\r\n        \"now you’re one step closer to thr final deal.\",\r\n        \"Let’s meet\",\r\n        \"Ask contact\",\r\n        false,\r\n        \"\",\r\n        true,\r\n        true\r\n      ),\r\n    },\r\n  };\r\n  console.log(\"showChatBodyCondition\", showChatBodyCondition);\r\n  async function deleteConversation(id) {\r\n    let deleteResponse = await deleteChatById(id);\r\n    console.log(\"deleteResponse\", deleteResponse);\r\n    if (deleteResponse.status == 200) {\r\n      toast.success(\"Deleted Successfully\");\r\n      // showChatBodyCondition=false\r\n      dispatch(updateItemId(\"\"));\r\n      setChatId(null);\r\n      fetchChatList();\r\n    } else {\r\n      toast.error(deleteResponse?.data?.message || \"Something Went Wrong\");\r\n    }\r\n  }\r\n\r\n  const handleButtonClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  let str = \"image/png\";\r\n  console.log(str.includes(\"image\"), \"includes\");\r\n  const handleFileChange = async (e) => {\r\n    console.log(\"event\", e.target.files);\r\n\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      let isImage = file.type.includes(\"image\");\r\n      if (!isImage) {\r\n        toast.error(\"Only Images Are Allowed\");\r\n        return;\r\n      }\r\n      // setLoading(true)\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      try {\r\n        const data = await uploadPhotoSingle(formData);\r\n        console.log(\"data uploadPhotoSingle\", data);\r\n        if (data.status == 200) {\r\n          imageSend(data.data.url);\r\n        }\r\n      } catch (err) {\r\n        console.log(\"err in file chjange\", err);\r\n      }\r\n    }\r\n  };\r\n\r\n  const ChatMessage = ({ lat, long }) => {\r\n    if (\r\n      typeof lat !== \"number\" ||\r\n      typeof long !== \"number\" ||\r\n      isNaN(lat) ||\r\n      isNaN(long)\r\n    ) {\r\n      console.error(\"Invalid coordinates:\", lat, long);\r\n      return null;\r\n    }\r\n    console.log(\"ChatMessage\", lat, long);\r\n    const mapUrl = `https://www.google.com/maps?q=${lat},${long}&z=15&output=embed`;\r\n\r\n    return (\r\n      <div\r\n        className=\"w-full max-w-[150px] h-[150px] rounded overflow-hidden border shadow\"\r\n        onClick={() => window.open(mapUrl)}\r\n      >\r\n        <iframe\r\n          src={mapUrl}\r\n          width=\"100%\"\r\n          height=\"100%\"\r\n          allowFullScreen=\"\"\r\n          loading=\"lazy\"\r\n        ></iframe>\r\n      </div>\r\n    );\r\n\r\n    // For normal text messages\r\n  };\r\n\r\n  let mediaType = { image: \"image\", location: \"location\" };\r\n  const RenderMessage = (message) => {\r\n    if (mediaType.image == message.mediaType) {\r\n      return (\r\n        <img\r\n          className={`w-[100px] h-[100px] rounded-md object-cover  transition-opacity duration-300 ${\r\n            isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n          }`}\r\n          src={message.mediaUrl}\r\n          onLoad={() => setisImageLoaded(true)}\r\n        />\r\n      );\r\n    } else if (mediaType.location == message.mediaType) {\r\n      console.log();\r\n      return ChatMessage(JSON.parse(message.text));\r\n    } else {\r\n      return (\r\n        <p className=\"py-4 px-6 global_linear_gradient rounded-tl-[32px] rounded-tr-[32px] rounded-bl-[32px] text-xs leading-normal text-white w-fit hyphens-auto wrap-anywhere overflow-hidden\">\r\n          {message?.text}\r\n        </p>\r\n      );\r\n    }\r\n  };\r\n\r\n  let lastMessageText = (chat) => {\r\n    if (chat.lastMessage.mediaType == mediaType.location) {\r\n      return (\r\n        <p className=\"mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]\">\r\n          Map\r\n        </p>\r\n      );\r\n    } else if (chat.lastMessage.mediaType == mediaType.image) {\r\n      return (\r\n        <p className=\"mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]\">\r\n          Image\r\n        </p>\r\n      );\r\n    } else {\r\n      return (\r\n        <p className=\"mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]\">\r\n          {chat?.lastMessage?.text}\r\n        </p>\r\n      );\r\n    }\r\n  };\r\n  console.log(\"makeOfferValue\", makeOfferValue);\r\n  console.log(\"openChat\", openChat);\r\n  let elseImage =\r\n    \"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/03df84d0-59d5-4991-977a-f9ab67fae5eb.jpg\";\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\" h-[500px] w-full flex justify-center items-center bg-white \">\r\n        <Circles\r\n          height=\"80\"\r\n          width=\"80\"\r\n          color=\"#4fa94d\"\r\n          ariaLabel=\"circles-loading\"\r\n          wrapperStyle={{}}\r\n          wrapperClass=\"\"\r\n          visible={true}\r\n        />\r\n      </div>\r\n    );\r\n  } else\r\n    return (\r\n      <div className={`${messagesComponentScss.myMessagesContainer} w-full`}>\r\n        <div>\r\n          {/* Header */}\r\n          <div\r\n            className={` ${\r\n              openChat ? \"hidden\" : \"flex\"\r\n            } lg:flex justify-between items-center px-2.5`}\r\n          >\r\n            <h3 className=\"text-lg md:text-2xl font-semibold leading-normal\">\r\n              My Messages\r\n            </h3>\r\n\r\n            <footer className=\"flex justify-center items-center\">\r\n              <Link href=\"/search\" aria-label=\"View all book categories\">\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  width=\"91\"\r\n                  height=\"34\"\r\n                  viewBox=\"0 0 91 34\"\r\n                  fill=\"none\"\r\n                  className=\"lg:hidden\"\r\n                >\r\n                  <path\r\n                    d=\"M87.5156 17C87.5156 9.26817 80.448 3.00029 71.7296 3.00029L18.7857 3.00028C10.0673 3.00028 2.99966 9.26817 2.99966 17\"\r\n                    stroke=\"#211F54\"\r\n                    strokeWidth=\"5.49989\"\r\n                  />\r\n                  <path\r\n                    d=\"M87.5156 17C87.5156 9.26817 80.473 3.00029 71.7854 3.00029L19.0285 3.00028\"\r\n                    stroke=\"#0161AB\"\r\n                    strokeWidth=\"5.49989\"\r\n                  />\r\n                  <path\r\n                    d=\"M3 17C3 24.7318 10.0676 30.9997 18.786 30.9997H71.7299C80.4483 30.9997 87.516 24.7318 87.516 17\"\r\n                    stroke=\"#EFDC2A\"\r\n                    strokeWidth=\"5.49989\"\r\n                  />\r\n                  <path\r\n                    d=\"M19.0293 30.9997H71.7861C80.4737 30.9997 87.5164 24.7318 87.5164 17\"\r\n                    stroke=\"#0161AB\"\r\n                    strokeWidth=\"5.49989\"\r\n                  />\r\n                  <path\r\n                    d=\"M71.7305 3L18.7866 3\"\r\n                    stroke=\"#FF0009\"\r\n                    strokeWidth=\"5.49989\"\r\n                  />\r\n                  <path\r\n                    d=\"M71.7305 31L18.7866 31\"\r\n                    stroke=\"#4A8B40\"\r\n                    strokeWidth=\"5.49989\"\r\n                  />\r\n                  <rect\r\n                    x=\"5.85938\"\r\n                    y=\"3.91406\"\r\n                    width=\"78.5871\"\r\n                    height=\"25.714\"\r\n                    rx=\"12.857\"\r\n                    fill=\"white\"\r\n                  />\r\n\r\n                  <text\r\n                    x=\"50%\"\r\n                    y=\"50%\"\r\n                    dominantBaseline=\"middle\"\r\n                    textAnchor=\"middle\"\r\n                    fontSize=\"12\"\r\n                    fill=\"#211F54\"\r\n                    fontWeight=\"500\"\r\n                    fontFamily=\"Poppins, sans-serif\"\r\n                  >\r\n                    Explore\r\n                  </text>\r\n                </svg>\r\n\r\n                <svg\r\n                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                  width=\"132\"\r\n                  height=\"54\"\r\n                  viewBox=\"0 0 132 54\"\r\n                  fill=\"none\"\r\n                  className=\"hidden lg:inline\"\r\n                >\r\n                  <path\r\n                    d=\"M127.527 26.9922C127.527 14.8465 117.281 5.00045 104.642 5.00045L27.8874 5.00044C15.2481 5.00044 5.00188 14.8465 5.00188 26.9922\"\r\n                    stroke=\"#211F54\"\r\n                    strokeWidth=\"8.63962\"\r\n                  />\r\n                  <path\r\n                    d=\"M127.527 26.9922C127.527 14.8465 117.317 5.00045 104.723 5.00044L28.2395 5.00044\"\r\n                    stroke=\"#0161AB\"\r\n                    strokeWidth=\"8.63962\"\r\n                  />\r\n                  <path\r\n                    d=\"M5.00391 26.9922C5.00391 39.1379 15.2501 48.9839 27.8894 48.9839H104.644C117.283 48.9839 127.529 39.1379 127.529 26.9922\"\r\n                    stroke=\"#EFDC2A\"\r\n                    strokeWidth=\"8.63962\"\r\n                  />\r\n                  <path\r\n                    d=\"M28.2422 48.9839H104.725C117.32 48.9839 127.53 39.1379 127.53 26.9922\"\r\n                    stroke=\"#0161AB\"\r\n                    strokeWidth=\"8.63962\"\r\n                  />\r\n                  <path\r\n                    d=\"M104.645 5L27.8901 4.99999\"\r\n                    stroke=\"#FF0009\"\r\n                    strokeWidth=\"8.63962\"\r\n                  />\r\n                  <path\r\n                    d=\"M104.645 48.9844L27.8901 48.9844\"\r\n                    stroke=\"#4A8B40\"\r\n                    strokeWidth=\"8.63962\"\r\n                  />\r\n                  <rect\r\n                    x=\"9.14844\"\r\n                    y=\"6.43359\"\r\n                    width=\"113.93\"\r\n                    height=\"40.3934\"\r\n                    rx=\"20.1967\"\r\n                    fill=\"white\"\r\n                  />\r\n\r\n                  <text\r\n                    x=\"50%\"\r\n                    y=\"50%\"\r\n                    dominantBaseline=\"middle\"\r\n                    textAnchor=\"middle\"\r\n                    fontSize=\"14\"\r\n                    fill=\"#211F54\"\r\n                    fontWeight=\"500\"\r\n                    fontFamily=\"Poppins, sans-serif\"\r\n                  >\r\n                    Explore\r\n                  </text>\r\n                </svg>\r\n              </Link>\r\n            </footer>\r\n          </div>\r\n\r\n          {/* Body */}\r\n          <div\r\n            className={`mt-[5px] lg:mt-[30px] flex ${\r\n              showMessageCondition ? \"\" : \"flex-col\"\r\n            }`}\r\n          >\r\n            {/* chats list */}\r\n            <div\r\n              className={` ${openChat ? \"hidden\" : \"\"} lg:block w-full ${\r\n                showMessageCondition ? \"lg:w-[40%]\" : \"\"\r\n              }`}\r\n            >\r\n              <div className=\"px-2.5 flex justify-between items-center py-4 lg:px-6 w-full border-b border-[#AFB8CF] lg:border-r\">\r\n                <div className=\"flex gap-2 items-center relative w-[200px] justify-between\">\r\n                  <p className=\"text-base leading-normal font-semibold lg:leading-[35px]\">\r\n                    {filter === \"sellers\"\r\n                      ? \"All Seller Messages\"\r\n                      : filter === \"buyers\"\r\n                      ? \"All Buyer Messages\"\r\n                      : \"All Messages\"}\r\n                  </p>\r\n                  <IoIosArrowDown\r\n                    ref={menuBtnRef}\r\n                    className={`w-[18px] h-[18px] cursor-pointer transition ease-in-out duration-300 ${\r\n                      openFilter ? \"-rotate-180\" : \"\"\r\n                    }`}\r\n                    onClick={() => setOpenFilter(!openFilter)}\r\n                  />\r\n\r\n                  {openFilter && (\r\n                    <div\r\n                      className={`flex flex-col z-50 absolute top-full right-0 bg-white shadow-md rounded-md`}\r\n                    >\r\n                      <span\r\n                        className=\"text-sm font-medium py-2.5 px-8 hover:bg-blue-100 rounded-t-md border-b border-[#80808026] text-center cursor-pointer\"\r\n                        onClick={() => filterHandler(\"all\")}\r\n                      >\r\n                        All\r\n                      </span>\r\n                      <span\r\n                        className=\"text-sm font-medium py-2.5 px-8 hover:bg-blue-100 border-b border-[#80808026] text-center cursor-pointer\"\r\n                        onClick={() => filterHandler(\"buyers\")}\r\n                      >\r\n                        Buyer\r\n                      </span>\r\n                      <span\r\n                        className=\"text-sm font-medium py-2.5 px-8 hover:bg-blue-100 rounded-b-md text-center cursor-pointer\"\r\n                        onClick={() => filterHandler(\"sellers\")}\r\n                      >\r\n                        Seller\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* <HiOutlineDotsVertical className='cursor-pointer lg:w-5 lg:h-5' /> */}\r\n              </div>\r\n\r\n              {/* Search */}\r\n              {showMessageCondition ? (\r\n                <div className=\"border-b border-[#AFB8CF] py-4 px-2.5 lg:border-r\">\r\n                  <div className=\"relative w-full\">\r\n                    <input\r\n                      autoComplete=\"off\"\r\n                      type=\"search\"\r\n                      className=\"focus:outline-none bg-[#F3FAFF] py-2 pl-[44px] pr-4 w-full rounded-lg text-sm placeholder:font-light leading-normal placeholder:text-[#9FA7BE]\"\r\n                      placeholder=\"Search or start a new chat\"\r\n                      onChange={(e) => setSearchText(e.target.value)}\r\n                    />\r\n                    <IoMdSearch className=\"w-5 h-5 absolute top-[25%] left-4\" />\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                \"\"\r\n              )}\r\n\r\n              {/* Chats */}\r\n              {!chatListLoading ? (\r\n                showMessageCondition ? (\r\n                  <div className=\" flex flex-col max-h-[60vh] pb-2.5 overflow-auto no_scrollbar\">\r\n                    {chatList.length > 0 ? (\r\n                      [...chatList]?.map((chat, idx) => {\r\n                        console.log(\r\n                          \"chat?.participantData receiver\",\r\n                          chat?.participantData\r\n                        );\r\n                        const receiver = chat?.participantData?.find(\r\n                          (participant) => participant?._id !== userData?._id\r\n                        );\r\n                        const isSeller =\r\n                          receiver?._id === chat?.item_doc?.[0]?.createdBy?._id;\r\n                        console.log(\"receiver\", receiver);\r\n                        return (\r\n                          <div\r\n                            key={`chat-list-${idx}`}\r\n                            className={`cursor-pointer py-4 px-2.5 border-b flex gap-4 border-[#AFB8CF] ${\r\n                              chatId?.toString() === chat._id.toString()\r\n                                ? \"border-r-0 bg-[#F3FAFF]\"\r\n                                : \"lg:border-r\"\r\n                            } `}\r\n                            onClick={() => {\r\n                              dispatch(updateChatId(chat._id));\r\n                              dispatch(updateItemId(chat.sellerItemId));\r\n                              openChatHandler(chat._id);\r\n                            }}\r\n                          >\r\n                            <div className=\"relative overflow-hidden aspect-auto w-14 min-w-14 h-16\">\r\n                              <img\r\n                                src={\r\n                                  chat?.item_doc?.images?.length\r\n                                    ? chat?.item_doc?.images[0]\r\n                                    : avatarUrl(selectedChat.userName)\r\n                                }\r\n                                alt=\"Book Img\"\r\n                                className=\"object-cover rounded-lg w-14 h-16\"\r\n                              />\r\n                            </div>\r\n\r\n                            <div className=\"w-[90%]\">\r\n                              <h3 className=\"text-base leading-normal font-semibold line-clamp-1\">{`${receiver?.firstName} ${receiver?.lastName}`}</h3>\r\n\r\n                              {lastMessageText(chat)}\r\n                              <p className=\"mt-2.5 text-[#0161AB] text-sm leading-normal flex flex-row items-center relative\">\r\n                                <LuClock3 className=\"stroke-[#0161AB]\" />\r\n                                {chat?.lastMessage?.createdAt\r\n                                  ? formatRelativeDate(\r\n                                      chat?.lastMessage?.createdAt\r\n                                    )\r\n                                  : \"Today\"}\r\n                                <span className=\"mx-1  bg-[#0161AB] w-[1px] h-[8px] scale-200 rounded-full relative -top-[2px]\"></span>\r\n                                {formatTo12HourTime(\r\n                                  chat?.lastMessage?.createdAt ?? new Date()\r\n                                )}\r\n                              </p>\r\n                            </div>\r\n                            <div>\r\n                              <MdDeleteOutline\r\n                                size={25}\r\n                                onClick={() => deleteConversation(chat._id)}\r\n                              />\r\n                            </div>\r\n                          </div>\r\n                        );\r\n                      })\r\n                    ) : (\r\n                      <div className=\"text-center py-4 px-2 bg-gray-50 mt-2 rounded-md\">\r\n                        No Data Found\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"w-full  flex justify-center items-center\"></div>\r\n                )\r\n              ) : (\r\n                <div className=\" w-full flex justify-center items-center\"></div>\r\n              )}\r\n            </div>\r\n\r\n            {/* ---------------------------------------- Chat screen ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- */}\r\n            <div\r\n              className={`  ${\r\n                openChat ? \"w-[100%]\" : \"w[0px]\"\r\n              } lg:w-[60%] relative p-0 m-0`}\r\n            >\r\n              {showChatBodyCondition ? (\r\n                <div\r\n                  ref={chatRef}\r\n                  style={{\r\n                    filter:\r\n                      ItemListStatusEnum.MARKED_AS_SOLD == selectedChat.status\r\n                        ? `blur(5px)`\r\n                        : \"\",\r\n                  }}\r\n                  className={`${\r\n                    openChat ? \"flex \" : \"hidden\"\r\n                  } w-full px-2.5 z-[5] lg:flex flex-col lg:p-0  ${\r\n                    ItemListStatusEnum.MARKED_AS_SOLD == selectedChat.status\r\n                      ? \"backdrop-blur-md bg-white/30\"\r\n                      : \"\"\r\n                  }`}\r\n                >\r\n                  {showChatBodyCondition && (\r\n                    <div className=\"border-b border-[#AFB8CF] flex justify-between py-2.5 lg:py-4 lg:px-6\">\r\n                      <div className=\"flex items-center gap-3.5\">\r\n                        <IoArrowBack\r\n                          className=\"cursor-pointer lg:hidden\"\r\n                          onClick={closeChatHandler}\r\n                        />\r\n\r\n                        <div className=\"flex items-center gap-1.5 lg:gap-2\">\r\n                          <div className=\"relative w-6 h-6 lg:w-[40px] lg:h-[40px] rounded-lg\">\r\n                            <img\r\n                              src={\r\n                                selectedChat?.backCoverImage ||\r\n                                avatarUrl(selectedChat.userName)\r\n                              }\r\n                              alt=\"Book Image\"\r\n                              className=\"object-cover rounded-lg w-full h-full\"\r\n                            />\r\n                            <span className=\"absolute -bottom-[5px] -right-[5px] rounded-full w-3 h-3 lg:w-[17px] lg:h-[17px]\">\r\n                              <img\r\n                                src={\r\n                                  selectedChat?.profileImage ||\r\n                                  avatarUrl(selectedChat.userName)\r\n                                }\r\n                                alt=\"Profile\"\r\n                                className=\"object-cover rounded-full z-10 w-full h-full\"\r\n                              />\r\n                            </span>\r\n                          </div>\r\n                          <p className=\"text-[11px] font-semibold leading-normal text-[#636363] lg:text-base\">\r\n                            {selectedChat?.userName}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div\r\n                        ref={menuRef}\r\n                        className=\"relative cursor-pointer py-0.5 px-1 flex justify-center items-center custom_shadow_first rounded-lg lg:px-2\"\r\n                      >\r\n                        <HiOutlineDotsVertical\r\n                          onClick={() => setshowOptionThreeDot(true)}\r\n                        />\r\n                        {showOptionThreeDot && (\r\n                          <div className=\"absolute left-[-100px] cursor-pointer py-[5px] px-1 flex justify-center items-center custom_shadow_first rounded-md lg:px-2\">\r\n                            <button\r\n                              onClick={() =>\r\n                                router.push(\r\n                                  `/book-detail?id=${selectedChat.itemId}`\r\n                                )\r\n                              }\r\n                            >\r\n                              View More\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"lg:pl-6\">\r\n                    <div\r\n                      ref={messagesEndRef}\r\n                      className={`flex h-[60vh] py-10  flex-col gap-8 pt-11 transition-all linear duration-300 ${\r\n                        !openChatTemplate\r\n                          ? \"pb-10\"\r\n                          : chatToggle === \"chats\"\r\n                          ? \"pb-35 md:pb-45\"\r\n                          : \"pb-[220px] md:pb-[270px]\"\r\n                      } overflow-auto h-[75vh] no_scrollbar`}\r\n                    >\r\n                      {messages?.map((message, idx) =>\r\n                        sender?._id?.toString() ===\r\n                        message?.sender?.toString() ? (\r\n                          //  right side message\r\n                          <div\r\n                            key={`message-${idx}`}\r\n                            className=\"flex flex-col items-end self-end gap-1 w-[70%] pr-2\"\r\n                          >\r\n                            {RenderMessage(message)}\r\n                          </div>\r\n                        ) : (\r\n                          // left side message\r\n                          <div\r\n                            key={`message-${idx}`}\r\n                            className=\"flex flex-col items-start self-start gap-1 w-[70%]\"\r\n                          >\r\n                            {RenderMessage(message)}\r\n                          </div>\r\n                        )\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {ItemListStatusEnum.MARKED_AS_SOLD !== selectedChat.status &&\r\n                    showChatBodyCondition && (\r\n                      <div className=\"relative z-[20] border\">\r\n                        <div className=\"absolute bottom-full w-full\">\r\n                          <div\r\n                            className={`flex gap-2 items-center bg-[#F9F9F9] ${messagesComponentScss.borderGradient}`}\r\n                          >\r\n                            <div\r\n                              className={`!absolute -top-[80%] left-1/2 -translate-x-1/2 translate-y-1/2 w-[41px] h-[22px] flex justify-center items-end cursor-pointer !rounded-t-full lg:w-[61px] lg:h-[33px] lg:top-[-110%] ${\r\n                                messagesComponentScss.borderGradient\r\n                              } ${openChatTemplate ? \"\" : \"\"}`}\r\n                              onClick={chatTemplateOpenHandler}\r\n                            >\r\n                              <svg width=\"0\" height=\"0\">\r\n                                <linearGradient\r\n                                  id=\"blue-gradient\"\r\n                                  x1=\"100%\"\r\n                                  y1=\"100%\"\r\n                                  x2=\"0%\"\r\n                                  y2=\"0%\"\r\n                                >\r\n                                  <stop stopColor=\"#211f54\" offset=\"11%\" />\r\n                                  <stop stopColor=\"#0161ab\" offset=\"98%\" />\r\n                                </linearGradient>\r\n                              </svg>\r\n                              <MdKeyboardArrowDown\r\n                                style={{ fill: \"url(#blue-gradient)\" }}\r\n                                className={`lg:w-7 lg:h-7 transition-transform duration-200 ${\r\n                                  openChatTemplate ? \"\" : \"-rotate-180\"\r\n                                }`}\r\n                              />\r\n                            </div>\r\n\r\n                            <div\r\n                              className={`flex items-center ${\r\n                                selectedChat.isSeller ? \"w-full\" : \"w-1/2\"\r\n                              } justify-center gap-2 text-[9.5px] leading-[13px] pt-3 pb-2 cursor-pointer lg:text-sm lg:leading-[19px]  ${\r\n                                chatToggle !== \"chats\"\r\n                                  ? \"text-[#A0A0A0]\"\r\n                                  : \"global_text_linear_gradient\"\r\n                              }`}\r\n                              onClick={() => setChatToggle(\"chats\")}\r\n                            >\r\n                              {chatToggle !== \"chats\" ? (\r\n                                <svg\r\n                                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                                  width=\"16\"\r\n                                  height=\"13\"\r\n                                  viewBox=\"0 0 22 18\"\r\n                                  fill=\"none\"\r\n                                  className=\"lg:w-[22px] lg:h-[18px]\"\r\n                                >\r\n                                  <path\r\n                                    d=\"M2 9C0.9 9 0 8.1 0 7V2C0 0.9 0.9 0 2 0H10C11.1 0 12 0.9 12 2V7C12 8.1 11.1 9 10 9H8V12L5 9H2ZM20 15C21.1 15 22 14.1 22 13V8C22 6.9 21.1 6 20 6H14V7C14 9.2 12.2 11 10 11V13C10 14.1 10.9 15 12 15H14V18L17 15H20Z\"\r\n                                    fill=\"#A0A0A0\"\r\n                                  />\r\n                                </svg>\r\n                              ) : (\r\n                                <svg\r\n                                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                                  width=\"16\"\r\n                                  height=\"13\"\r\n                                  viewBox=\"0 0 22 18\"\r\n                                  fill=\"none\"\r\n                                  className=\"lg:w-[22px] lg:h-[18px]\"\r\n                                >\r\n                                  <path\r\n                                    d=\"M2 9C0.9 9 0 8.1 0 7V2C0 0.9 0.9 0 2 0H10C11.1 0 12 0.9 12 2V7C12 8.1 11.1 9 10 9H8V12L5 9H2ZM20 15C21.1 15 22 14.1 22 13V8C22 6.9 21.1 6 20 6H14V7C14 9.2 12.2 11 10 11V13C10 14.1 10.9 15 12 15H14V18L17 15H20Z\"\r\n                                    fill=\"url(#paint0_linear_2021_12436)\"\r\n                                  />\r\n                                  <defs>\r\n                                    <linearGradient\r\n                                      id=\"paint0_linear_2021_12436\"\r\n                                      x1=\"19.5451\"\r\n                                      y1=\"1.55769\"\r\n                                      x2=\"-0.248734\"\r\n                                      y2=\"2.28634\"\r\n                                      gradientUnits=\"userSpaceOnUse\"\r\n                                    >\r\n                                      <stop stopColor=\"#211F54\" />\r\n                                      <stop offset=\"1\" stopColor=\"#0161AB\" />\r\n                                    </linearGradient>\r\n                                  </defs>\r\n                                </svg>\r\n                              )}\r\n                              Chats\r\n                            </div>\r\n                            {!selectedChat.isSeller && (\r\n                              <div\r\n                                className={`flex items-center w-1/2 justify-center gap-2 text-[9.5px] leading-[13px] pt-3 pb-2 cursor-pointer lg:text-sm lg:leading-[19px] ${\r\n                                  chatToggle === \"make offer\"\r\n                                    ? \"global_text_linear_gradient\"\r\n                                    : \"text-[#A0A0A0]\"\r\n                                } `}\r\n                                onClick={() => setChatToggle(\"make offer\")}\r\n                              >\r\n                                <svg\r\n                                  xmlns=\"http://www.w3.org/2000/svg\"\r\n                                  width=\"15\"\r\n                                  height=\"15\"\r\n                                  viewBox=\"0 0 20 20\"\r\n                                  fill=\"none\"\r\n                                  className=\"lg:w-[18px] lg:h-[18px]\"\r\n                                >\r\n                                  <path\r\n                                    d=\"M19.41 9.58L10.41 0.58C10.05 0.22 9.55 0 9 0H2C0.9 0 0 0.9 0 2V9C0 9.55 0.22 10.05 0.59 10.42L9.59 19.42C9.95 19.78 10.45 20 11 20C11.55 20 12.05 19.78 12.41 19.41L19.41 12.41C19.78 12.05 20 11.55 20 11C20 10.45 19.77 9.94 19.41 9.58ZM3.5 5C2.67 5 2 4.33 2 3.5C2 2.67 2.67 2 3.5 2C4.33 2 5 2.67 5 3.5C5 4.33 4.33 5 3.5 5Z\"\r\n                                    fill={`${\r\n                                      chatToggle === \"make offer\"\r\n                                        ? \"url(#paint0_linear_2021_16128)\"\r\n                                        : \"#A0A0A0\"\r\n                                    } `}\r\n                                  />\r\n                                  <defs>\r\n                                    <linearGradient\r\n                                      id=\"paint0_linear_2021_16128\"\r\n                                      x1=\"17.7683\"\r\n                                      y1=\"1.73077\"\r\n                                      x2=\"-0.234176\"\r\n                                      y2=\"2.27298\"\r\n                                      gradientUnits=\"userSpaceOnUse\"\r\n                                    >\r\n                                      <stop stopColor=\"#211F54\" />\r\n                                      <stop offset=\"1\" stopColor=\"#0161AB\" />\r\n                                    </linearGradient>\r\n                                  </defs>\r\n                                </svg>\r\n                                Make Offer\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n\r\n                          <div\r\n                            className={`global_linear_gradient text-white text-[9.5px] leading-normal py-3 px-5 lg:py-4 lg:px-8 lg:text-sm transition-all duration-300 ${\r\n                              openChatTemplate\r\n                                ? \"max-h-[200px] opacity-100\"\r\n                                : \"max-h-0 opacity-0 !p-0\"\r\n                            } `}\r\n                          >\r\n                            Don't buy nuh 'puss in a bag'!\r\n                            <br />\r\n                            Ask about the item's condition, request additional\r\n                            photos, and confirm payment and pick-up/delivery\r\n                            options before you close the sale.\r\n                          </div>\r\n\r\n                          <div\r\n                            className={` w-full bg-white transition-all duration-300 ${\r\n                              openChatTemplate\r\n                                ? \"max-h-[200px] opacity-100\"\r\n                                : \"max-h-0 opacity-0\"\r\n                            }`}\r\n                          >\r\n                            {chatToggle === \"chats\" ? (\r\n                              <div className=\"flex gap-3.5 pt-1 px-5 lg:py-[14px] pb-2.5 overflow-auto no_scrollbar\">\r\n                                {userData?._id == itemOfConversation?.createdBy\r\n                                  ? chatSuggestions.admin?.map((chat, idx) => (\r\n                                      <span\r\n                                        key={`predefined - chats - ${idx} `}\r\n                                        className={`w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-t-xl rounded-bl-xl gradient-all-round-border border-2 lg:py-2 lg:px-3.5 lg:text-xs lg:rounded-t-[18px] lg:rounded-bl-[18px] cursor-pointer active:scale-95`}\r\n                                        onClick={() => sendMessage(chat)}\r\n                                      >\r\n                                        <p className=\"global_text_linear_gradient font-medium\">\r\n                                          {chat}\r\n                                        </p>\r\n                                      </span>\r\n                                    ))\r\n                                  : chatSuggestions.client?.map((chat, idx) => (\r\n                                      <span\r\n                                        key={`predefined - chats - ${idx} `}\r\n                                        className={`w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-t-xl rounded-bl-xl gradient-all-round-border border-2 lg:py-2 lg:px-3.5 lg:text-xs lg:rounded-t-[18px] lg:rounded-bl-[18px] cursor-pointer active:scale-95`}\r\n                                        onClick={() => sendMessage(chat)}\r\n                                      >\r\n                                        <p className=\"global_text_linear_gradient font-medium\">\r\n                                          {chat}\r\n                                        </p>\r\n                                      </span>\r\n                                    ))}\r\n                              </div>\r\n                            ) : (\r\n                              // make offer components render based on seller and buyer\r\n                              // selectedChat?.isSeller ?\r\n                              // makeOfferComponents.seller[sellerMakeOfferShownComponent]\r\n                              // :\r\n                              makeOfferComponents.buyer[\r\n                                buyerMakeOfferShownComponent\r\n                              ]\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"relative w-full\">\r\n                          <div className=\"w-full  flex gap-3 py-2.5 pr-4 bg-[#F3F3F3] items-center lg:py-4 lg:pr-6\">\r\n                            <div className=\"flex justify-center items-center  mx-2\">\r\n                              <FaMapMarkerAlt\r\n                                className=\"lg:w-5 lg:h-[27px] fill-[#575757] cursor-pointer\"\r\n                                onClick={locationSend}\r\n                              />\r\n                            </div>\r\n\r\n                            <form\r\n                              className=\"w-full   flex bg-white rounded-full\"\r\n                              onSubmit={sendMessageHandler}\r\n                            >\r\n                              <input\r\n                                type=\"text\"\r\n                                autoComplete=\"off\"\r\n                                name=\"message\"\r\n                                className=\"w-full focus:outline-none text-[10px] leading-normal py-1.5 pl-3 pr-[50px] rounded-full placeholder:text-[#9FA7BE] lg:py-2.5 lg:pl-[21px] lg:text-[15px]\"\r\n                                placeholder=\"Type your message here ...\"\r\n                                //   autoComplete=\"off\"\r\n                              />\r\n                              <div className=\"flex  items-center justify-between mr-3 \">\r\n                                <input\r\n                                  autoComplete=\"off\"\r\n                                  type=\"file\"\r\n                                  ref={fileInputRef}\r\n                                  onChange={handleFileChange}\r\n                                  className=\"hidden\"\r\n                                />\r\n                                <button\r\n                                  type=\"button\"\r\n                                  className=\" cursor-pointer flex justify-center items-center px-2  rounded-full\"\r\n                                  onClick={handleButtonClick}\r\n                                >\r\n                                  <FaPlusCircle size={25} />\r\n                                </button>\r\n                                <button\r\n                                  type=\"submit\"\r\n                                  className=\" cursor-pointer flex justify-center items-center p-1 lg:p-1.5 lg:px-1.5 bg-[#575757] rounded-full\"\r\n                                >\r\n                                  <BiLogoTelegram\r\n                                    size={25}\r\n                                    className=\"fill-white w-[14px] h-[14px] lg:w-3.5 lg:h-3.5\"\r\n                                  />\r\n                                </button>\r\n                              </div>\r\n                            </form>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className={`${\r\n                    showMessageCondition ? \"hidden lg:w-[60%]\" : \"flex\"\r\n                  } lg:flex flex-col w-full justify-center items-center min-h-[70vh] md:min-h-[75vh] gap-[60px]`}\r\n                >\r\n                  <p className=\"text-xs\">No, messages,yet?</p>\r\n\r\n                  <div>\r\n                    <svg\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      width=\"90\"\r\n                      height=\"70\"\r\n                      viewBox=\"0 0 129 106\"\r\n                      fill=\"none\"\r\n                      className=\"md:w-[129px] md:h-[106px]\"\r\n                    >\r\n                      <path\r\n                        d=\"M11.7273 52.7727C5.27727 52.7727 0 47.4955 0 41.0455V11.7273C0 5.27727 5.27727 0 11.7273 0H58.6364C65.0864 0 70.3636 5.27727 70.3636 11.7273V41.0455C70.3636 47.4955 65.0864 52.7727 58.6364 52.7727H46.9091V70.3636L29.3182 52.7727H11.7273ZM117.273 87.9545C123.723 87.9545 129 82.6773 129 76.2273V46.9091C129 40.4591 123.723 35.1818 117.273 35.1818H82.0909V41.0455C82.0909 53.9455 71.5364 64.5 58.6364 64.5V76.2273C58.6364 82.6773 63.9136 87.9545 70.3636 87.9545H82.0909V105.545L99.6818 87.9545H117.273Z\"\r\n                        fill=\"url(#paint0_linear_2933_4071)\"\r\n                      />\r\n                      <defs>\r\n                        <linearGradient\r\n                          id=\"paint0_linear_2933_4071\"\r\n                          x1=\"114.605\"\r\n                          y1=\"9.13373\"\r\n                          x2=\"-1.45849\"\r\n                          y2=\"13.4062\"\r\n                          gradientUnits=\"userSpaceOnUse\"\r\n                        >\r\n                          <stop stopColor=\"#211F54\" />\r\n                          <stop offset=\"1\" stopColor=\"#0161AB\" />\r\n                        </linearGradient>\r\n                      </defs>\r\n                    </svg>\r\n                  </div>\r\n                  <p className=\"text-xs leading-normal\">\r\n                    We’ll keep messages for any item you’re selling in here\r\n                  </p>\r\n                </div>\r\n              )}\r\n              {ItemListStatusEnum.MARKED_AS_SOLD == selectedChat.status && (\r\n                <div className=\"bg-white   max-w-full h-[30%] absolute bottom-[0%] flex items-cemter justify-center\">\r\n                  <div className=\"w-fit  flex flex-col items-center justify-center\">\r\n                    <img className=\"mx-auto\" src={\"/icons/bookEmpty.png\"} />\r\n                    <p className=\"md:w-[60%] text-center mx-auto\">\r\n                      The chat has been removed because the seller mark the book\r\n                      as sold\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n}\r\n\r\nexport default myMessages;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAOA;AAGA;AAKA;AACA;AACA;AA3CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA,MAAM,kBAAkB;IACtB,OAAO;QACL;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,SAAS;IACP,IAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;IAC/B,QAAQ,GAAG,CAAC,YAAY;IACxB,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAM,GAAG,WAAW;IAChD,MAAM,YAAY,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAM,GAAG;IACxC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,qBAAqB;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,qBAAqB;IAC5E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7D;QACA;KACD;IACD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,QAAQ;QACR,QAAQ,UAAU;QAClB,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,uBAAuB;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,+BAA+B,iCAAiC,GACrE,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,yDAAyD;IACpF,MAAM,CAAC,8BAA8B,gCAAgC,GACnE,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,0FAA0F;IACpH,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;IAC9D,IAAI,YAAY,YAAY,GAAG,CAAC;IAChC,QAAQ,GAAG,CAAC,aAAa;IACzB,yEAAyE;IACzE,QAAQ,GAAG,CAAC,sBAAsB;IAClC,MAAM,uBACJ,UAAU,UAAU,UAAU,SAAS,KAAK,YAAY;IAC1D,IAAI,wBAAwB,UAAU;IACtC,QAAQ,GAAG,CAAC,wBAAwB;IACpC,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC,aAAa;IACzB,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,QAAQ,GAAG,CAAC,YAAY;IACxB,QAAQ,GAAG,CAAC,sBAAsB;IAElC,kBAAkB;IAClB,QAAQ,GAAG,CAAC,UAAU;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,sBAAsB;YACxB,4CAA4C;YAC5C,QAAQ,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QACvD;QAEA,MAAM,YAAY,eAAe,OAAO;QACxC,IAAI,WAAW;YACb,UAAU,QAAQ,CAAC;gBACjB,KAAK,UAAU,YAAY;gBAC3B,UAAU;YACZ;QACF;IACF,GAAG;QAAC;KAAS;IAEb,yDAAyD;IACzD,MAAM,kBAAkB,CAAC;QACvB,UAAU;QACV,cAAc;QACd,YAAY;IACd;IAEA,MAAM,mBAAmB;QACvB,YAAY;IACd;IAEA,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,GAAG;QACzB,kDAAkD;QAClD,MAAM,OAAO;eAAI;SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;YACzC,IAAI,GAAG,WAAW,QAAQ,KAAK,OAAO;QACxC;QACA,4BAA4B;QAC5B,MAAM,OAAO,qHAAA,CAAA,UAAe,CAAC,MAAM,KAAK;QAExC,IAAI,MAAM,SAAS,GAAG;YACpB,uBAAuB;QACzB;IACF,GAAG;QAAC;KAAS;IAEb,qBAAqB;IAErB,MAAM,uBAAuB;QAC3B,IAAI,WAAW,UAAU,WAAW,CAAC,kBAAkB,CAAC,CAAC;YACvD,QAAQ,GAAG,CAAC,YAAY;YACxB,mBAAmB;gBACjB,KAAK,SAAS,MAAM,CAAC,QAAQ;gBAC7B,MAAM,SAAS,MAAM,CAAC,SAAS;YACjC;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,iBAAiB;IAE7B,MAAM,eAAe;QACnB,IAAI;YACF,IAAI,YAAY,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;YACvB,IAAI,WAAW,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;YAC/B,IAAI,mBAAmB,MAAM,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;YAExC,UAAU;QACV,iCAAiC;QACjC,qBAAqB;QACrB,iBAAiB;QACjB,iDAAiD;QACjD,QAAQ;QACR,yBAAyB;QACzB,yCAAyC;QACzC,2CAA2C;QAC3C,6BAA6B;QAC7B,wCAAwC;QACxC,8BAA8B;QAC9B,eAAe;QACf,2DAA2D;QAC3D,QAAQ;QACR,KAAK;QACP,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS;QACvB;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB;QACpB,IAAI;YACF,IAAI,YAAY,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;YACvB,IAAI,aAAa,MAAM,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;YACpC,QAAQ,GAAG,CAAC,4BAA4B;YACxC,IAAI,WAAW,MAAM,IAAI,KAAK;gBAC5B,sBAAsB,WAAW,IAAI;gBAErC,gBAAgB,CAAC,OAAS,CAAC;wBACzB,GAAG,IAAI;wBACP,QAAQ,YAAY,MAAM,WAAW;wBACrC,QAAQ,YAAY,MAAM;wBAC1B,gBACE,YAAY,MAAM,OAAO,UAAU,YAAY,MAAM,MAAM,CAAC,EAAE;wBAChE,cAAc,YAAY,MAAM,WAAW;wBAC3C,UAAU,GAAG,YAAY,MAAM,WAAW,UAAU,CAAC,EAAE,YAAY,MAAM,WAAW,UAAU;wBAC9F,UAAU,SAAS,GAAG,IAAI,WAAW,IAAI,EAAE,WAAW;wBACtD,QAAQ,YAAY,KAAK;oBAC3B,CAAC;gBACD,sFAAsF;gBACtF,gBAAgB,CAAC;oBACf,OAAO;wBAAE,GAAG,IAAI;wBAAE,WAAW,WAAW,IAAI,CAAC,KAAK;oBAAC;gBACrD;gBACA,kBAAkB,GAAG,WAAW,IAAI,CAAC,KAAK,EAAE;YAC5C,+CAA+C;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;QACvB;IACF;IAEA,0DAA0D;IAC1D,MAAM,gBAAgB,OAAO,YAAY;QACvC,IAAI;YACF,mBAAmB;YACnB,IAAI,YAAY,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;YAEvB,MAAM,cAAc,CAAC;YACrB,IAAI,YAAY,WAAW,CAAC,aAAa,GAAG;YAC5C,IAAI,YAAY,WAAW,CAAC,WAAW,GAAG;YAC1C,IAAI,UAAU,MAAM,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE;YAE/B,QAAQ,GAAG,CAAC,WAAW;YACvB,IAAI,QAAQ,MAAM,IAAI,KAAK;gBACzB,IAAI,eAAe;gBACnB,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC;oBAC1C,IAAI,WAAW,KAAK,YAAY,EAAE;wBAChC,eAAe,KAAK,GAAG;oBACzB;oBACA,OAAO,CAAC,KAAK,SAAS,CAAC,QAAQ,CAAC,SAAS,GAAG;gBAC9C;gBACA,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,IAAI,cAAc;oBAChB,cAAc;gBAChB;gBACA,YAAY;gBACZ,IAAI,QAAQ;oBACV,IAAK,IAAI,QAAQ,GAAG,QAAQ,eAAe,MAAM,EAAE,QAAS;wBAC1D,IAAI,UAAU,MAAM,IAAI,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE;wBACjD,2CAA2C;wBAC7C;oBACF;gBACF;gBACA,WAAW;YACb,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,WAAW;gBAChC,WAAW;YACb;YACA,mBAAmB;QACnB,iCAAiC;QACjC,sBAAsB;QACtB,iBAAiB;QACjB,8CAA8C;QAC9C,iDAAiD;QACjD,SAAS;QACT,wCAAwC;QACxC,yBAAyB;QACzB,yCAAyC;QACzC,8BAA8B;QAC9B,8BAA8B;QAC9B,kDAAkD;QAClD,sCAAsC;QACtC,4BAA4B;QAC5B,oCAAoC;QACpC,eAAe;QACf,oEAAoE;QACpE,4BAA4B;QAC5B,oCAAoC;QACpC,QAAQ;QACR,KAAK;QACP,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;YACrB,WAAW;YACX,mBAAmB;QACrB;IACF;IACA,QAAQ,GAAG,CAAC,WAAW,cAAc,QAAQ,UAAU,UAAU;IACjE,iFAAiF;IACjF,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,eAAe;YACf,IAAI,YAAY,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;YACvB,IAAI,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD,EAAE;YACjC,QAAQ,GAAG,CAAC,wBAAwB;YACpC,IAAI,SAAS,MAAM,IAAI,KAAK;gBAC1B,sBAAsB,SAAS,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC3D,aAAa,UAAU,MAAM;gBAC7B,MAAM,eAAe,UAAU,MAAM,cAAc,CAAC,EAAE;gBAEtD,MAAM,eAAe,cAAc,mBAAmB,CAAC,EAAE;gBACzD,MAAM,eAAe,cAAc,mBAAmB,CAAC,EAAE;gBACzD,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,QAAQ,GAAG,CAAC,gBAAgB;gBAE5B,MAAM,WACJ,UAAU,OAAO,aAAa,GAAG,GAAG,eAAe;gBAErD,QAAQ,GAAG,CAAC,kBAAkB;gBAE9B,gBAAgB,CAAC,OAAS,CAAC;wBACzB,GAAG,IAAI;wBACP,gBAAgB,cAAc;wBAC9B,QAAQ,cAAc;wBACtB,QAAQ,UAAU;wBAClB,UAAU,UAAU,YAAY,MAAM,UAAU;wBAChD,cAAc,UAAU;wBACxB,gBACE,cAAc,MAAM,CAAC,EAAE,EAAE,OAAO,UAChC,cAAc,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;wBACpC,WAAW,cAAc,MAAM,CAAC,EAAE,EAAE;wBACpC,UAAU,cAAc,MAAM,CAAC,EAAE,EAAE,cAAc,SAAS,GAAG;wBAC7D,QAAQ,cAAc,MAAM,CAAC,EAAE,EAAE;oBACnC,CAAC;gBACD,0FAA0F;gBAC1F,WAAW;gBACX,YAAY,UAAU,MAAM;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;QACvB;IACF;IAEA,gDAAgD;IAChD,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;YACrB,MAAM,UAAU,CAAC;YACjB,IAAI,aAAa,WAAW,OAAO,CAAC,WAAW,GAAG;iBAC7C;QACL,2BAA2B;QAC3B,+DAA+D;QACjE,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;QACvB;IACF;IAEA,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mGAAmG;QACnG,IAAI,QAAQ;YACV;YACA,YAAY;QACd;QAEA;QACA;QACA;QACA,MAAM,qBAAqB,CAAC;YAC1B,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;gBAC9D,sBAAsB;YACxB;QACF;QACA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB,OAAO,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;QACpD,MAAM,gBAAgB,CAAC;YACrB,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG;gBAChE,cAAc;YAChB;QACF;QACA,SAAS,gBAAgB,CAAC,SAAS;QACnC,OAAO;YACL,kBAAkB,OAAO,EAAE;YAC3B,SAAS,mBAAmB,CAAC,SAAS;QAEtC,yBAAyB;QAC3B;IACF,GAAG,EAAE;IAEL,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,OAAO,EAAE;YAC7B,kBAAkB,OAAO,CAAC,YAAY;QACxC;IACF,GAAG;QAAC;QAAY;KAAO;IAEvB,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,UAAU,6GAAA,CAAA,SAAM;QAC5B,6GAAA,CAAA,SAAM,CAAC,EAAE,CAAC,WAAW;YACnB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,iBAAiB,6GAAA,CAAA,SAAM;QACrC;QACA,6GAAA,CAAA,SAAM,CAAC,EAAE,CAAC,cAAc;YACtB,QAAQ,GAAG,CAAC;QACd;QACA,6GAAA,CAAA,SAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC1B,QAAQ,GAAG,CAAC,kBAAkB,IAAI,OAAO,GAAG,wFAAwF;QACtI;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,6CAA6C;YAC7C,YAAY,CAAC,OAAS;uBAAI;oBAAM,KAAK,OAAO;iBAAC;YAC7C,IAAI,iBAAiB,MAAM,OAAO,OAAO,CAAC;gBACxC,OAAO,CAAC,KAAK,SAAS,CAAC,QAAQ,CAAC,SAAS,GAAG;YAC9C;YACA,YAAY;QACd;QAEA,6GAAA,CAAA,SAAM,CAAC,EAAE,CAAC,cAAc;QACxB,6GAAA,CAAA,SAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC1B,QAAQ,KAAK,CAAC,4BAA4B,IAAI,OAAO;QACvD;QACA,6GAAA,CAAA,SAAM,CAAC,EAAE,CAAC,WAAW;YACnB,QAAQ,GAAG,CAAC,+BAA+B,6GAAA,CAAA,SAAM,CAAC,EAAE;QACtD;QAEA,2CAA2C;QAC3C,OAAO;YACL,6GAAA,CAAA,SAAM,CAAC,GAAG,CAAC,cAAc;QAC3B;IACF,GAAG,EAAE;IACL,MAAM,YAAY,CAAC;QACjB,OAAO,CAAA,GAAA,yJAAA,CAAA,uBAAoB,AAAD,EAAE,GAAG,UAAU,EAAE;YACzC,SAAS;YACT,WAAW;QACb;IACF;IACA,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC,gBAAgB;IAC5B,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC,YAAY;IAExB,+BAA+B;IAC/B,MAAM,cAAc,CAAC,SAAS,WAAW;QACvC,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,KAAK,GAAG;QAE7C,IAAI,OAAO;YACT,aAAa,aAAa,MAAM,IAAI,mBAAmB,SAAS,CAAC,GAAG;YACpE,cAAc,cAAc,UAAU;YACtC,SAAS;gBACP,MAAM,QAAQ,IAAI;YACpB;QACF;QAEA,IAAI,UAAU;YACZ,KAAK,OAAO,CAAC,UAAU,GAAG;YAC1B,KAAK,OAAO,CAAC,SAAS,GAAG;QAC3B;QACA,6GAAA,CAAA,SAAM,CAAC,IAAI,CAAC,gBAAgB;IAC9B;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,OAAO;YACT,aAAa,aAAa,MAAM,IAAI,mBAAmB,SAAS,CAAC,GAAG;YACpE,cAAc,cAAc,UAAU;YACtC,SAAS;gBACP,WAAW;gBACX,UAAU;gBACV,SAAS;YACX;QACF;QACA,6GAAA,CAAA,SAAM,CAAC,IAAI,CAAC,gBAAgB;IAC9B;IAEA,MAAM,eAAe,CAAC;QACpB,QAAQ,GAAG,CAAC;QACZ,UAAU,WAAW,EACjB,MAAM;YAAE,MAAM;QAAc,GAC7B,KAAK,SAAU,MAAM;YACpB,QAAQ,GAAG,CAAC,sBAAsB,OAAO,KAAK;QAChD;QACF,IAAI,WAAW,UAAU,WAAW,CAAC,kBAAkB,CAAC,CAAC;YACvD,QAAQ,GAAG,CAAC,YAAY;YACxB,IAAI,CAAC,SAAS,MAAM,EAAE;gBACpB;YACF;YACA,mBAAmB;gBACjB,KAAK,SAAS,MAAM,CAAC,QAAQ;gBAC7B,MAAM,SAAS,MAAM,CAAC,SAAS;YACjC;YACA,IAAI,OAAO;gBACT,aAAa,aAAa,MAAM,IAAI,mBAAmB,SAAS,CAAC,GAAG;gBACpE,cAAc,cAAc,UAAU;gBACtC,SAAS;oBACP,WAAW;oBACX,wBAAwB;oBACxB,mBAAmB;oBACnB,MAAM,KAAK,SAAS,CAAC;wBACnB,KAAK,SAAS,MAAM,CAAC,QAAQ;wBAC7B,MAAM,SAAS,MAAM,CAAC,SAAS;oBACjC;gBACF;YACF;YACA,6GAAA,CAAA,SAAM,CAAC,IAAI,CAAC,gBAAgB;QAC9B;IACF;IAEA,kBAAkB;IAClB,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAEhB,eAAe;QACf,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK;QAElC,oBAAoB;QACpB,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG;IAC3B;IAEA,MAAM,gBAAgB,CAAC;QACrB,UAAU;QACV,cAAc;QACd,cAAc;QACd,UAAU;QACV,YAAY,QAAQ,wBAAwB;IAC9C;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B;QAC9B,oBAAoB,CAAC;QACrB,MAAM,YAAY,eAAe,OAAO;IACxC,mBAAmB;IACnB,yBAAyB;IACzB,mCAAmC;IACnC,0BAA0B;IAC1B,QAAQ;IACR,IAAI;IACN;IAEA,IAAI,SAAS,OAAO,iBAAiB;IACrC,qFAAqF;IACrF,MAAM,wBAAwB,CAAC;QAC7B,IAAI,aAAa,EAAE,MAAM,CAAC,KAAK;QAC/B,+CAA+C;QAC/C,IAAI,WAAW,UAAU,CAAC,SAAS;YACjC,aAAa,WAAW,KAAK,CAAC,OAAO,MAAM;QAC7C,OAAO;YACL,kEAAkE;YAClE,aAAa,WAAW,OAAO,CAAC,WAAW;QAC7C;QAEA,oBAAoB;QACpB,MAAM,UAAU,WAAW,OAAO,CAAC,WAAW;QAE9C,kBAAkB;IACpB;IACA,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,4BAA4B;IAC5B,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,IAAI;QACJ,IAAI,eAAe,QAAQ,CAAC,SAAS;YACnC,YAAY,eAAe,KAAK,CAAC,OAAO,MAAM;QAChD,OAAO;YACL,YAAY;QACd;QACA,QAAQ,GAAG,CAAC,WAAW;QACvB,4CAA4C;QAC5C,mDAAmD;QACnD,aAAa;QACb,IAAI;QAEJ,IAAI,mBAAmB,QAAQ;QAC/B,IAAI,gBAAgB,OAAO;QAC3B,YAAY;IACZ,2BAA2B;IAC7B;IAEA,iEAAiE;IACjE,oBAAoB;IACpB,oCAAoC;IACpC,mCAAmC;IACnC,gEAAgE;IAEhE,6EAA6E;IAC7E,kFAAkF;IAClF,wCAAwC;IACxC,wEAAwE;IACxE,8CAA8C;IAC9C,qCAAqC;IACrC,cAAc;IAEd,0EAA0E;IAE1E,kDAAkD;IAClD,gDAAgD;IAChD,qFAAqF;IACrF,sBAAsB;IAEtB,sCAAsC;IACtC,QAAQ;IACR,iBAAiB;IAEjB,MAAM,6BACJ,8OAAC;QACC,OAAM;QACN,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,WAAU;;0BAEV,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,8OAAC;0BACC,cAAA,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,eAAc;;sCAEd,8OAAC;4BAAK,WAAU;;;;;;sCAChB,8OAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMnC,MAAM,yBAAyB,CAC7B,OACA,OACA,MACA,MACA,MACA,WAAW,KAAK,EAChB,gBAAgB,EAAE,EAClB,UAAU,IAAI,EACd,WAAW,KAAK,iBAEhB,8OAAC;YAAI,WAAU;;gBACZ,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAmC;gCAC5B;;;;;;;sCAEpB,8OAAC;4BAAK,WAAU;sCAAkD;;;;;;;;;;;;8BAMtE,8OAAC;oBAAE,WAAU;8BAAoC;;;;;;8BAEjD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAuF;gCAC9F;;;;;;;wBAEL,YAAY;;;;;;;8BAGf,8OAAC;oBAAE,WAAU;8BAAqC;;;;;;gBAEjD,yBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAO,WAAU;sCACf;;;;;;sCAGH,8OAAC;4BAAO,WAAU;sCACf;;;;;;;;;;;;;;;;;;IAOX,MAAM,2BACJ,8OAAC;QAAK,UAAU;QAAsB,WAAU;;0BAC9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CAAoF;;;;;;0CAKnG,8OAAC;gCACC,MAAK;gCACL,cAAa;gCACb,WAAU;gCACV,OAAO;gCACP,aAAY;gCACZ,KAAK,mBAAmB,KAAK;gCAC7B,UAAU;;;;;;;;;;;;kCAId,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,aAAa,SAAS,iBACtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA2D;;;;;;8CAGxE,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;;;;;;iDAKpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA2D;;;;;;8CAGxE,8OAAC;oCAAE,WAAU;8CAAuD;;;;;;;;;;;;;;;;;;;;;;;0BAO5E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;IAOP,MAAM,sBAAsB;QAC1B,OAAO;YACL,wBAAwB;YACxB,0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG;4BAC5B,MAAM,YAAY,cAAc,aAAa;4BAC7C,MAAM,QAAQ,KAAK,KAAK,CAAC,YAAY,KAAK,GAAG,CAAC,KAAK;4BAEnD,qBACE,8OAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,YAAY,CAAC,GAAG,EAAE,OAAO;0CAExC,cAAA,8OAAC;oCAAE,WAAU;;wCAA8B;wCAAI;;;;;;;+BAJ1C;;;;;wBAOX;;;;;;oBAGD;;;;;;;YAIL,aAAa;YACb,0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;kCAChD,8OAAC;wBAAE,WAAU;kCAAwE;;;;;;kCAGrF,8OAAC;wBAAE,WAAU;kCAAoC;;;;;;kCAGjD,8OAAC;wBAAO,WAAU;kCAA0I;;;;;;;;;;;;YAMhK,gBAAgB;YAChB,6BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;kCAGhD,8OAAC;wBAAE,WAAU;kCAAuF;;;;;;kCAGpG,8OAAC;wBAAE,WAAU;kCAAoC;;;;;;kCAIjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAA8J;;;;;;0CAIhL,8OAAC;gCAAO,WAAU;0CAA8J;;;;;;;;;;;;;;;;;;YAOtL,2BAA2B;YAC3B,sCACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAA2I;oCACjJ;kDACP,8OAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;0CAG/C,8OAAC;gCAAI,WAAU;0CAAyI;;;;;;;;;;;;oBAKzJ;;;;;;;YAIL,iBAAiB;YACjB,8BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;kCAChD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA6F;;;;;;4BAGzG;;;;;;;kCAEH,8OAAC;wBAAE,WAAU;kCAAoC;;;;;;kCAIjD,8OAAC;wBAAO,WAAU;kCAAwJ;;;;;;;;;;;;QAKhL;QAEA,QAAQ;YACN,aACE,6FAA6F;YAC7F,uBACE,iBACA,KACA,mCACA,kBACA;YAEJ,SAAS;YAET,gBAAgB,uBACd,sBACA,KACA,gCACA,IACA,IACA,MACA,KACA;YAGF,gBAAgB,uBACd,kBACA,KACA,iDACA,cACA,eACA,OACA,IACA,MACA;QAEJ;IACF;IACA,QAAQ,GAAG,CAAC,yBAAyB;IACrC,eAAe,mBAAmB,EAAE;QAClC,IAAI,iBAAiB,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE;QAC1C,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,IAAI,eAAe,MAAM,IAAI,KAAK;YAChC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,8BAA8B;YAC9B,SAAS,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE;YACtB,UAAU;YACV;QACF,OAAO;YACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gBAAgB,MAAM,WAAW;QAC/C;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa,OAAO,EAAE;IACxB;IAEA,IAAI,MAAM;IACV,QAAQ,GAAG,CAAC,IAAI,QAAQ,CAAC,UAAU;IACnC,MAAM,mBAAmB,OAAO;QAC9B,QAAQ,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK;QAEnC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,MAAM;YACR,IAAI,UAAU,KAAK,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,CAAC,SAAS;gBACZ,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,mBAAmB;YACnB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,IAAI;gBACF,MAAM,OAAO,MAAM,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE;gBACrC,QAAQ,GAAG,CAAC,0BAA0B;gBACtC,IAAI,KAAK,MAAM,IAAI,KAAK;oBACtB,UAAU,KAAK,IAAI,CAAC,GAAG;gBACzB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,GAAG,CAAC,uBAAuB;YACrC;QACF;IACF;IAEA,MAAM,cAAc,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE;QAChC,IACE,OAAO,QAAQ,YACf,OAAO,SAAS,YAChB,MAAM,QACN,MAAM,OACN;YACA,QAAQ,KAAK,CAAC,wBAAwB,KAAK;YAC3C,OAAO;QACT;QACA,QAAQ,GAAG,CAAC,eAAe,KAAK;QAChC,MAAM,SAAS,CAAC,8BAA8B,EAAE,IAAI,CAAC,EAAE,KAAK,kBAAkB,CAAC;QAE/E,qBACE,8OAAC;YACC,WAAU;YACV,SAAS,IAAM,OAAO,IAAI,CAAC;sBAE3B,cAAA,8OAAC;gBACC,KAAK;gBACL,OAAM;gBACN,QAAO;gBACP,iBAAgB;gBAChB,SAAQ;;;;;;;;;;;IAKd,2BAA2B;IAC7B;IAEA,IAAI,YAAY;QAAE,OAAO;QAAS,UAAU;IAAW;IACvD,MAAM,gBAAgB,CAAC;QACrB,IAAI,UAAU,KAAK,IAAI,QAAQ,SAAS,EAAE;YACxC,qBACE,8OAAC;gBACC,WAAW,CAAC,6EAA6E,EACvF,gBAAgB,gBAAgB,aAChC;gBACF,KAAK,QAAQ,QAAQ;gBACrB,QAAQ,IAAM,iBAAiB;;;;;;QAGrC,OAAO,IAAI,UAAU,QAAQ,IAAI,QAAQ,SAAS,EAAE;YAClD,QAAQ,GAAG;YACX,OAAO,YAAY,KAAK,KAAK,CAAC,QAAQ,IAAI;QAC5C,OAAO;YACL,qBACE,8OAAC;gBAAE,WAAU;0BACV,SAAS;;;;;;QAGhB;IACF;IAEA,IAAI,kBAAkB,CAAC;QACrB,IAAI,KAAK,WAAW,CAAC,SAAS,IAAI,UAAU,QAAQ,EAAE;YACpD,qBACE,8OAAC;gBAAE,WAAU;0BAAyF;;;;;;QAI1G,OAAO,IAAI,KAAK,WAAW,CAAC,SAAS,IAAI,UAAU,KAAK,EAAE;YACxD,qBACE,8OAAC;gBAAE,WAAU;0BAAyF;;;;;;QAI1G,OAAO;YACL,qBACE,8OAAC;gBAAE,WAAU;0BACV,MAAM,aAAa;;;;;;QAG1B;IACF;IACA,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,YAAY;IACxB,IAAI,YACF;IAEF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,4JAAA,CAAA,UAAO;gBACN,QAAO;gBACP,OAAM;gBACN,OAAM;gBACN,WAAU;gBACV,cAAc,CAAC;gBACf,cAAa;gBACb,SAAS;;;;;;;;;;;IAIjB,OACE,qBACE,8OAAC;QAAI,WAAW,GAAG,4KAAA,CAAA,UAAqB,CAAC,mBAAmB,CAAC,OAAO,CAAC;kBACnE,cAAA,8OAAC;;8BAEC,8OAAC;oBACC,WAAW,CAAC,CAAC,EACX,WAAW,WAAW,OACvB,4CAA4C,CAAC;;sCAE9C,8OAAC;4BAAG,WAAU;sCAAmD;;;;;;sCAIjE,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,cAAW;;kDAC9B,8OAAC;wCACC,OAAM;wCACN,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,GAAE;gDACF,OAAM;gDACN,QAAO;gDACP,IAAG;gDACH,MAAK;;;;;;0DAGP,8OAAC;gDACC,GAAE;gDACF,GAAE;gDACF,kBAAiB;gDACjB,YAAW;gDACX,UAAS;gDACT,MAAK;gDACL,YAAW;gDACX,YAAW;0DACZ;;;;;;;;;;;;kDAKH,8OAAC;wCACC,OAAM;wCACN,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,QAAO;gDACP,aAAY;;;;;;0DAEd,8OAAC;gDACC,GAAE;gDACF,GAAE;gDACF,OAAM;gDACN,QAAO;gDACP,IAAG;gDACH,MAAK;;;;;;0DAGP,8OAAC;gDACC,GAAE;gDACF,GAAE;gDACF,kBAAiB;gDACjB,YAAW;gDACX,UAAS;gDACT,MAAK;gDACL,YAAW;gDACX,YAAW;0DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAST,8OAAC;oBACC,WAAW,CAAC,2BAA2B,EACrC,uBAAuB,KAAK,YAC5B;;sCAGF,8OAAC;4BACC,WAAW,CAAC,CAAC,EAAE,WAAW,WAAW,GAAG,iBAAiB,EACvD,uBAAuB,eAAe,IACtC;;8CAEF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,WAAW,YACR,wBACA,WAAW,WACX,uBACA;;;;;;0DAEN,8OAAC,8IAAA,CAAA,iBAAc;gDACb,KAAK;gDACL,WAAW,CAAC,qEAAqE,EAC/E,aAAa,gBAAgB,IAC7B;gDACF,SAAS,IAAM,cAAc,CAAC;;;;;;4CAG/B,4BACC,8OAAC;gDACC,WAAW,CAAC,0EAA0E,CAAC;;kEAEvF,8OAAC;wDACC,WAAU;wDACV,SAAS,IAAM,cAAc;kEAC9B;;;;;;kEAGD,8OAAC;wDACC,WAAU;wDACV,SAAS,IAAM,cAAc;kEAC9B;;;;;;kEAGD,8OAAC;wDACC,WAAU;wDACV,SAAS,IAAM,cAAc;kEAC9B;;;;;;;;;;;;;;;;;;;;;;;gCAWR,qCACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,cAAa;gDACb,MAAK;gDACL,WAAU;gDACV,aAAY;gDACZ,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;0DAE/C,8OAAC,8IAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;2CAI1B;gCAID,CAAC,kBACA,qCACE,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,GAAG,IACjB;2CAAI;qCAAS,EAAE,IAAI,CAAC,MAAM;wCACxB,QAAQ,GAAG,CACT,kCACA,MAAM;wCAER,MAAM,WAAW,MAAM,iBAAiB,KACtC,CAAC,cAAgB,aAAa,QAAQ,UAAU;wCAElD,MAAM,WACJ,UAAU,QAAQ,MAAM,UAAU,CAAC,EAAE,EAAE,WAAW;wCACpD,QAAQ,GAAG,CAAC,YAAY;wCACxB,qBACE,8OAAC;4CAEC,WAAW,CAAC,gEAAgE,EAC1E,QAAQ,eAAe,KAAK,GAAG,CAAC,QAAQ,KACpC,4BACA,cACL,CAAC,CAAC;4CACH,SAAS;gDACP,SAAS,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE,KAAK,GAAG;gDAC9B,SAAS,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE,KAAK,YAAY;gDACvC,gBAAgB,KAAK,GAAG;4CAC1B;;8DAEA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KACE,MAAM,UAAU,QAAQ,SACpB,MAAM,UAAU,MAAM,CAAC,EAAE,GACzB,UAAU,aAAa,QAAQ;wDAErC,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAuD,GAAG,UAAU,UAAU,CAAC,EAAE,UAAU,UAAU;;;;;;wDAElH,gBAAgB;sEACjB,8OAAC;4DAAE,WAAU;;8EACX,8OAAC,8IAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,MAAM,aAAa,YAChB,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EACf,MAAM,aAAa,aAErB;8EACJ,8OAAC;oEAAK,WAAU;;;;;;gEACf,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAChB,MAAM,aAAa,aAAa,IAAI;;;;;;;;;;;;;8DAI1C,8OAAC;8DACC,cAAA,8OAAC,8IAAA,CAAA,kBAAe;wDACd,MAAM;wDACN,SAAS,IAAM,mBAAmB,KAAK,GAAG;;;;;;;;;;;;2CA5CzC,CAAC,UAAU,EAAE,KAAK;;;;;oCAiD7B,mBAEA,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;;;;;yDAMtE,8OAAC;oCAAI,WAAU;;;;;yDAGjB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAKnB,8OAAC;4BACC,WAAW,CAAC,EAAE,EACZ,WAAW,aAAa,SACzB,4BAA4B,CAAC;;gCAE7B,sCACC,8OAAC;oCACC,KAAK;oCACL,OAAO;wCACL,QACE,yHAAA,CAAA,qBAAkB,CAAC,cAAc,IAAI,aAAa,MAAM,GACpD,CAAC,SAAS,CAAC,GACX;oCACR;oCACA,WAAW,GACT,WAAW,UAAU,SACtB,8CAA8C,EAC7C,yHAAA,CAAA,qBAAkB,CAAC,cAAc,IAAI,aAAa,MAAM,GACpD,iCACA,IACJ;;wCAED,uCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,+IAAA,CAAA,cAAW;4DACV,WAAU;4DACV,SAAS;;;;;;sEAGX,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,KACE,cAAc,kBACd,UAAU,aAAa,QAAQ;4EAEjC,KAAI;4EACJ,WAAU;;;;;;sFAEZ,8OAAC;4EAAK,WAAU;sFACd,cAAA,8OAAC;gFACC,KACE,cAAc,gBACd,UAAU,aAAa,QAAQ;gFAEjC,KAAI;gFACJ,WAAU;;;;;;;;;;;;;;;;;8EAIhB,8OAAC;oEAAE,WAAU;8EACV,cAAc;;;;;;;;;;;;;;;;;;8DAKrB,8OAAC;oDACC,KAAK;oDACL,WAAU;;sEAEV,8OAAC,8IAAA,CAAA,wBAAqB;4DACpB,SAAS,IAAM,sBAAsB;;;;;;wDAEtC,oCACC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,SAAS,IACP,OAAO,IAAI,CACT,CAAC,gBAAgB,EAAE,aAAa,MAAM,EAAE;0EAG7C;;;;;;;;;;;;;;;;;;;;;;;sDASX,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,KAAK;gDACL,WAAW,CAAC,6EAA6E,EACvF,CAAC,mBACG,UACA,eAAe,UACf,mBACA,2BACL,oCAAoC,CAAC;0DAErC,UAAU,IAAI,CAAC,SAAS,MACvB,QAAQ,KAAK,eACb,SAAS,QAAQ,aACf,sBAAsB;kEACtB,8OAAC;wDAEC,WAAU;kEAET,cAAc;uDAHV,CAAC,QAAQ,EAAE,KAAK;;;;+DAMvB,oBAAoB;kEACpB,8OAAC;wDAEC,WAAU;kEAET,cAAc;uDAHV,CAAC,QAAQ,EAAE,KAAK;;;;;;;;;;;;;;;wCAU9B,yHAAA,CAAA,qBAAkB,CAAC,cAAc,KAAK,aAAa,MAAM,IACxD,uCACE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,qCAAqC,EAAE,4KAAA,CAAA,UAAqB,CAAC,cAAc,EAAE;;8EAEzF,8OAAC;oEACC,WAAW,CAAC,qLAAqL,EAC/L,4KAAA,CAAA,UAAqB,CAAC,cAAc,CACrC,CAAC,EAAE,mBAAmB,KAAK,IAAI;oEAChC,SAAS;;sFAET,8OAAC;4EAAI,OAAM;4EAAI,QAAO;sFACpB,cAAA,8OAAC;gFACC,IAAG;gFACH,IAAG;gFACH,IAAG;gFACH,IAAG;gFACH,IAAG;;kGAEH,8OAAC;wFAAK,WAAU;wFAAU,QAAO;;;;;;kGACjC,8OAAC;wFAAK,WAAU;wFAAU,QAAO;;;;;;;;;;;;;;;;;sFAGrC,8OAAC,8IAAA,CAAA,sBAAmB;4EAClB,OAAO;gFAAE,MAAM;4EAAsB;4EACrC,WAAW,CAAC,gDAAgD,EAC1D,mBAAmB,KAAK,eACxB;;;;;;;;;;;;8EAIN,8OAAC;oEACC,WAAW,CAAC,kBAAkB,EAC5B,aAAa,QAAQ,GAAG,WAAW,QACpC,yGAAyG,EACxG,eAAe,UACX,mBACA,+BACJ;oEACF,SAAS,IAAM,cAAc;;wEAE5B,eAAe,wBACd,8OAAC;4EACC,OAAM;4EACN,OAAM;4EACN,QAAO;4EACP,SAAQ;4EACR,MAAK;4EACL,WAAU;sFAEV,cAAA,8OAAC;gFACC,GAAE;gFACF,MAAK;;;;;;;;;;iGAIT,8OAAC;4EACC,OAAM;4EACN,OAAM;4EACN,QAAO;4EACP,SAAQ;4EACR,MAAK;4EACL,WAAU;;8FAEV,8OAAC;oFACC,GAAE;oFACF,MAAK;;;;;;8FAEP,8OAAC;8FACC,cAAA,8OAAC;wFACC,IAAG;wFACH,IAAG;wFACH,IAAG;wFACH,IAAG;wFACH,IAAG;wFACH,eAAc;;0GAEd,8OAAC;gGAAK,WAAU;;;;;;0GAChB,8OAAC;gGAAK,QAAO;gGAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;wEAIjC;;;;;;;gEAGH,CAAC,aAAa,QAAQ,kBACrB,8OAAC;oEACC,WAAW,CAAC,+HAA+H,EACzI,eAAe,eACX,gCACA,iBACL,CAAC,CAAC;oEACH,SAAS,IAAM,cAAc;;sFAE7B,8OAAC;4EACC,OAAM;4EACN,OAAM;4EACN,QAAO;4EACP,SAAQ;4EACR,MAAK;4EACL,WAAU;;8FAEV,8OAAC;oFACC,GAAE;oFACF,MAAM,GACJ,eAAe,eACX,mCACA,UACL,CAAC,CAAC;;;;;;8FAEL,8OAAC;8FACC,cAAA,8OAAC;wFACC,IAAG;wFACH,IAAG;wFACH,IAAG;wFACH,IAAG;wFACH,IAAG;wFACH,eAAc;;0GAEd,8OAAC;gGAAK,WAAU;;;;;;0GAChB,8OAAC;gGAAK,QAAO;gGAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;wEAG3B;;;;;;;;;;;;;sEAMZ,8OAAC;4DACC,WAAW,CAAC,+HAA+H,EACzI,mBACI,8BACA,yBACL,CAAC,CAAC;;gEACJ;8EAEC,8OAAC;;;;;gEAAK;;;;;;;sEAMR,8OAAC;4DACC,WAAW,CAAC,6CAA6C,EACvD,mBACI,8BACA,qBACJ;sEAED,eAAe,wBACd,8OAAC;gEAAI,WAAU;0EACZ,UAAU,OAAO,oBAAoB,YAClC,gBAAgB,KAAK,EAAE,IAAI,CAAC,MAAM,oBAChC,8OAAC;wEAEC,WAAW,CAAC,8OAA8O,CAAC;wEAC3P,SAAS,IAAM,YAAY;kFAE3B,cAAA,8OAAC;4EAAE,WAAU;sFACV;;;;;;uEALE,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;;;;gFASvC,gBAAgB,MAAM,EAAE,IAAI,CAAC,MAAM,oBACjC,8OAAC;wEAEC,WAAW,CAAC,8OAA8O,CAAC;wEAC3P,SAAS,IAAM,YAAY;kFAE3B,cAAA,8OAAC;4EAAE,WAAU;sFACV;;;;;;uEALE,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;;;;;;;;;uEAW7C,yDAAyD;4DACzD,2BAA2B;4DAC3B,4DAA4D;4DAC5D,IAAI;4DACJ,oBAAoB,KAAK,CACvB,6BACD;;;;;;;;;;;;8DAKP,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oEACb,WAAU;oEACV,SAAS;;;;;;;;;;;0EAIb,8OAAC;gEACC,WAAU;gEACV,UAAU;;kFAEV,8OAAC;wEACC,MAAK;wEACL,cAAa;wEACb,MAAK;wEACL,WAAU;wEACV,aAAY;;;;;;kFAGd,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,cAAa;gFACb,MAAK;gFACL,KAAK;gFACL,UAAU;gFACV,WAAU;;;;;;0FAEZ,8OAAC;gFACC,MAAK;gFACL,WAAU;gFACV,SAAS;0FAET,cAAA,8OAAC,8IAAA,CAAA,eAAY;oFAAC,MAAM;;;;;;;;;;;0FAEtB,8OAAC;gFACC,MAAK;gFACL,WAAU;0FAEV,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oFACb,MAAM;oFACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAW9B,8OAAC;oCACC,WAAW,GACT,uBAAuB,sBAAsB,OAC9C,4FAA4F,CAAC;;sDAE9F,8OAAC;4CAAE,WAAU;sDAAU;;;;;;sDAEvB,8OAAC;sDACC,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDACC,GAAE;wDACF,MAAK;;;;;;kEAEP,8OAAC;kEACC,cAAA,8OAAC;4DACC,IAAG;4DACH,IAAG;4DACH,IAAG;4DACH,IAAG;4DACH,IAAG;4DACH,eAAc;;8EAEd,8OAAC;oEAAK,WAAU;;;;;;8EAChB,8OAAC;oEAAK,QAAO;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKnC,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;gCAKzC,yHAAA,CAAA,qBAAkB,CAAC,cAAc,IAAI,aAAa,MAAM,kBACvD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAU,KAAK;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlE;uCAEe", "debugId": null}}]}