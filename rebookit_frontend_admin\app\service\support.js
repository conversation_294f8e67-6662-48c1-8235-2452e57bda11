import { axiosErrorHandler } from "../utils/axiosError.handler";
import instance from "./axios";
let uri={
    create:"/user/support-request",
    getRequest:"/user/filter-support-request",
    updateRequest:"user/update-support-request"

}
export const createSupportRequest=async(data)=>{
    let response = await instance
        .post(`${uri.create}`,data)
        .catch(axiosErrorHandler);
    console.log("createTestimonials response", response)
    return response
}


export const getSupportRequest=async(data,query)=>{
    
    let response = await instance
        .post(`${uri.getRequest}${query}`,data)
        .catch(axiosErrorHandler);
    console.log("createTestimonials response", response)
    return response
}

export const updateSupportRequest=async(data,query)=>{
    
    let response = await instance
        .put(`${uri.updateRequest}${query}`,data)
        .catch(axiosErrorHandler);
    console.log("createTestimonials response", response)
    return response
}

