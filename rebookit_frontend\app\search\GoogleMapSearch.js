import { useEffect, useRef, useState } from "react";
import { useLoadScript } from "@react-google-maps/api";
import { FaChevronDown } from "react-icons/fa6";
import { ParishesListEnum, ParishCoordinatesMap } from "../config/constant";
import { RxCross2 } from "react-icons/rx";
import { useDispatch, useSelector } from "react-redux";
import {
  getCurrentLocationAndAddress,
  ipBasedLocationFinder,
  parseGeocodeResponse,
} from "../utils/utils";
import { MdMyLocation } from "react-icons/md";
import { PiMapPinAreaDuotone } from "react-icons/pi";
import { toast } from "react-toastify";
import { updateUserLocationData } from "../redux/slices/storeSlice";

// Simple spinner component
const Spinner = () => (
  <svg
    className="animate-spin h-5 w-5 text-blue-500"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    style={{ minWidth: "20px", minHeight: "20px" }}
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    ></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
    ></path>
  </svg>
);

const libraries = ["places"];

function debounce(fn, delay) {
  let timer;
  return (...args) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn(...args), delay);
  };
}

const CustomAutocomplete = ({}) => {
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY,
    libraries,
  });
  const [isFocused, setIsFocused] = useState(false);
  const filterRef = useRef(null);
  const dispatch = useDispatch();
  const [suggestions, setSuggestions] = useState([]);
  const [expanded, setExpanded] = useState(false);
  const [showLocationBlockedModal, setShowLocationBlockedModal] =
    useState(false);

  const userLocationData = useSelector(
    (state) => state.storeData.userLocationData
  );

  const [locationData, setLocationData] = useState({ locality: "" });

  // For Google AutocompleteService instance
  const autocompleteServiceRef = useRef(null);

  // Debounce for input change
  const debouncedGetPredictions = useRef();

  // Loading state for current location
  const [isGettingCurrentLocation, setIsGettingCurrentLocation] =
    useState(false);
  const [isSearchingSuggestions, setIsSearchingSuggestions] = useState(false);
  const latestQuerySeqRef = useRef(0);
  const latestQueryValueRef = useRef("");

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (filterRef.current && !filterRef.current.contains(event.target)) {
        setIsFocused(false);
        setExpanded(false);
      }
    }
    if (isFocused || expanded) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isFocused, expanded]);

  useEffect(() => {
    if (isLoaded && window.google && !autocompleteServiceRef.current) {
      autocompleteServiceRef.current =
        new window.google.maps.places.AutocompleteService();
      // If user already typed something before script loaded, trigger a search now
      if (locationData?.locality) {
        debouncedGetPredictions.current?.(locationData.locality);
      }
    }
  }, [isLoaded]);

  useEffect(() => {
    // If userLocationData is not present in the store, fetch and store it
    if (
      !userLocationData ||
      !userLocationData.latitude ||
      !userLocationData.longitude
    ) {
      (async () => {
        try {
          const locationData = await ipBasedLocationFinder();
          dispatch(updateUserLocationData(locationData));
          setLocationData({
            locality: locationData.locality || "",
            latitude: locationData.latitude || "",
            longitude: locationData.longitude || "",
          });
        } catch (e) {
          console.warn("Failed to get user location data:", e);
        }
      })();
    } else {
      setLocationData({
        locality: userLocationData.locality || "",
        latitude: userLocationData.latitude || "",
        longitude: userLocationData.longitude || "",
      });
    }
  }, [userLocationData, dispatch]);

  // Debounced function to get place predictions
  useEffect(() => {
    debouncedGetPredictions.current = debounce((value) => {
      const trimmed = (value || "").trim();
      alert("ka")
      if (
        
        autocompleteServiceRef.current &&
        window.google &&
        window.google.maps &&
        window.google.maps.places &&
        trimmed
      ) {
        alert("dfddsf")
        const currentSeq = ++latestQuerySeqRef.current;
        const currentQuery = trimmed;
        latestQueryValueRef.current = currentQuery;
        try {
          autocompleteServiceRef.current.getPlacePredictions(
            { input: currentQuery },
            (predictions, status) => {
              // Ignore stale callbacks
              if (currentSeq !== latestQuerySeqRef.current) return;
              if (
                status === window.google.maps.places.PlacesServiceStatus.OK &&
                predictions
              ) {
                setSuggestions(predictions);
              } else {
                setSuggestions([]);
              }
              setIsSearchingSuggestions(false);
            }
          );
        } catch (e) {
          // Ensure loader is turned off on errors
          if (currentSeq === latestQuerySeqRef.current) {
            setIsSearchingSuggestions(false);
            setSuggestions([]);
          }
        }
      } else {
        setSuggestions([]);
        setIsSearchingSuggestions(false);
      }
    }, 300);
  }, [isLoaded]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setLocationData((pre) => ({ ...pre, locality: value }));
    // Start showing loader immediately when user types non-empty text
    if ((value || "").trim()) {
      setIsSearchingSuggestions(true);
    }
    if (!value || !(value || "").trim()) {
      setSuggestions([]);
      setIsSearchingSuggestions(false);
    }
    if (debouncedGetPredictions.current) {
      debouncedGetPredictions.current(value);
    }
  };

  const handleSelect = async (prediction) => {
    if (!window.google || !window.google.maps) return;
    const geocoder = new window.google.maps.Geocoder();
    await geocoder.geocode(
      { placeId: prediction.place_id },
      (results, status) => {
        if (
          status === "OK" &&
          results &&
          results[0] &&
          results[0].geometry?.location
        ) {
          const { lat, lng } = results[0].geometry.location;
          const locality = prediction.description;

          const locationPayload = {
            locality,
            latitude: typeof lat === "function" ? lat() : lat,
            longitude: typeof lng === "function" ? lng() : lng,
          };
          dispatch(updateUserLocationData(locationPayload));
          setLocationData(locationPayload);
          setSuggestions([]);
          setIsSearchingSuggestions(false);
        }
      }
    );
    setIsFocused(false);
    setExpanded(false);
  };

  const handleParishSelect = (parish) => {
    const parishCoordinates = ParishCoordinatesMap[parish];
    if (parishCoordinates) {
      const locationPayload = {
        locality: parish,
        latitude: parishCoordinates.lat,
        longitude: parishCoordinates.lng,
        isParishSelection: true,
      };
      dispatch(updateUserLocationData(locationPayload));
      setLocationData(locationPayload);
      setIsFocused(false);
      setExpanded(false);
    }
  };

  const handleGetCurrentLocation = async () => {
    if (navigator.permissions && navigator.permissions.query) {
      try {
        const result = await navigator.permissions.query({
          name: "geolocation",
        });
        if (result.state === "denied") {
          setShowLocationBlockedModal(true);
          return;
        }
      } catch (err) {
        setShowLocationBlockedModal(true);
      }
    }

    setIsGettingCurrentLocation(true);

    const fetchAndSetLocation = async () => {
      try {
        const addressData = await getCurrentLocationAndAddress(
          "getfullAddress"
        );
        const parsedAddress = parseGeocodeResponse(addressData);

        if (parsedAddress) {
          // Save in redux in the required format
          const locationData = {
            locality: parsedAddress.locality || "",
            latitude: parsedAddress.latitude || "",
            longitude: parsedAddress.longitude || "",
            currentLocation: true,
          };
          dispatch(updateUserLocationData(locationData));
        }
      } catch (err) {
        toast.error("Failed to get current location. Please try again.");
      } finally {
        setIsGettingCurrentLocation(false);
      }
    };

    fetchAndSetLocation();
  };

  return (
    <div className="md:ms-2 absolute z-[10]  overflow-hidden  border border-[#ccc] rounded-xl   w-full top-[10px]  ">
      <div
        ref={filterRef}
        className={`  border-[#ccc]  bg-white  transition-all duration-500 ease-in-out  ${
          expanded ? "h-[300px]" : ""
        }`}
      >
        <div className="flex items-center relative px-2">
          <input
            type="text"
            placeholder="Search for a location "
            className="w-full p-2 !h-[42px] outline-none cursor-pointer"
            style={{ height: "42px" }}
            value={locationData?.locality || ""}
            onFocus={() => {
              setIsFocused(true);
              setExpanded(true);
            }}
            onChange={handleInputChange}
          />
          <div className="flex items-center gap-2 ml-2">
            {/* Show spinner if loading current location */}
            {(isGettingCurrentLocation || isSearchingSuggestions) && (
              <span className="mr-1">
                <Spinner />
              </span>
            )}
            <FaChevronDown
              onClick={() => {
                setIsFocused((prev) => !prev);
                setExpanded((prev) => !prev);
              }}
              className={`transform cursor-pointer hover:text-blue-500 transition-transform duration-300 ${
                expanded ? "rotate-180" : ""
              }`}
            />
          </div>
        </div>
        {isFocused &&
          (suggestions?.length > 0 ? (
            <ul className=" z-10 bg-white w-full mt-1  max-h-[250px] overflow-auto">
              {suggestions?.map((prediction) => (
                <li
                  key={prediction.place_id}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  onClick={() => {
                    handleSelect(prediction);
                  }}
                >
                  {prediction.description}
                </li>
              ))}
            </ul>
          ) : (
            <div className="absolute top-[40] z-10 bg-white w-full mt-1 max-h-[250px] overflow-auto">
              {isSearchingSuggestions ? (
                <div className="px-4 py-3 flex items-center gap-2 text-gray-600">
                  <Spinner />
                  <span>Searching...</span>
                </div>
              ) : (
                <>
                  {/* Get Current Location Button */}
                  <div
                    className={`px-4 py-3 border-b border-gray-200 hover:bg-blue-50 cursor-pointer flex items-center gap-2 text-blue-600 font-medium ${
                      isGettingCurrentLocation
                        ? "opacity-60 pointer-events-none"
                        : ""
                    }`}
                    onClick={async () => {
                      if (!isGettingCurrentLocation) {
                        handleGetCurrentLocation();
                        setIsFocused(false);
                        setExpanded(false);
                      }
                    }}
                  >
                    <MdMyLocation size={18} />
                    <span>Get Current Location</span>
                    {isGettingCurrentLocation && (
                      <span className="ml-2">
                        <Spinner />
                      </span>
                    )}
                  </div>

                  <div className="px-4 py-2 text-[18px] mt-2 bg-gray-200">
                    {" "}
                    Popular Location
                  </div>
                  {Object.values(ParishesListEnum).map((item) => (
                    <div
                      key={item}
                      value={item}
                      onClick={() => {
                        handleParishSelect(item);
                      }}
                      className={`${
                        userLocationData?.locality == item ? "bg-gray-100" : ""
                      } flex items-center gap-2  px-4 py-2 hover:bg-gray-100 cursor-pointer`}
                    >
                      <PiMapPinAreaDuotone />
                      {item}
                    </div>
                  ))}
                </>
              )}
            </div>
          ))}
      </div>
      {/* Location Blocked Modal */}
      {showLocationBlockedModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4 relative">
            {/* Close button */}
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
              onClick={() => setShowLocationBlockedModal(false)}
            >
              <RxCross2 size={24} />
            </button>

            {/* Modal content */}
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                Geolocation is blocked
              </h2>
              <p className="text-gray-600 mb-6">
                Looks like your geolocation permissions are blocked. Please,
                provide geolocation access in your browser settings.
              </p>
              <button
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                onClick={() => setShowLocationBlockedModal(false)}
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomAutocomplete;
