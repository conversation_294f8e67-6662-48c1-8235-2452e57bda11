import React, { useEffect, useRef, useState } from 'react'
import { BsThreeDotsVertical } from "react-icons/bs";

export default function Action({item,updateSupportRequestFunc}) {
    const [opneList, setopneList] = useState(false)
    const menuRef =useRef(null)
    const DropDown = () => {
        return <div className=" absolute z-100 right-[20px] top-0 mt-1 min-w-[100px] max-w-[100px] w-fit rounded-md bg-white shadow-lg border border-slate-200 py-1">
            <div className="cursor-pointer w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100 transition-colors" onClick={()=>{
                updateSupportRequestFunc(item._id)
                setopneList(false)
                }}>close</div>
        </div>
    }
    useEffect(() => {
        const handleClickOutside = (e) => {
            
            if (menuRef.current && !menuRef.current.contains(e.target)) {
                // document.getElementById(data._id).classList.add("hidden");
                setopneList(false)
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return (
        <div className='relative  w-fit' ref={menuRef}>
            {!item.status&&<BsThreeDotsVertical onClick={() => setopneList(true)} className="cursor-pointer text-gray-500 hover:text-gray-800" />}
            {opneList && <DropDown />}
        </div>
    )
}
