{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/signup/signup.module.scss.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"fields\": \"signup-module-scss-module__t2cgJW__fields\",\n  \"imageContainer\": \"signup-module-scss-module__t2cgJW__imageContainer\",\n  \"imageElem\": \"signup-module-scss-module__t2cgJW__imageElem\",\n  \"signupContainer\": \"signup-module-scss-module__t2cgJW__signupContainer\",\n  \"submitButton\": \"signup-module-scss-module__t2cgJW__submitButton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/public/test.jpeg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 259, height: 194, blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAGAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwBttdXD6hFFY3tzbK3Ytu+tdWGnGpBJt3+Vjzq8pQm7JW+dz//Z\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,wGAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA24B,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/public/images/login1.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 660, height: 617, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA50lEQVR42g3KwUrCYADA8e9FImjmClokUaatdJsyoxTDFYUHW1LQsIZoEmQQdRhm0KbprQ4eguGlbj1BdPGZ/nr/CbuQxtI3KecNHloug8Eb3t0Vvtem131C1AopbDNBt5ojaNoMgw5f9+d8Ptb4cIuI7Q2Z41yScb/Db9Dm+bpEvaJye6LxUtEQK2mZi+ICfivL96tDv5Ek29hiP6XQrJcRSwcKrmMwCj3CUcDl4Q43pyq9qoFl7iIWo3NYpTx//xN+xiGmus6aEuHMkImvKjMgzRONSGT0DEd7GnoihhRbRnfi+MN3pmEcdrwDzwdlAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 7 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0Z,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/public/images/login2.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 660, height: 617, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA30lEQVR42g3OTUvCYADA8efcPaK13MaazWdme0mp1jZfm7WDRdibEtgLE4JAog6dOngMhDwKfYC+5F/vv8NPnDVbfPTapBWbbWWTolXk+aLDLL/mydcQf/Mfvkd9IrlDOzkhrbkEJZPQMbk01hD/ixm/kyGDJOAq8vlMq8S2jle2V7iCaIVV8vNj5nmft4bHV/eAjtSZ3GZM83tE7FiYmsawUeOl7tOUBaRVYDq+4/0mQ4wTF03dQlUUHk8j6vsSyzTIjlxit4Tolg3UjXX2TJ3F64BR6PEQB/QOV1lnlyW4qGOYXQvd1QAAAABJRU5ErkJggg==\", blurWidth: 8, blurHeight: 7 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAkZ,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/utils/axiosError.handler.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\r\nimport { getToken } from \"./utils\";\r\n// import history from \"./history\";\r\n\r\n\r\nexport const axiosErrorHandler = (error, action, checkUnauthorized = true) => {\r\n\r\n    const requestStatus = error?.request?.status;\r\n    const responseStatus = error?.response?.status;\r\n    const dataStatus = error?.data?.statusCode;\r\n    \r\n    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {\r\n        \r\n        // Clear local storage and redirect to /login\r\n        localStorage.clear();\r\n        toast.error(\r\n            error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n        );\r\n        window.location.href = \"/login\";\r\n    }\r\n    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {\r\n        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map(er => toast.error(er)) || error?.data?.error?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n            );\r\n    }\r\n    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {\r\n        // console.log(\"error log is\", error)\r\n        \r\n        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map(er => toast.error(er.message)) || error?.data?.message?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.message || error?.response?.data?.data || error?.data?.message,\r\n            );\r\n    }\r\n    if (\r\n        checkUnauthorized &&\r\n        (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)\r\n    ) {\r\n        if (getToken()) {\r\n            toast.error(error?.response?.data?.message);\r\n        }\r\n    }\r\n\r\n    if (action === \"uploadImage\") {\r\n        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {\r\n            if (getToken()) {\r\n                const message = error?.response?.data?.message;\r\n                message && toast.error(message);\r\n            } else history.push(\"/\");\r\n        }\r\n    }\r\n\r\n    if (error?.response) return error.response;\r\n    else if (error?.request) return error.request;\r\n    else return error?.message;\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIO,MAAM,oBAAoB,CAAC,OAAO,QAAQ,oBAAoB,IAAI;IAErE,MAAM,gBAAgB,OAAO,SAAS;IACtC,MAAM,iBAAiB,OAAO,UAAU;IACxC,MAAM,aAAa,OAAO,MAAM;IAEhC,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QAEvE,6CAA6C;QAC7C,aAAa,KAAK;QAClB,sJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;QAEjF,OAAO,QAAQ,CAAC,IAAI,GAAG;IAC3B;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QACvE,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,UAAU,MAAM,OAAO,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,OAAO,MAAM,OAAO,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEzL,sJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;IAEzF;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,kBAAkB,KAAK;QAChG,qCAAqC;QAErC,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,WAAW,OAAO,QAAQ,OAAO,MAAM,SAAS,OAAO,UAAU,MAAM,QAAQ,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,OAAO,MAAM,OAAO,MAAM,SAAS,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEtM,sJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,WAAW,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM;IAE1F;IACA,IACI,qBACA,CAAC,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,GAAG,GACxE;QACE,IAAI,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,KAAK;YACZ,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM;QACvC;IACJ;IAEA,IAAI,WAAW,eAAe;QAC1B,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;YACvE,IAAI,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,KAAK;gBACZ,MAAM,UAAU,OAAO,UAAU,MAAM;gBACvC,WAAW,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAC3B,OAAO,QAAQ,IAAI,CAAC;QACxB;IACJ;IAEA,IAAI,OAAO,UAAU,OAAO,MAAM,QAAQ;SACrC,IAAI,OAAO,SAAS,OAAO,MAAM,OAAO;SACxC,OAAO,OAAO;AACvB", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/services/axios.js"], "sourcesContent": ["const { default: axios } = require(\"axios\");\r\nconst { getToken } = require(\"../utils/utils\");\r\n\r\nconst BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst instance = axios.create({\r\n  baseURL: BASE_URL+\"/api\" ,\r\n\r\n  // Lets keep a check as default is 0 millisecond i.e. never\r\n  // Note: timeout is only for server response not network i.e. server reachability\r\n  timeout: 100000,\r\n\r\n  // Lets keep a check as default bytes- 2k\r\n  maxContentLength: 1000,\r\n\r\n  // Lets keep a check as default 5 seems high\r\n  maxRedirects: 2,\r\n});\r\n\r\ninstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = getToken();\r\n    console.log(\"token\", token)\r\n    \r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // Rate limiting: only fire a request every 2 sec from lodash.debounce\r\n    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });\r\n    return Promise.resolve(config);\r\n  },\r\n  function (error) {\r\n    const response = handleLogError(error); // log them\r\n\r\n    return Promise.reject(error);\r\n  }\r\n  // multiple options as to when and how to apply these interceptors\r\n  // , { synchronous: true, runWhen: onGetCall }\r\n);\r\n\r\n\r\nmodule.exports = instance;"], "names": [], "mappings": "AAGiB;AAHjB,MAAM,EAAE,SAAS,KAAK,EAAE;AACxB,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM;AAEN,MAAM,WAAW,MAAM,MAAM,CAAC;IAC5B,SAAS,WAAS;IAElB,2DAA2D;IAC3D,iFAAiF;IACjF,SAAS;IAET,yCAAyC;IACzC,kBAAkB;IAElB,4CAA4C;IAC5C,cAAc;AAChB;AAEA,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;IACC,MAAM,QAAQ;IACd,QAAQ,GAAG,CAAC,SAAS;IAErB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,sEAAsE;IACtE,8EAA8E;IAC9E,OAAO,QAAQ,OAAO,CAAC;AACzB,GACA,SAAU,KAAK;IACb,MAAM,WAAW,eAAe,QAAQ,WAAW;IAEnD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAMF,OAAO,OAAO,GAAG", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/services/auth.js"], "sourcesContent": ["import { USER_ROUTES } from \"../config/api\";\r\nimport { axiosErrorHandler } from \"../utils/axiosError.handler\";\r\nimport { getToken } from \"../utils/utils\";\r\nimport instance from \"./axios\";\r\n\r\n\r\nconst uri = {\r\n    login: \"/user/login\",\r\n    userInfo: \"/user\",\r\n    editProfile:\"/user/edit-profile\",\r\n    verifyOtp:\"user/verify-otp\",\r\n    registerUser: \"/user/register\",\r\n    forgetPassword: \"user/forgot-password\",\r\n    createNewPassword: \"user/create-new-password\",\r\n    sendOtp: \"user/send-otp\",\r\n    verifyForgetPassword: \"/user/verify-forgot-otp\",\r\n    resetPassword:\"/user/reset-password\"\r\n};\r\n\r\n\r\n\r\n// fetch( {\r\n//     method: \"POST\",\r\n//     headers: {\r\n//         \"Content-Type\": \"application/json\"\r\n//     },\r\n//     body: JSON.stringify(data)\r\n// }).then(async res => {\r\n//     const response = await res.json();\r\n//     let authData = response\r\n//     console.log(\"response auth\", response)\r\n//     if (authData?.success) {\r\n//         setToken(authData?.token)\r\n//         toast.success(\"Logged in successfully!\")\r\n//         router.push(\"/\")\r\n//     } else {\r\n//         toast.error(response?.message || \"Incorrect credentials! Try again\")\r\n//     }\r\n// })\r\n\r\n\r\n// const login = async (data) => {\r\n//     try {\r\n//         let userToken = getToken()\r\n//         let response = await RestCall({\r\n//             method: \"get\",\r\n//             url: `${USER_ROUTES.LIST_ITEM}/${id}`,\r\n//             headers: {\r\n//                 \"Content-Type\": \"application/json\",\r\n//             }, data: data\r\n//         })\r\n//         return response\r\n//     } catch (err) {\r\n//         return { message: err.message, code: err.statusCode }\r\n//     }\r\n// }\r\n\r\n\r\nexport const login = async (payload, guestId) => {\r\n    let response = await instance\r\n        .post(`${uri.login}`,payload)\r\n        .catch(axiosErrorHandler);\r\n        console.log(\"login test response\",response)\r\n        return response \r\n};\r\nexport const userInfo_api = async () => {\r\n    if (getToken()) {\r\n    try{\r\n        let response = await instance\r\n            .get(`${uri.userInfo}`)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"login test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }}\r\n};\r\nexport const update_userInfo_api = async (userPayload) => {\r\n    try{\r\n        let response = await instance\r\n            .put(`${uri.editProfile}`,userPayload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"login test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\nexport const verifyOtp = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.verifyOtp}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"verifyOtp test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\nexport const registerUser = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.registerUser}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"registerUser test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\n\r\n\r\nexport const forgotPassword = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.forgetPassword}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"registerUser test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\n\r\nexport const createNewPassword = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.forgetPassword}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"registerUser test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\nexport const sendOTP = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.sendOtp}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"sendOTP test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\nexport const verifyForgetPassword = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.verifyForgetPassword}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"sendOTP test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\n\r\nexport const resetPassword = async (payload) => {\r\n    try{\r\n        let response = await instance\r\n            .post(`${uri.resetPassword}`,payload)\r\n            .catch(axiosErrorHandler);\r\n            console.log(\"resetPassword test response\",response)\r\n            return response \r\n    }catch(err){\r\n        console.log(\"err in userInfo_api\",err)\r\n    }\r\n};\r\n\r\n\r\n\r\n\r\n// fetch(USER_ROUTES.EDIT_USER_INFO, {\r\n//                 method: \"put\",\r\n//                 headers: {\r\n//                     \"Authorization\": `Bearer ${userToken}`,\r\n//                     \"Content-Type\": \"application/json\"\r\n\r\n//                 },\r\n//                 body: JSON.stringify(userPayload)\r\n\r\n//             }).then(async res => {\r\n//                 const response = await res.json();\r\n//                 if (!response.error) {\r\n//                     toast.success(response.message)\r\n//                     setBtnDisabled(true)\r\n//                 } else {\r\n//                     response.message?.map(x => toast.error(x))\r\n//                     toast.error(response.message || \"No Info Found\")\r\n//                 }\r\n//             })\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAGA,MAAM,MAAM;IACR,OAAO;IACP,UAAU;IACV,aAAY;IACZ,WAAU;IACV,cAAc;IACd,gBAAgB;IAChB,mBAAmB;IACnB,SAAS;IACT,sBAAsB;IACtB,eAAc;AAClB;AAyCO,MAAM,QAAQ,OAAO,SAAS;IACjC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,KAAK,EAAE,EAAC,SACpB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;IACxB,QAAQ,GAAG,CAAC,uBAAsB;IAClC,OAAO;AACf;AACO,MAAM,eAAe;IACxB,IAAI,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,KAAK;QAChB,IAAG;YACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,QAAQ,EAAE,EACrB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;YACxB,QAAQ,GAAG,CAAC,uBAAsB;YAClC,OAAO;QACf,EAAC,OAAM,KAAI;YACP,QAAQ,GAAG,CAAC,uBAAsB;QACtC;IAAC;AACL;AACO,MAAM,sBAAsB,OAAO;IACtC,IAAG;QACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,GAAG,CAAC,GAAG,IAAI,WAAW,EAAE,EAAC,aACzB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,uBAAsB;QAClC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAEO,MAAM,YAAY,OAAO;IAC5B,IAAG;QACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,SAAS,EAAE,EAAC,SACxB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,2BAA0B;QACtC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAEO,MAAM,eAAe,OAAO;IAC/B,IAAG;QACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,YAAY,EAAE,EAAC,SAC3B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,8BAA6B;QACzC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAIO,MAAM,iBAAiB,OAAO;IACjC,IAAG;QACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,cAAc,EAAE,EAAC,SAC7B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,8BAA6B;QACzC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAGO,MAAM,oBAAoB,OAAO;IACpC,IAAG;QACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,cAAc,EAAE,EAAC,SAC7B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,8BAA6B;QACzC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAEO,MAAM,UAAU,OAAO;IAC1B,IAAG;QACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,OAAO,EAAE,EAAC,SACtB,KAAK,CAAC,wIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,yBAAwB;QACpC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAEO,MAAM,uBAAuB,OAAO;IACvC,IAAG;QACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,oBAAoB,EAAE,EAAC,SACnC,KAAK,CAAC,wIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,yBAAwB;QACpC,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ;AAGO,MAAM,gBAAgB,OAAO;IAChC,IAAG;QACC,IAAI,WAAW,MAAM,2HAAA,CAAA,UAAQ,CACxB,IAAI,CAAC,GAAG,IAAI,aAAa,EAAE,EAAC,SAC5B,KAAK,CAAC,wIAAA,CAAA,oBAAiB;QACxB,QAAQ,GAAG,CAAC,+BAA8B;QAC1C,OAAO;IACf,EAAC,OAAM,KAAI;QACP,QAAQ,GAAG,CAAC,uBAAsB;IACtC;AACJ,GAKA,sCAAsC;CACtC,iCAAiC;CACjC,6BAA6B;CAC7B,8DAA8D;CAC9D,yDAAyD;CAEzD,qBAAqB;CACrB,oDAAoD;CAEpD,qCAAqC;CACrC,qDAAqD;CACrD,yCAAyC;CACzC,sDAAsD;CACtD,2CAA2C;CAC3C,2BAA2B;CAC3B,iEAAiE;CACjE,uEAAuE;CACvE,oBAAoB;CACpB,iBAAiB", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/components/common/SubmitButton.js"], "sourcesContent": ["import React, {useState} from \"react\";\r\n\r\nexport default function SubmitButton({isLoading, InnerDiv, type, btnAction}) {\r\n  const [isDisabled, setIsDisabled] = useState(false);\r\n\r\n  const handleClick = (e) => {\r\n    if (isDisabled || isLoading) return;\r\n\r\n    setIsDisabled(true);\r\n    btnAction?.(e); // safely call btnAction if defined\r\n\r\n    // Re-enable after 1 second (adjust if needed)\r\n    setTimeout(() => setIsDisabled(false), 2000);\r\n  };\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      onClick={handleClick}\r\n      disabled={isLoading}\r\n      //   className=\"mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5\"\r\n      className={`mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5 ${\r\n        isDisabled || isLoading ? \"opacity-50 cursor-not-allowed\" : \"\"\r\n      }`}\r\n    >\r\n      {isLoading ? (\r\n        <>\r\n          <svg\r\n            className=\"w-4 h-4 animate-spin text-white\"\r\n            fill=\"none\"\r\n            viewBox=\"0 0 24 24\"\r\n          >\r\n            <circle\r\n              className=\"opacity-25\"\r\n              cx=\"12\"\r\n              cy=\"12\"\r\n              r=\"10\"\r\n              stroke=\"currentColor\"\r\n              strokeWidth=\"4\"\r\n            ></circle>\r\n            <path\r\n              className=\"opacity-75\"\r\n              fill=\"currentColor\"\r\n              d=\"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\r\n            ></path>\r\n          </svg>\r\n          Loading...\r\n        </>\r\n      ) : (\r\n        <InnerDiv />\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAEe,SAAS,aAAa,EAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAC;;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,cAAc,CAAC;QACnB,IAAI,cAAc,WAAW;QAE7B,cAAc;QACd,YAAY,IAAI,mCAAmC;QAEnD,8CAA8C;QAC9C,WAAW,IAAM,cAAc,QAAQ;IACzC;IAEA,qBACE,6LAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU;QACV,8HAA8H;QAC9H,WAAW,CAAC,8GAA8G,EACxH,cAAc,YAAY,kCAAkC,IAC5D;kBAED,0BACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,SAAQ;;sCAER,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BA<PERSON>,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAEA;;yCAIR,6LAAC;;;;;;;;;;AAIT;GAnDwB;KAAA", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/signup/signUpComponent.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport signUpCss from \"./signup.module.scss\";\r\n\r\nimport dummyImage from \"@/public/test.jpeg\";\r\nimport login1 from \"@/public/images/login1.png\";\r\nimport login2 from \"@/public/images/login2.png\";\r\n\r\nconst CustomSlider = dynamic(() => import(\"@/app/components/common/Slider\"));\r\nconst BuyAndSellComponent = dynamic(() =>\r\n  import(\"@/app/components/about/BuyAndSellComponent\")\r\n);\r\n\r\nimport dynamic from \"next/dynamic\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useState } from \"react\";\r\nimport { FaRegEyeSlash } from \"react-icons/fa\";\r\nimport { IoEyeSharp } from \"react-icons/io5\";\r\nimport { toast } from \"react-toastify\";\r\nimport { USER_ROUTES } from \"../config/api\";\r\nimport { getToken, setInLocalStorage, setToken } from \"../utils/utils\";\r\nimport { registerUser } from \"../services/auth\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { setVerificationData } from \"../redux/slices/storeSlice\";\r\nimport SubmitButton from \"../components/common/SubmitButton\";\r\n\r\nexport default function SignUpComponent() {\r\n  const router = useRouter();\r\n\r\n  const {\r\n    register,\r\n    watch,\r\n    formState: { errors },\r\n    getValues,\r\n    handleSubmit,\r\n    setValue,\r\n    reset,\r\n    setError,\r\n  } = useForm();\r\n\r\n  const Images = [login1, login2];\r\n\r\n  const [passwordView, setPasswordView] = useState(false);\r\n  const [confirm_passwordView, setconfirm_passwordView] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [location, setLocation] = useState(null);\r\n  const storeData = useSelector((state) => state.storeData);\r\n  const dispatch = useDispatch();\r\n  console.log(\"storeData\", storeData);\r\n  const handleGetLocation = () => {\r\n    if (!navigator.geolocation) {\r\n      alert(\"Geolocation is not supported by your browser.\");\r\n      return;\r\n    }\r\n\r\n    navigator.geolocation.getCurrentPosition(\r\n      (position) => {\r\n        setLocation({\r\n          lat: position.coords.latitude,\r\n          lng: position.coords.longitude,\r\n        });\r\n      },\r\n      (err) => {\r\n        if (err.code === err.PERMISSION_DENIED) {\r\n          // alert(\"Location access was blocked. Please enable it in your browser settings.\");\r\n          console.log(\"err\", err);\r\n        } else {\r\n          // alert(\"Unable to retrieve location. Please try again.\");\r\n          console.log(\"err\", err);\r\n        }\r\n      }\r\n    );\r\n  };\r\n\r\n  useEffect(() => {\r\n    const token = getToken();\r\n    if (token) {\r\n      router.replace(\"/profile\");\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => handleGetLocation(), []);\r\n\r\n  const onSubmit = async (data) => {\r\n      \r\n    console.log(\"Form submitted:\", data);\r\n    handleGetLocation();\r\n\r\n    // let passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/\r\n    // if (!passwordRegex.test(data.password)) {\r\n    //     toast.error(\"In password Need 1 upper case 1 speacial Character and 1 number atleast\")\r\n    //     return\r\n    // }\r\n\r\n    if (!location) {\r\n      // console.log(\"location blocked\")\r\n      // alert(\"Please allow location to continue.\")\r\n      // return;\r\n      setLocation({\r\n        lat: 0,\r\n        lng: 0,\r\n      });\r\n    }\r\n\r\n    try {\r\n      const payload = {\r\n        ...data,\r\n        email: storeData.verficationData.email,\r\n        location: {\r\n          type: \"Point\",\r\n          coordinates: Object.values(location),\r\n        },\r\n      };\r\n\r\n      delete payload.Confirm_Password;\r\n      delete payload.agreed;\r\n      let authData = await registerUser(payload);\r\n      console.log(\"authData\", authData);\r\n      if (authData.status == 200) {\r\n        console.log(\"authData?.token\", authData?.token);\r\n        setToken(authData?.data.token);\r\n        toast.success(\"Signed Up successfully!\");\r\n        setInLocalStorage(\"userData\", authData?.data.user);\r\n        dispatch(setVerificationData({}));\r\n        router.push(\"/\");\r\n      } else {\r\n        Array.isArray(authData.message)\r\n          ? authData.message?.map((x) => toast.error(x))\r\n          : toast.error(\r\n            authData.message || \"Error signing up! Please try again\"\r\n          );\r\n      }\r\n    } catch (error) {\r\n      console.log(\"error\", error);\r\n    }\r\n  };\r\n\r\n  const settings = {\r\n    dots: true,\r\n    infinite: true,\r\n    speed: 500,\r\n    slidesToShow: 1,\r\n    slidesToScroll: 1,\r\n    autoplay: true,\r\n    autoplaySpeed: 3000,\r\n  };\r\n\r\n  console.log(\"errors\", errors);\r\n\r\n  return (\r\n    <section className={`${signUpCss.signupContainer}`}>\r\n      <section className=\"container-wrapper w-full\">\r\n        <section className=\"md:flex flex-col lg:flex-row lg:justify-between justify-center xs:items-center\">\r\n          <section className=\"lg:w-1/2 lg:min-w-[600px] lg:max-w-[700px]\">\r\n            <div className={`${signUpCss.imageContainer}`}>\r\n              {/* <LoginSlider ImageSlider={Images} /> */}\r\n\r\n              <CustomSlider sliderSettings={settings}>\r\n                {Images.map((image, idx) => (\r\n                  <div\r\n                    className={`relative overflow-hidden ${signUpCss.imageElem}`}\r\n                  >\r\n                    <Image\r\n                      key={idx}\r\n                      src={image}\r\n                      alt=\"login page images\"\r\n                      className=\"h-full w-full rounded-2xl p-1\"\r\n                    />\r\n                  </div>\r\n                ))}\r\n              </CustomSlider>\r\n            </div>\r\n          </section>\r\n\r\n          <section className=\"md:w-5/12 md:mt-20 md:min-w-[600px]\">\r\n            <h2 className=\"text-[18px] font-semibold md:text-[40px] md:font-semibold\">\r\n              Sign Up\r\n            </h2>\r\n            <p className=\"mt-3 font-extralight text-[14px] md:text-[14px] w-10/12\">\r\n              Let’s get you all set up so you can access your personal account.\r\n            </p>\r\n\r\n            <form\r\n              onSubmit={handleSubmit(onSubmit)}\r\n              className=\"text-[#1C1B1F] my-4\"\r\n            >\r\n              {/* FirstName Input */}\r\n              <div className=\"md:flex justify-between items-center\">\r\n                <div className=\"relative py-2 h-[90px] md:h-[100px] md:w-[47%]\">\r\n                  <fieldset className={` ${signUpCss.fields}`}>\r\n                    <legend className=\"text-[14px] font-medium px-[7px]\">\r\n                      FirstName\r\n                    </legend>\r\n                    <label htmlFor=\"firstName\" className=\"sr-only\">\r\n                      FirstName\r\n                    </label>\r\n                    <input\r\n                      id=\"firstName\"\r\n                      type=\"text\"\r\n                      autoComplete=\"off\"\r\n                      {...register(\"firstName\", {\r\n                        required: \"firstName is required\",\r\n                        maxLength: {\r\n                          value: 70,\r\n                          message: \"Maximum 70 letter Allowed\",\r\n                        },\r\n                      })}\r\n                      className=\"w-full text-[13px] md:text-[17px] outline-none border-none\"\r\n                      aria-invalid={!!errors?.firstName}\r\n                      aria-describedby=\"email-error\"\r\n                    />\r\n                  </fieldset>\r\n\r\n                  {errors?.firstName && (\r\n                    <p\r\n                      id=\"email-error\"\r\n                      className=\"text-red-500 text-[12px] mt-1 absolute right-3 bottom-0\"\r\n                    >\r\n                      {errors?.firstName.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"relative py-2 h-[90px] md:h-[100px] md:w-[47%]\">\r\n                  <fieldset className={` ${signUpCss.fields}`}>\r\n                    <legend className=\"text-[14px] font-medium px-[7px]\">\r\n                      LastName\r\n                    </legend>\r\n                    <label htmlFor=\"lastName\" className=\"sr-only\">\r\n                      LastName\r\n                    </label>\r\n                    <input\r\n                      id=\"lastName\"\r\n                      type=\"text\"\r\n                      autoComplete=\"off\"\r\n                      {...register(\"lastName\", {\r\n                        required: \"lastName is required\",\r\n                        maxLength: {\r\n                          value: 70,\r\n                          message: \"Maximum 70 letter allowed\",\r\n                        },\r\n                      })}\r\n                      className=\"w-full text-[13px] md:text-[17px] outline-none border-none\"\r\n                      aria-invalid={!!errors.lastName}\r\n                    // aria-describedby=\"email-error\"\r\n                    />\r\n                  </fieldset>\r\n                  {errors.lastName && (\r\n                    <p\r\n                      id=\"email-error\"\r\n                      className=\"text-red-500 text-[12px] mt-1 absolute right-3 bottom-0\"\r\n                    >\r\n                      {errors.lastName.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Password Input */}\r\n              <div className=\"relative h-[90px] md:h-[100px]\">\r\n                <fieldset className={` ${signUpCss.fields}`}>\r\n                  <legend className=\"text-[14px] font-medium px-[7px]\">\r\n                    Password\r\n                  </legend>\r\n                  <label htmlFor=\"password\" className=\"sr-only\">\r\n                    Password\r\n                  </label>\r\n                  <input\r\n                    id=\"password\"\r\n                    type={passwordView ? \"text\" : \"password\"}\r\n                    autoComplete=\"off\"\r\n                    // autoComplete=\"current-password\"\r\n                    {...register(\"password\", {\r\n                      required: \"Valid Password is Required\",\r\n                      maxLength: {\r\n                        value: 70,\r\n                        message: \"Maximum 70 letter allowed\",\r\n                      },\r\n                    })}\r\n                    className=\"w-[90%]  text-[13px] md:text-[17px] outline-none \"\r\n                    aria-invalid={!!errors.password}\r\n                    aria-describedby=\"password-error\"\r\n                  />\r\n                  <div\r\n                    className=\"absolute top-[29px] right-[17px]\"\r\n                    onClick={() => setPasswordView(!passwordView)}\r\n                    role=\"button\"\r\n                    tabIndex={0}\r\n                    aria-label={\r\n                      passwordView ? \"Hide password\" : \"Show password\"\r\n                    }\r\n                    onKeyDown={(e) =>\r\n                      e.key === \"Enter\" && setPasswordView(!passwordView)\r\n                    }\r\n                  >\r\n                    {passwordView ? (\r\n                      <FaRegEyeSlash className=\"cursor-pointer\" size={20} />\r\n                    ) : (\r\n                      <IoEyeSharp className=\"cursor-pointer\" size={20} />\r\n                    )}\r\n                  </div>\r\n                </fieldset>\r\n                {errors.password && (\r\n                  <p\r\n                    id=\"password-error\"\r\n                    className=\"text-red-500 text-[12px] mt-1 absolute right-3 bottom-0\"\r\n                  >\r\n                    {errors.password.message}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"relative h-[90px] md:h-[100px]\">\r\n                <fieldset className={` ${signUpCss.fields}`}>\r\n                  <legend className=\"text-[14px] font-medium px-[7px]\">\r\n                    Confirm Password\r\n                  </legend>\r\n                  <label htmlFor=\"password\" className=\"sr-only\">\r\n                    Confirm Password\r\n                  </label>\r\n                  <input\r\n                    id=\"confirm-password\"\r\n                    type={confirm_passwordView ? \"text\" : \"password\"}\r\n                    autoComplete=\"off\"\r\n                    {...register(\"Confirm_Password\", {\r\n                      required: \"Valid Password is Required\",\r\n                      validate: (value) =>\r\n                        value === watch(\"password\") || \"Passwords do not match\",\r\n                    })}\r\n                    className=\"w-[90%] text-[13px] md:text-[17px] outline-none border-none\"\r\n                    aria-invalid={!!errors.Confirm_Password}\r\n                    aria-describedby=\"password-error\"\r\n                  />\r\n                  <div\r\n                    className=\"absolute top-[29px] right-[17px]\"\r\n                    onClick={() =>\r\n                      setconfirm_passwordView(!confirm_passwordView)\r\n                    }\r\n                    role=\"button\"\r\n                    tabIndex={0}\r\n                    aria-label={\r\n                      confirm_passwordView ? \"Hide password\" : \"Show password\"\r\n                    }\r\n                    onKeyDown={(e) =>\r\n                      e.key === \"Enter\" &&\r\n                      setconfirm_passwordView(!confirm_passwordView)\r\n                    }\r\n                  >\r\n                    {confirm_passwordView ? (\r\n                      <FaRegEyeSlash className=\"cursor-pointer\" size={20} />\r\n                    ) : (\r\n                      <IoEyeSharp className=\"cursor-pointer\" size={20} />\r\n                    )}\r\n                  </div>\r\n                </fieldset>\r\n                {errors.Confirm_Password && (\r\n                  <p\r\n                    id=\"password-error\"\r\n                    className=\"text-red-500 text-[12px] mt-1 absolute right-3 bottom-0\"\r\n                  >\r\n                    {errors.Confirm_Password.message}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Remember Me + Forgot Password */}\r\n              <div className=\"mb-4 flex items-center justify-between relative\">\r\n                <div className=\"mb-4 flex items-center\">\r\n                  <input\r\n                    id=\"agree\"\r\n                    type=\"checkbox\"\r\n                    autoComplete=\"off\"\r\n                    className=\"mr-2 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n                    {...register(\"agreed\", {\r\n                      required: \"Agree to the terms and policies\",\r\n                    })}\r\n                  />\r\n                  <label className=\"text-[12px] md:text-[16px] text-gray-700\">\r\n                    I agree to all the{\" \"}\r\n                    <span className=\"text-[#FF8682]\">Terms</span> and{\" \"}\r\n                    <a\r\n                      href=\"/privacy-policy\"\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      className=\"text-[#FF8682] cursor-pointer\"\r\n                    >   <span className=\"text-[#FF8682] cursor-pointer\" >Privacy Policies</span></a>\r\n                  </label>\r\n                  {errors.agreed && (\r\n                    <p\r\n                      id=\"password-error\"\r\n                      className=\"text-red-500 text-[11px] mt-1 absolute left-0 bottom-0\"\r\n                    >\r\n                      {errors.agreed.message}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Submit Button */}\r\n              {/* <button\r\n                isLoading={isLoading}\r\n                type=\"submit\"\r\n                className={`${signUpCss.submitButton}`}\r\n              >\r\n                Create Account\r\n              </button> */}\r\n\r\n              <SubmitButton\r\n                isLoading={isLoading}\r\n                type=\"submit\"\r\n                btnAction={null} // because submit handled by react-hook-form\r\n                InnerDiv={() => (\r\n                  <span className={`${signUpCss.submitButton}`}>\r\n                    Create Account\r\n                  </span>\r\n                )}\r\n              />\r\n\r\n              {/* Sign up prompt */}\r\n              <p className=\"text-center text-[12px] md:text-[16px] my-3\">\r\n                Already have an account?{\" \"}\r\n                <Link href=\"/login\">\r\n                  <span className=\"text-[#FF8682] font-medium cursor-pointer\">\r\n                    Login\r\n                  </span>\r\n                </Link>\r\n              </p>\r\n            </form>\r\n          </section>\r\n        </section>\r\n\r\n        <section className=\"my-5 md:my-0 md:w-12/12 \">\r\n          <BuyAndSellComponent />\r\n        </section>\r\n      </section>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA5BA;;;;;;;AAUA,MAAM,eAAe,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;KAAvB;AACN,MAAM,sBAAsB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;MAA9B;;;;;;;;;;;;;;;AAmBS,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACrB,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD;IAEV,MAAM,SAAS;QAAC,uRAAA,CAAA,UAAM;QAAE,uRAAA,CAAA,UAAM;KAAC;IAE/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,YAAY,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,QAAU,MAAM,SAAS;;IACxD,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,QAAQ,GAAG,CAAC,aAAa;IACzB,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,MAAM;YACN;QACF;QAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;YACC,YAAY;gBACV,KAAK,SAAS,MAAM,CAAC,QAAQ;gBAC7B,KAAK,SAAS,MAAM,CAAC,SAAS;YAChC;QACF,GACA,CAAC;YACC,IAAI,IAAI,IAAI,KAAK,IAAI,iBAAiB,EAAE;gBACtC,oFAAoF;gBACpF,QAAQ,GAAG,CAAC,OAAO;YACrB,OAAO;gBACL,2DAA2D;gBAC3D,QAAQ,GAAG,CAAC,OAAO;YACrB;QACF;IAEJ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,QAAQ,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;YACrB,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC;YACjB;QACF;oCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE,IAAM;oCAAqB,EAAE;IAEvC,MAAM,WAAW,OAAO;QAEtB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B;QAEA,6FAA6F;QAC7F,4CAA4C;QAC5C,6FAA6F;QAC7F,aAAa;QACb,IAAI;QAEJ,IAAI,CAAC,UAAU;YACb,kCAAkC;YAClC,8CAA8C;YAC9C,UAAU;YACV,YAAY;gBACV,KAAK;gBACL,KAAK;YACP;QACF;QAEA,IAAI;YACF,MAAM,UAAU;gBACd,GAAG,IAAI;gBACP,OAAO,UAAU,eAAe,CAAC,KAAK;gBACtC,UAAU;oBACR,MAAM;oBACN,aAAa,OAAO,MAAM,CAAC;gBAC7B;YACF;YAEA,OAAO,QAAQ,gBAAgB;YAC/B,OAAO,QAAQ,MAAM;YACrB,IAAI,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;YAClC,QAAQ,GAAG,CAAC,YAAY;YACxB,IAAI,SAAS,MAAM,IAAI,KAAK;gBAC1B,QAAQ,GAAG,CAAC,mBAAmB,UAAU;gBACzC,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,KAAK;gBACxB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,UAAU,KAAK;gBAC7C,SAAS,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,OAAO,CAAC,SAAS,OAAO,IAC1B,SAAS,OAAO,EAAE,IAAI,CAAC,IAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MACzC,sJAAA,CAAA,QAAK,CAAC,KAAK,CACX,SAAS,OAAO,IAAI;YAE1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;QACvB;IACF;IAEA,MAAM,WAAW;QACf,MAAM;QACN,UAAU;QACV,OAAO;QACP,cAAc;QACd,gBAAgB;QAChB,UAAU;QACV,eAAe;IACjB;IAEA,QAAQ,GAAG,CAAC,UAAU;IAEtB,qBACE,6LAAC;QAAQ,WAAW,GAAG,wJAAA,CAAA,UAAS,CAAC,eAAe,EAAE;kBAChD,cAAA,6LAAC;YAAQ,WAAU;;8BACjB,6LAAC;oBAAQ,WAAU;;sCACjB,6LAAC;4BAAQ,WAAU;sCACjB,cAAA,6LAAC;gCAAI,WAAW,GAAG,wJAAA,CAAA,UAAS,CAAC,cAAc,EAAE;0CAG3C,cAAA,6LAAC;oCAAa,gBAAgB;8CAC3B,OAAO,GAAG,CAAC,CAAC,OAAO,oBAClB,6LAAC;4CACC,WAAW,CAAC,yBAAyB,EAAE,wJAAA,CAAA,UAAS,CAAC,SAAS,EAAE;sDAE5D,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDAEJ,KAAK;gDACL,KAAI;gDACJ,WAAU;+CAHL;;;;;;;;;;;;;;;;;;;;;;;;;sCAWjB,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAE,WAAU;8CAA0D;;;;;;8CAIvE,6LAAC;oCACC,UAAU,aAAa;oCACvB,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAS,WAAW,CAAC,CAAC,EAAE,wJAAA,CAAA,UAAS,CAAC,MAAM,EAAE;;8EACzC,6LAAC;oEAAO,WAAU;8EAAmC;;;;;;8EAGrD,6LAAC;oEAAM,SAAQ;oEAAY,WAAU;8EAAU;;;;;;8EAG/C,6LAAC;oEACC,IAAG;oEACH,MAAK;oEACL,cAAa;oEACZ,GAAG,SAAS,aAAa;wEACxB,UAAU;wEACV,WAAW;4EACT,OAAO;4EACP,SAAS;wEACX;oEACF,EAAE;oEACF,WAAU;oEACV,gBAAc,CAAC,CAAC,QAAQ;oEACxB,oBAAiB;;;;;;;;;;;;wDAIpB,QAAQ,2BACP,6LAAC;4DACC,IAAG;4DACH,WAAU;sEAET,QAAQ,UAAU;;;;;;;;;;;;8DAKzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAS,WAAW,CAAC,CAAC,EAAE,wJAAA,CAAA,UAAS,CAAC,MAAM,EAAE;;8EACzC,6LAAC;oEAAO,WAAU;8EAAmC;;;;;;8EAGrD,6LAAC;oEAAM,SAAQ;oEAAW,WAAU;8EAAU;;;;;;8EAG9C,6LAAC;oEACC,IAAG;oEACH,MAAK;oEACL,cAAa;oEACZ,GAAG,SAAS,YAAY;wEACvB,UAAU;wEACV,WAAW;4EACT,OAAO;4EACP,SAAS;wEACX;oEACF,EAAE;oEACF,WAAU;oEACV,gBAAc,CAAC,CAAC,OAAO,QAAQ;;;;;;;;;;;;wDAIlC,OAAO,QAAQ,kBACd,6LAAC;4DACC,IAAG;4DACH,WAAU;sEAET,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;sDAOhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAS,WAAW,CAAC,CAAC,EAAE,wJAAA,CAAA,UAAS,CAAC,MAAM,EAAE;;sEACzC,6LAAC;4DAAO,WAAU;sEAAmC;;;;;;sEAGrD,6LAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAAU;;;;;;sEAG9C,6LAAC;4DACC,IAAG;4DACH,MAAM,eAAe,SAAS;4DAC9B,cAAa;4DAEZ,GAAG,SAAS,YAAY;gEACvB,UAAU;gEACV,WAAW;oEACT,OAAO;oEACP,SAAS;gEACX;4DACF,EAAE;4DACF,WAAU;4DACV,gBAAc,CAAC,CAAC,OAAO,QAAQ;4DAC/B,oBAAiB;;;;;;sEAEnB,6LAAC;4DACC,WAAU;4DACV,SAAS,IAAM,gBAAgB,CAAC;4DAChC,MAAK;4DACL,UAAU;4DACV,cACE,eAAe,kBAAkB;4DAEnC,WAAW,CAAC,IACV,EAAE,GAAG,KAAK,WAAW,gBAAgB,CAAC;sEAGvC,6BACC,6LAAC,iJAAA,CAAA,gBAAa;gEAAC,WAAU;gEAAiB,MAAM;;;;;qFAEhD,6LAAC,kJAAA,CAAA,aAAU;gEAAC,WAAU;gEAAiB,MAAM;;;;;;;;;;;;;;;;;gDAIlD,OAAO,QAAQ,kBACd,6LAAC;oDACC,IAAG;oDACH,WAAU;8DAET,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAS,WAAW,CAAC,CAAC,EAAE,wJAAA,CAAA,UAAS,CAAC,MAAM,EAAE;;sEACzC,6LAAC;4DAAO,WAAU;sEAAmC;;;;;;sEAGrD,6LAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAAU;;;;;;sEAG9C,6LAAC;4DACC,IAAG;4DACH,MAAM,uBAAuB,SAAS;4DACtC,cAAa;4DACZ,GAAG,SAAS,oBAAoB;gEAC/B,UAAU;gEACV,UAAU,CAAC,QACT,UAAU,MAAM,eAAe;4DACnC,EAAE;4DACF,WAAU;4DACV,gBAAc,CAAC,CAAC,OAAO,gBAAgB;4DACvC,oBAAiB;;;;;;sEAEnB,6LAAC;4DACC,WAAU;4DACV,SAAS,IACP,wBAAwB,CAAC;4DAE3B,MAAK;4DACL,UAAU;4DACV,cACE,uBAAuB,kBAAkB;4DAE3C,WAAW,CAAC,IACV,EAAE,GAAG,KAAK,WACV,wBAAwB,CAAC;sEAG1B,qCACC,6LAAC,iJAAA,CAAA,gBAAa;gEAAC,WAAU;gEAAiB,MAAM;;;;;qFAEhD,6LAAC,kJAAA,CAAA,aAAU;gEAAC,WAAU;gEAAiB,MAAM;;;;;;;;;;;;;;;;;gDAIlD,OAAO,gBAAgB,kBACtB,6LAAC;oDACC,IAAG;oDACH,WAAU;8DAET,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;sDAMtC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,cAAa;wDACb,WAAU;wDACT,GAAG,SAAS,UAAU;4DACrB,UAAU;wDACZ,EAAE;;;;;;kEAEJ,6LAAC;wDAAM,WAAU;;4DAA2C;4DACvC;0EACnB,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;4DAAY;4DAAK;0EAClD,6LAAC;gEACC,MAAK;gEACL,QAAO;gEACP,KAAI;gEACJ,WAAU;;oEACX;kFAAG,6LAAC;wEAAK,WAAU;kFAAiC;;;;;;;;;;;;;;;;;;oDAEtD,OAAO,MAAM,kBACZ,6LAAC;wDACC,IAAG;wDACH,WAAU;kEAET,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;sDAe9B,6LAAC,8IAAA,CAAA,UAAY;4CACX,WAAW;4CACX,MAAK;4CACL,WAAW;4CACX,UAAU,kBACR,6LAAC;oDAAK,WAAW,GAAG,wJAAA,CAAA,UAAS,CAAC,YAAY,EAAE;8DAAE;;;;;;;;;;;sDAOlD,6LAAC;4CAAE,WAAU;;gDAA8C;gDAChC;8DACzB,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,6LAAC;wDAAK,WAAU;kEAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStE,6LAAC;oBAAQ,WAAU;8BACjB,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;AAKX;GA3ZwB;;QACP,qIAAA,CAAA,YAAS;QAWpB,iKAAA,CAAA,UAAO;QAQO,4JAAA,CAAA,cAAW;QACZ,4JAAA,CAAA,cAAW;;;MArBN", "debugId": null}}]}