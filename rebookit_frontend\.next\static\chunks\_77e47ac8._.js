(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/components/landingPage/search/search.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "searchContainer": "search-module-scss-module__r3xxDG__searchContainer",
});
}}),
"[project]/public/test.jpeg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/test.701609de.jpeg");}}),
"[project]/public/test.jpeg.mjs { IMAGE => \"[project]/public/test.jpeg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$test$2e$jpeg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/test.jpeg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$test$2e$jpeg__$28$static__in__ecmascript$29$__["default"],
    width: 259,
    height: 194,
    blurDataURL: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAGAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwBttdXD6hFFY3tzbK3Ytu+tdWGnGpBJt3+Vjzq8pQm7JW+dz//Z",
    blurWidth: 8,
    blurHeight: 6
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/landing_page_1.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/landing_page_1.6f28ad5c.png");}}),
"[project]/public/images/landing_page_1.png.mjs { IMAGE => \"[project]/public/images/landing_page_1.png (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_1$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/landing_page_1.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_1$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 900,
    height: 1350,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAICAIAAAC+k6JsAAAAi0lEQVR42gGAAH//AFtYVnVycYeEg3p4d15bWQCJgHZpWFF8ZF52YVmRg3cArKaggWpjv5KCcVdNkIR6AHp4e4NmZa91ZUYxLGRhZACJW2qSHzyMGy1QDhloTFEAZyEygwEgigEieAEcYyQxAEEXIFgCFGEBFVQBEkMfJgA3Hh86Bw83Agk0BAs2KCneJya6vxg9vwAAAABJRU5ErkJggg==",
    blurWidth: 5,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/landing_page_2.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/landing_page_2.00350a86.png");}}),
"[project]/public/images/landing_page_2.png.mjs { IMAGE => \"[project]/public/images/landing_page_2.png (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_2$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/landing_page_2.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_2$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 900,
    height: 1350,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAICAIAAAC+k6JsAAAAi0lEQVR42gGAAH//AAMDAxENChwVEAsIBwICAgAGBgY4KyA+LB4UDwsDAgIACAgIe2NUkWtTGRALBAMCAAgICFxIPoRdSSYYEQgFBAAXGBhfVE5qSjchFg4KBwUAf4WMoqOkkXNcNiUZFBEQAH+KmKCYlqF9ZEAtIRwdHwA9RE5tamxFMycjHx0cICW5uBiXIbAMAAAAAABJRU5ErkJggg==",
    blurWidth: 5,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/landing_page_3.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/landing_page_3.628691c9.png");}}),
"[project]/public/images/landing_page_3.png.mjs { IMAGE => \"[project]/public/images/landing_page_3.png (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_3$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/landing_page_3.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_3$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 900,
    height: 1350,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAICAIAAAC+k6JsAAAAi0lEQVR42gGAAH//AD00K05PSU1MRk5PSjUvJwBRTkducm5iXFVucm5TU04AbHBsYWhmaFlSdHdzdXp3AGJqZjM4OB8iIj5EQnqCgABbYl1XU00xLixdWFBnbGcAUlpYM0VHTkdFS2Rka3BtAFNbW0RPT1BWVEtobGZubQBgXl5+gIBlYWB9dXNzbGr3KympjWT6DwAAAABJRU5ErkJggg==",
    blurWidth: 5,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/landing_page_4.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/landing_page_4.75e4adeb.png");}}),
"[project]/public/images/landing_page_4.png.mjs { IMAGE => \"[project]/public/images/landing_page_4.png (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_4$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/landing_page_4.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_4$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 900,
    height: 1350,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAICAIAAAC+k6JsAAAAi0lEQVR42gGAAH//AAENPwERUwETWgESWgASWAABEVITIWZVS3IeKW8BFWMAARNbMzlzj2pjWEVkAhhuAAAUXTxKkZOFk0NEgQIdgQAAFmdWXJa7o6GKeZYQLJ4AARFMNDlpgmtni3BxKjR9AAIJKiglP1pEQVQ+PCYmSQACCy8VGDsvKEIfHkIJE0hrYx5u/cXVygAAAABJRU5ErkJggg==",
    blurWidth: 5,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/landing_page_5.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/landing_page_5.ecfc3f4f.png");}}),
"[project]/public/images/landing_page_5.png.mjs { IMAGE => \"[project]/public/images/landing_page_5.png (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_5$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/landing_page_5.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_5$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 900,
    height: 1350,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAICAIAAAC+k6JsAAAAi0lEQVR42gGAAH//ABZzlSl4lx9tjSpykDF6mQAPcpQedJUdbIslcI8veZcABm6RGXKRXomfNH6cFHCRAAFqjRVwj2Judyh1lQtvkQACZooSa5JUc4cxe6AFbZMACGaLG2mQYJCtKnKYCGWLAANafhdkiU6DohtnjwNcggACUnUUUW4qVW4cVG8CTGpkGiwHzDyIRQAAAABJRU5ErkJggg==",
    blurWidth: 5,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/images/landing1.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/landing1.e41aa1ad.png");}}),
"[project]/public/images/landing1.png.mjs { IMAGE => \"[project]/public/images/landing1.png (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing1$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/landing1.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing1$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 1440,
    height: 710,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAECAYAAACzzX7wAAAAj0lEQVR42gGEAHv/AIx7ef9ARlD/NFJy/1Zpg/+GenT/eV1I/1o6J/9cPiv/AIRza/9UUFH/c3iD/4yOl/+DgIX/jYB5/11EOf+Tgnr/AH5hRv+HbVP/oJSJ/6iakv+nj4X/moh//2NMSP9SPTj/AIFzX/+ThG7/o5aP/5aBeP+nfXH/l3Rp/4ppav92WFj/iclKQuTCG2sAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 4
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/hooks/useDebounce.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDebounce": (()=>useDebounce)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function useDebounce(value, delay) {
    _s();
    const [debouncedValue, setDebouncedValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(value);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDebounce.useEffect": ()=>{
            const timer = setTimeout({
                "useDebounce.useEffect.timer": ()=>{
                    setDebouncedValue(value);
                }
            }["useDebounce.useEffect.timer"], delay);
            return ({
                "useDebounce.useEffect": ()=>clearTimeout(timer)
            })["useDebounce.useEffect"]; // cleanup on value or delay change
        }
    }["useDebounce.useEffect"], [
        value,
        delay
    ]);
    return debouncedValue;
}
_s(useDebounce, "KDuPAtDOgxm8PU6legVJOb3oOmA=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/utils/restCall.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const RestCall = async (data)=>{
    try {
        let response = await axios(data);
        return response;
    } catch (err) {
        return {
            message: err.message,
            code: err.statusCode
        };
    }
};
_c = RestCall;
const __TURBOPACK__default__export__ = RestCall;
var _c;
__turbopack_context__.k.register(_c, "RestCall");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://devapi.rebookit.club");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {
        // Clear local storage and redirect to /login
        localStorage.clear();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
        window.location.href = "/login";
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        // console.log("error log is", error)
        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.message)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/bookDetails.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { default: RestCall } = __turbopack_context__.r("[project]/app/utils/restCall.js [app-client] (ecmascript)");
const { ELASTIC_DB_ROUTES } = __turbopack_context__.r("[project]/app/config/api.js [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const instance = __turbopack_context__.r("[project]/app/services/axios.js [app-client] (ecmascript)");
const { axiosErrorHandler } = __turbopack_context__.r("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
const bookDetails = async (data)=>{
    try {
        let userToken = getToken();
        let response = await RestCall({
            method: "get",
            url: `${USER_ROUTES.LIST_ITEM}/${id}`,
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${userToken}`
            },
            data: data
        });
        return response;
    } catch (err) {
        return {
            message: err.message,
            code: err.statusCode
        };
    }
};
const bookSearch = async (url, data)=>{
    try {
        let userToken = getToken();
        let response = await RestCall({
            method: "POST",
            url: url,
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${userToken}`
            },
            data: data
        });
        return response;
    } catch (err) {
        return {
            message: err.message,
            code: err.statusCode
        };
    }
};
// const bookSuggestion =async(url)=>{
//      try{
//      let userToken = getToken()
//         let response = await RestCall({
//             method:"get",
//             url: url,
//             headers: {
//                 "Content-Type": "application/json",
//                 "Authorization": `Bearer ${userToken}`
//             }
//         })
//         return response
//     }catch(err){
//         return { message: err.message, code: err.statusCode }
//     }
// }
// const markAsSold=async(url,data)=>{
//      try{
//      let userToken = getToken()
//         let response = await RestCall({
//             method:"put",
//             url: url,
//             headers: {
//                 "Content-Type": "application/json",
//                 "Authorization": `Bearer ${userToken}`
//             },
//             data
//         })
//         return response
//     }catch(err){
//         return { message: err.message, code: err.statusCode }
//     }
// }
const bookSuggestion = async (url)=>{
    let response = await instance.get(url).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const markAsSold = async (url, data)=>{
    let response = await instance.put(url, data).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
module.exports = {
    bookDetails,
    bookSearch,
    bookSuggestion,
    markAsSold
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/landingPage/search/search.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BookSearchComponent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$landingPage$2f$search$2f$search$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/components/landingPage/search/search.module.scss.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/lu/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io5/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$test$2e$jpeg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$test$2e$jpeg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/test.jpeg.mjs { IMAGE => "[project]/public/test.jpeg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_1$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_1$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/landing_page_1.png.mjs { IMAGE => "[project]/public/images/landing_page_1.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_2$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_2$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/landing_page_2.png.mjs { IMAGE => "[project]/public/images/landing_page_2.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_3$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_3$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/landing_page_3.png.mjs { IMAGE => "[project]/public/images/landing_page_3.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_4$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_4$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/landing_page_4.png.mjs { IMAGE => "[project]/public/images/landing_page_4.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_5$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_5$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/landing_page_5.png.mjs { IMAGE => "[project]/public/images/landing_page_5.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing1$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing1$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/landing1.png.mjs { IMAGE => "[project]/public/images/landing1.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/hooks/useDebounce.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$bookDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/bookDetails.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function BookSearchComponent() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const suggestionsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [visible, setVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    let avatarImages = [
        // landing1,
        __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_1$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_1$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_2$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_2$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_3$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_3$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_4$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_4$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$landing_page_5$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$landing_page_5$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"]
    ];
    const [searchInput, setSearchInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [searching, setSearching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasFetched, setHasFetched] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchSuggestions, setSearchSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    console.log(searchSuggestions, "searchSuggestions");
    console.log("searchInput", searchInput);
    const fetchSearchData = async ()=>{
        try {
            let urlString = `${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELASTIC_DB_ROUTES"].SUGGESTION}?searchTerm=${searchInput.trim()}&page=1`;
            let searchData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$bookDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bookSuggestion"])(urlString);
            console.log("searchData", searchData);
            if (searchData.status == 200) {
                setSearchSuggestions(searchData.data.data);
            }
        } catch (error) {
            console.log("error", error);
        } finally{
            setSearching(false);
            setHasFetched(true);
        }
    };
    // Debouncing in the search text [Using Value]
    const debouncedSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDebounce"])(searchInput, 500);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookSearchComponent.useEffect": ()=>{
            // You can choose how to handle empty input here
            console.log("debouncedSearch", debouncedSearch);
            if (!debouncedSearch) {
                // Handle empty search case, like clearing results
                setSearchSuggestions([]);
                setHasFetched(false);
            } else {
                fetchSearchData(debouncedSearch);
            }
        }
    }["BookSearchComponent.useEffect"], [
        debouncedSearch
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookSearchComponent.useEffect": ()=>{
            const handleClickOutside = {
                "BookSearchComponent.useEffect.handleClickOutside": (event)=>{
                    if (suggestionsRef.current && !suggestionsRef.current.contains(event.target)) {
                        // setVisible(false);
                        setSearchSuggestions([]);
                    }
                }
            }["BookSearchComponent.useEffect.handleClickOutside"];
            document.addEventListener("mousedown", handleClickOutside);
            return ({
                "BookSearchComponent.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleClickOutside);
                }
            })["BookSearchComponent.useEffect"];
        }
    }["BookSearchComponent.useEffect"], []);
    return(// <section
    //   className={`${searchComponentCss.searchContainer} relative lg:!px-[100px]`}
    // >
    //   <div className="container-wrapper">
    //     <div className="mt-[27px] mb-7 md:mt-[11vh]">
    //       <div className="flex gap-1 md:items-center justify-start">
    //         <div className="flex relative">
    //           {avatarImages.map((image, idx) => {
    //             const overlapOffset = idx * -4; // 4px to the left
    //             const zIndex = 0 + idx;
    //             return (
    //               <div
    //                 key={idx}
    //                 className="relative h-[14px] w-[14px] md:h-[27px] md:w-[27px] rounded-full ring-[0.7px] ring-black overflow-hidden"
    //                 style={{
    //                   // position: 'relative',
    //                   left: `${overlapOffset}px`,
    //                   zIndex: zIndex,
    //                 }}
    //               >
    //                 <Image
    //                   key={idx}
    //                   src={image}
    //                   alt=""
    //                   className="inline-block rounded-full"
    //                   fill
    //                   objectFit="cover"
    //                   sizes="33w"
    //                 />
    //               </div>
    //             );
    //           })}
    //         </div>
    //         {/* <p className='text-[10px] text-white leading-normal md:text-md md:ml-[42px] lg:ml-0 absolute left-[18%] w-[55%] md:relative md:left-0'> Trusted by thousands of book lovers and sellers worldwide</p> */}
    //         <p className="text-[10px] text-white leading-normal md:text-sm lg:ml-0 relative -left-[10px]">
    //           {" "}
    //           Jamaica's First Digital Education Ecosystem
    //         </p>
    //       </div>
    //       <h1
    //         className={`${searchComponentCss.mainHeading} mt-5  md:mt-[23px]`}
    //       >
    //         “Don’t go broke buying it new,
    //         <br />
    //         or throw it out when you are through.
    //         <br />
    //         <span className="inline-block">ReBookIt and $ave!”</span>
    //       </h1>
    //     </div>
    //     <form
    //       onSubmit={(e) => {
    //         e.preventDefault();
    //         if (searchInput?.length > 0) {
    //           router.push(`/search?book=${searchInput}`);
    //         }
    //       }}
    //       className={`w-[98%] rounded-3xl m-auto md:absolute md:left-0 md:scale-[0.8] lg:scale-100 lg:relative lg:w-9/12 md:m-0 md:max-w-[900px] md:flex md:items-center relative lg:ml-[16px] ring-[8px] md:ring-[16px] ring-[#ffffff26] z-2 ${
    //         searchSuggestions?.length > 0 || hasFetched
    //           ? "rounded-[15px]"
    //           : "md:rounded-full"
    //       }`}
    //       role="search"
    //     >
    //       <div
    //         className={`${searchComponentCss.searchBox} ${
    //           searchSuggestions?.length > 0 || hasFetched
    //             ? "!rounded-t-[15px] !rounded-b-none !border-none"
    //             : ""
    //         }`}
    //       >
    //         <div className="rounded-full bg-[#025FA8] p-1 md:p-2">
    //           <LuBookOpen
    //             className="w-[10px] h-[10px] md:w-[23px] md:h-[23px]"
    //             color="#fff"
    //           />
    //         </div>
    //         <input
    //           type="text"
    //           className="w-full px-2 outline-none border-none font-poppins font-[275] italic md:text-[16.77px] text-[12px] leading-[100%] tracking-[0%]  text-[#757575]"
    //           placeholder="Search by Books, ED-irectory, Events, Scholarship & Awards."
    //           aria-label="Search books"
    //           value={searchInput}
    //           onChange={(e) => setSearchInput(e.target.value)}
    //           // onFocus={() => setSearching(true)}
    //           // onBlur={() => setSearching(false)}
    //         />
    //         <button className="flex justify-center items-center rounded-full">
    //           <IoSearchSharp
    //             className="w-[10px] h-[10px] md:w-[20px] md:h-[20px]"
    //             color="#fff"
    //           />
    //           <p className={`${searchComponentCss.buttonText}`}>Search</p>
    //         </button>
    //       </div>
    //       {/* {
    //         // searchInput.length ?
    //         // Search List
    //         <div
    //           id="suggestions"
    //           ref={suggestionsRef}
    //           className={`flex flex-col justify-start absolute left-0 top-full bg-white w-full h-fit rounded-b-[15px] overflow-y-auto overflow-x-hidden max-h-[180px] lg:max-h-[300px] no_scrollbar shadow-xl`}
    //         >
    //           {searchSuggestions?.map((words) => {
    //             return (
    //               <Link
    //                 href={`book-detail?id=${words._id}`}
    //                 key={words?.title}
    //                 className="py-[16px] px-5 flex justify-between gap-10 group border-b border-b-gray-300 hover:bg-[#211F54]"
    //               >
    //                 <p className=" text-sm md:text-base flex items-center  lg:text-lg capitalize text-[#666] leading-normal lg:leading-[29px] line-clamp-1 group-hover:text-white">
    //                   {words?.title}
    //                 </p>
    //                 <div className="relative overflow-hidden w-[50px] h-[60px]">
    //                   <img
    //                     src={
    //                       words.images?.length
    //                         ? words.images[0]
    //                         : "/images/book_1.jpg"
    //                     }
    //                     alt={`search-book-${words?.title}`}
    //                     fill
    //                     //   objectFit="cover"
    //                     className="w-full h-full"
    //                   />
    //                 </div>
    //               </Link>
    //             );
    //           })}
    //         </div>
    //         // ) : // :
    //         // // <Link href={`search?book=${searchInput}`} className={`${searchComponentCss.searchSuggestion}`} >
    //         // //     <div className='flex justify-start items-center text-left w-full border-b-1 border-gray-200/10 hover:bg-gray-200 rounded-lg'>
    //         // //         <MdSearch color='#000' size={20} className='mx-2' />
    //         // //         {searchInput}
    //         // //     </div>
    //         // // </Link>
    //         // null
    //       } */}
    //       {searchInput && (
    //         <div
    //           id="suggestions"
    //           ref={suggestionsRef}
    //           className="absolute left-0 top-full bg-white w-full rounded-b-[15px] overflow-y-auto max-h-[300px] shadow-xl"
    //         >
    //           {searching ? (
    //             <div className="py-4 px-5 text-center text-gray-500 italic">
    //               Loading…
    //             </div>
    //           ) : hasFetched && searchSuggestions.length === 0 ? (
    //             <p className="py-4 text-center  px-5  text-gray-500 italic">
    //               No results found
    //             </p>
    //           ) : (
    //             searchSuggestions.map((book) => (
    //               <Link
    //                 href={`book-detail?id=${book._id}`}
    //                 key={book._id}
    //                 className="py-3 px-5 flex justify-between border-b border-gray-200 hover:bg-[#211F54]"
    //               >
    //                 <p className="truncate text-[#666] flex items-center group-hover:text-white">
    //                   {book.title}
    //                 </p>
    //                 <div className="w-[50px] h-[60px] overflow-hidden">
    //                   <img
    //                     src={book.images?.[0] || "/images/book_1.jpg"}
    //                     alt={book.title}
    //                     className="w-full h-full object-cover"
    //                   />
    //                 </div>
    //               </Link>
    //             ))
    //           )}
    //         </div>
    //       )}
    //     </form>
    //   </div>
    // </section>
    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$landingPage$2f$search$2f$search$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].searchContainer} relative p-[10px] md:p-[40px_50px] min-h-[360px] md:min-h-[590px] lg:!px-[100px]`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container-wrapper",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-[27px] mb-7 md:mt-[11vh]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-1 md:items-center justify-start",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex relative",
                                    children: avatarImages.map((image, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative h-[14px] w-[14px] md:h-[27px] md:w-[27px] rounded-full ring-[0.7px] ring-black overflow-hidden",
                                            style: {
                                                left: `${idx * -4}px`,
                                                zIndex: idx
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                src: image,
                                                alt: "",
                                                className: "inline-block rounded-full",
                                                fill: true,
                                                objectFit: "cover",
                                                sizes: "33w"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/search/search.js",
                                                lineNumber: 303,
                                                columnNumber: 19
                                            }, this)
                                        }, idx, false, {
                                            fileName: "[project]/app/components/landingPage/search/search.js",
                                            lineNumber: 298,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 296,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-[10px] text-white leading-normal md:text-sm relative -left-[10px]",
                                    children: "Jamaica's First Digital Education Ecosystem"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 314,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/landingPage/search/search.js",
                            lineNumber: 294,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "   mt-5   text-[22px] sm:text-3xl md:mt-6 md:text-[40px] md:leading-[55px] md:tracking-tight   lg:text-[58px] lg:leading-[72px] lg:tracking-[-2.88px]   xl:leading-[75px]   font-[playfair] font-bold text-[#ffc72c] lg:text-[#ffb800]      ",
                            children: [
                                "\"Don't go broke buying it new,",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 331,
                                    columnNumber: 13
                                }, this),
                                "or throw it out when you are through.",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 333,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "   text-white font-playfair   text-lg sm:text-xl md:text-[36px] md:leading-[50px] md:tracking-tight   lg:text-[55px] lg:leading-[65px]   xl:text-[65px] xl:leading-[72px]   ",
                                    children: "ReBookIt and $ave!"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 334,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/landingPage/search/search.js",
                            lineNumber: 320,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/landingPage/search/search.js",
                    lineNumber: 293,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: (e)=>{
                        e.preventDefault();
                        if (searchInput?.length > 0) {
                            router.push(`/search?book=${searchInput}`);
                        }
                    },
                    className: `w-[98%] rounded-3xl m-auto md:absolute md:left-0 md:scale-[0.8] lg:scale-100 lg:relative lg:w-9/12 md:m-0 md:max-w-[900px] md:flex md:items-center relative lg:ml-[16px] ring-[8px] md:ring-[16px] ring-[#ffffff26] z-2 ${searchSuggestions?.length > 0 || hasFetched ? "rounded-[15px]" : "md:rounded-full"}`,
                    role: "search",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `flex justify-between items-center w-full bg-white rounded-[47.159px] p-2
                   md:rounded-[103px] md:p-[17px_21px_17px_29px] 
                   ${searchSuggestions?.length > 0 || hasFetched ? "!rounded-t-[15px] !rounded-b-none border-none" : "border border-[#909090] shadow-[0px_1.43238px_5.72951px_-4.29713px_rgba(0,0,0,0.4)]"}`,
                            style: {
                                borderWidth: "0.477459px"
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "rounded-full bg-[#025FA8] p-1 md:p-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LuBookOpen"], {
                                        className: "w-[10px] h-[10px] md:w-[23px] md:h-[23px] text-white"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/landingPage/search/search.js",
                                        lineNumber: 373,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 372,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    className: "w-full px-2 outline-none border-none font-poppins font-[275] italic text-[12px] md:text-[16.77px] leading-[100%] text-[#757575] ",
                                    placeholder: "Search by Books, E-Directory, Events, Scholarship & Awards.",
                                    "aria-label": "Search books",
                                    value: searchInput,
                                    onChange: (e)=>setSearchInput(e.target.value)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 376,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "flex justify-center items-center rounded-full bg-gradient-to-r from-[#211F54] to-[#0161ab]    md:w-[159.355px] md:h-[50.323px] md:rounded-[27.5px] p-2 md:p-3",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoSearchSharp"], {
                                            className: "w-[10px] h-[10px] md:w-[20px] md:h-[20px] text-white"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/landingPage/search/search.js",
                                            lineNumber: 389,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-white text-[10px] md:text-[16px]",
                                            children: "Search"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/landingPage/search/search.js",
                                            lineNumber: 390,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 385,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/landingPage/search/search.js",
                            lineNumber: 362,
                            columnNumber: 11
                        }, this),
                        searchInput && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            id: "suggestions",
                            ref: suggestionsRef,
                            className: "absolute left-0 top-full bg-white w-full rounded-b-[15px] overflow-y-auto max-h-[300px] shadow-xl",
                            children: searching ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "py-4 px-5 text-center text-gray-500 italic",
                                children: "Loading…"
                            }, void 0, false, {
                                fileName: "[project]/app/components/landingPage/search/search.js",
                                lineNumber: 404,
                                columnNumber: 17
                            }, this) : hasFetched && searchSuggestions.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "py-4 text-center px-5 text-gray-500 italic",
                                children: "No results found"
                            }, void 0, false, {
                                fileName: "[project]/app/components/landingPage/search/search.js",
                                lineNumber: 408,
                                columnNumber: 17
                            }, this) : searchSuggestions.map((book)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: `book-detail?id=${book._id}`,
                                    className: "py-3 px-5 flex justify-between border-b border-gray-200 hover:bg-[#211F54]",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "truncate text-[#666] hover:text-white",
                                            children: book.title
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/landingPage/search/search.js",
                                            lineNumber: 418,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-[50px] h-[60px] overflow-hidden",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                src: book.images?.[0] || "/images/book_1.jpg",
                                                alt: book.title,
                                                className: "w-full h-full object-cover"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/search/search.js",
                                                lineNumber: 422,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/landingPage/search/search.js",
                                            lineNumber: 421,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, book._id, true, {
                                    fileName: "[project]/app/components/landingPage/search/search.js",
                                    lineNumber: 413,
                                    columnNumber: 19
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/app/components/landingPage/search/search.js",
                            lineNumber: 398,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/landingPage/search/search.js",
                    lineNumber: 348,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/landingPage/search/search.js",
            lineNumber: 292,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/landingPage/search/search.js",
        lineNumber: 282,
        columnNumber: 5
    }, this));
}
_s(BookSearchComponent, "BDlHqXOX99xd60mwPNr2hZOxAn4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDebounce"]
    ];
});
_c = BookSearchComponent;
var _c;
__turbopack_context__.k.register(_c, "BookSearchComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/landingPage/search/search.js [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/components/landingPage/search/search.js [app-client] (ecmascript)"));
}}),
}]);

//# sourceMappingURL=_77e47ac8._.js.map