{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/components/common/SubmitButton.js"], "sourcesContent": ["import React from 'react'\r\n\r\nexport default function SubmitButton({isLoading,InnerDiv,type,btnAction}) {\r\n  return (\r\n     <button type={type} onClick={btnAction} className='mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5' >\r\n            {isLoading ? <>\r\n                <svg\r\n                    className=\"w-4 h-4 animate-spin text-white\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                >\r\n                    <circle\r\n                        className=\"opacity-25\"\r\n                        cx=\"12\"\r\n                        cy=\"12\"\r\n                        r=\"10\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"4\"\r\n                    ></circle>\r\n                    <path\r\n                        className=\"opacity-75\"\r\n                        fill=\"currentColor\"\r\n                        d=\"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\r\n                    ></path>\r\n                </svg>\r\n                Loading...\r\n            </> : <InnerDiv />}\r\n\r\n        </button>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,aAAa,EAAC,SAAS,EAAC,QAAQ,EAAC,IAAI,EAAC,SAAS,EAAC;IACtE,qBACG,8OAAC;QAAO,MAAM;QAAM,SAAS;QAAW,WAAU;kBAC1C,0BAAY;;8BACT,8OAAC;oBACG,WAAU;oBACV,MAAK;oBACL,SAAQ;;sCAER,8OAAC;4BACG,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEhB,8OAAC;4BACG,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAEJ;;yCAEJ,8OAAC;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/utils/axiosError.handler.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\r\n// import history from \"./history\";\r\nimport { removeToken } from \"../utils/utils\";\r\n\r\n\r\nexport const axiosErrorHandler = (error, action, checkUnauthorized = true) => {\r\n\r\n    console.log(\"error\",error)\r\n    const requestStatus = error?.request?.status;\r\n    const responseStatus = error?.response?.status;\r\n    const dataStatus = error?.data?.statusCode;\r\n\r\n    // Only log out on true 401 Unauthorized from response\r\n    if (responseStatus === 401) {\r\n        removeToken();\r\n        if (typeof window !== 'undefined' && window.location) {\r\n            window.location.href = \"/login\";\r\n        }\r\n        return;\r\n    }\r\n    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {\r\n        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map(er => toast.error(er.messages)) || error?.data?.error?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n            );\r\n    }\r\n    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {\r\n        console.log(\"error log is\", error)\r\n        if (Array.isArray(error?.response?.data?.message) || Array?.isArray(error?.data?.message)) error?.response?.data?.message?.map(er => toast.error(er)) || error?.data?.message?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.message || error?.response?.data?.data || error?.data?.message,\r\n            );\r\n    }\r\n    if (\r\n        checkUnauthorized &&\r\n        (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)\r\n    ) {\r\n        if (localStorage.getItem(\"token\")) {\r\n            toast.error(error?.response?.data?.message);\r\n        }\r\n    }\r\n\r\n    if (action === \"uploadImage\") {\r\n        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {\r\n            if (localStorage.getItem(\"token\")) {\r\n                const message = error?.response?.data?.message;\r\n                message && toast.error(message);\r\n            } else history.push(\"/\");\r\n        }\r\n    }\r\n\r\n    if (error?.response) return error.response;\r\n    else if (error?.request) return error.request;\r\n    else return error?.message;\r\n};"], "names": [], "mappings": ";;;AAAA;AACA,mCAAmC;AACnC;;;AAGO,MAAM,oBAAoB,CAAC,OAAO,QAAQ,oBAAoB,IAAI;IAErE,QAAQ,GAAG,CAAC,SAAQ;IACpB,MAAM,gBAAgB,OAAO,SAAS;IACtC,MAAM,iBAAiB,OAAO,UAAU;IACxC,MAAM,aAAa,OAAO,MAAM;IAEhC,sDAAsD;IACtD,IAAI,mBAAmB,KAAK;QACxB,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;QACV,uCAAsD;;QAEtD;QACA;IACJ;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QACvE,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,UAAU,MAAM,OAAO,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,MAAM,OAAO,MAAM,OAAO,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAElM,mJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;IAEzF;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,kBAAkB,KAAK;QAChG,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,YAAY,OAAO,QAAQ,OAAO,MAAM,UAAU,OAAO,UAAU,MAAM,SAAS,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,OAAO,MAAM,SAAS,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEjM,mJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,WAAW,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM;IAE1F;IACA,IACI,qBACA,CAAC,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,GAAG,GACxE;QACE,IAAI,aAAa,OAAO,CAAC,UAAU;YAC/B,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM;QACvC;IACJ;IAEA,IAAI,WAAW,eAAe;QAC1B,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;YACvE,IAAI,aAAa,OAAO,CAAC,UAAU;gBAC/B,MAAM,UAAU,OAAO,UAAU,MAAM;gBACvC,WAAW,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAC3B,OAAO,QAAQ,IAAI,CAAC;QACxB;IACJ;IAEA,IAAI,OAAO,UAAU,OAAO,MAAM,QAAQ;SACrC,IAAI,OAAO,SAAS,OAAO,MAAM,OAAO;SACxC,OAAO,OAAO;AACvB", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/service/axios.js"], "sourcesContent": ["const { default: axios } = require(\"axios\");\r\nconst { getToken } = require(\"../utils/utils\");\r\n\r\nconst BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst instance = axios.create({\r\n  baseURL: BASE_URL+\"/api\" ,\r\n\r\n  // Lets keep a check as default is 0 millisecond i.e. never\r\n  // Note: timeout is only for server response not network i.e. server reachability\r\n  timeout: 100000,\r\n\r\n  // Lets keep a check as default bytes- 2k\r\n  maxContentLength: 1000,\r\n\r\n  // Lets keep a check as default 5 seems high\r\n  maxRedirects: 2,\r\n});\r\n\r\ninstance.interceptors.request.use(\r\n  (config) => {\r\n    // const token = localStorage.getItem(\"auth\");\r\n    const token = getToken();\r\n    console.log(\"token\", token)\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // Rate limiting: only fire a request every 2 sec from lodash.debounce\r\n    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });\r\n    return Promise.resolve(config);\r\n  },\r\n  function (error) {\r\n    const response = handleLogError(error); // log them\r\n\r\n    return Promise.reject(error);\r\n  }\r\n  // multiple options as to when and how to apply these interceptors\r\n  // , { synchronous: true, runWhen: onGetCall }\r\n);\r\n\r\n\r\nmodule.exports = instance;"], "names": [], "mappings": "AAAA,MAAM,EAAE,SAAS,KAAK,EAAE;AACxB,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM;AAEN,MAAM,WAAW,MAAM,MAAM,CAAC;IAC5B,SAAS,WAAS;IAElB,2DAA2D;IAC3D,iFAAiF;IACjF,SAAS;IAET,yCAAyC;IACzC,kBAAkB;IAElB,4CAA4C;IAC5C,cAAc;AAChB;AAEA,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;IACC,8CAA8C;IAC9C,MAAM,QAAQ;IACd,QAAQ,GAAG,CAAC,SAAS;IACrB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,sEAAsE;IACtE,8EAA8E;IAC9E,OAAO,QAAQ,OAAO,CAAC;AACzB,GACA,SAAU,KAAK;IACb,MAAM,WAAW,eAAe,QAAQ,WAAW;IAEnD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAMF,OAAO,OAAO,GAAG", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/service/booklisting.js"], "sourcesContent": ["const {axiosErrorHandler} = require(\"../utils/axiosError.handler\");\r\nconst instance = require(\"./axios\");\r\n\r\nlet uri = {\r\n  getItemList: \"/item/search/admin\",\r\n  deleteItem: \"admin/item\",\r\n  getItemById: \"/item\",\r\n};\r\n\r\nexport const getList = async (query, data) => {\r\n  let response = await instance\r\n    .post(uri.getItemList + query, data)\r\n    .catch(axiosErrorHandler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\n\r\nexport const getBooksById = async (id, userId) => {\r\n  let response = await instance\r\n    .get(`${uri.getItemById}/${id}`, {\r\n      params: {userId},\r\n    })\r\n    .catch(axiosErrorHandler);\r\n  console.log(\"login test response\", response);\r\n  return response;\r\n};\r\n\r\nexport const deleteItem = async (id) => {\r\n  let response = await instance\r\n    .delete(uri.deleteItem + id)\r\n    .catch(axiosError<PERSON>andler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,EAAC,iBAAiB,EAAC;AACzB,MAAM;AAEN,IAAI,MAAM;IACR,aAAa;IACb,YAAY;IACZ,aAAa;AACf;AAEO,MAAM,UAAU,OAAO,OAAO;IACnC,IAAI,WAAW,MAAM,SAClB,IAAI,CAAC,IAAI,WAAW,GAAG,OAAO,MAC9B,KAAK,CAAC;IACT,+CAA+C;IAC/C,OAAO;AACT;AAEO,MAAM,eAAe,OAAO,IAAI;IACrC,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,GAAG,IAAI,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE;QAC/B,QAAQ;YAAC;QAAM;IACjB,GACC,KAAK,CAAC;IACT,QAAQ,GAAG,CAAC,uBAAuB;IACnC,OAAO;AACT;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI,WAAW,MAAM,SAClB,MAAM,CAAC,IAAI,UAAU,GAAG,IACxB,KAAK,CAAC;IACT,+CAA+C;IAC/C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/components/common/PopUp.js"], "sourcesContent": ["import React from \"react\";\r\n\r\nexport default function Popup({\r\n  isOpen,\r\n  title = \"Are you sure?\",\r\n  message = \"\",\r\n  confirmText = \"Confirm\",\r\n  cancelText = \"Cancel\",\r\n  onConfirm,\r\n  onCancel,\r\n  actionLoading=false\r\n  \r\n}) {\r\n\r\n    console.log(\"actionLoading in popup \",actionLoading)\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div\r\n      className=\"fixed inset-0 z-50 flex items-center justify-center\"\r\n      style={{backgroundColor: \"rgba(0,0,0,0.7)\"}}\r\n    >\r\n      <div className=\"bg-white rounded-2xl shadow-lg max-w-sm w-full p-6\">\r\n        <h3 className=\"text-xl font-semibold mb-2 \">{title}</h3>\r\n        {message && <p className=\"text-gray-600 mb-4\">{message}</p>}\r\n        <div className=\"flex justify-end space-x-3\">\r\n          <button\r\n            onClick={onCancel}\r\n            className=\"px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-100 transition\"\r\n          >\r\n            {cancelText}\r\n          </button>\r\n          <button\r\n            onClick={onConfirm}\r\n            className=\"px-4 py-2 rounded-lg global_linear_gradient text-white \"\r\n          >{\r\n            actionLoading?\"...Loading\":confirmText\r\n          }\r\n            \r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,MAAM,EAC5B,MAAM,EACN,QAAQ,eAAe,EACvB,UAAU,EAAE,EACZ,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,SAAS,EACT,QAAQ,EACR,gBAAc,KAAK,EAEpB;IAEG,QAAQ,GAAG,CAAC,2BAA0B;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAC,iBAAiB;QAAiB;kBAE1C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA+B;;;;;;gBAC5C,yBAAW,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BAC/C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAET;;;;;;sCAEH,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,gBAAc,eAAa;;;;;;;;;;;;;;;;;;;;;;;AAQvC", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/public/icons/magnifier_icon.svg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 24, height: 24, blurDataURL: null, blurWidth: 0, blurHeight: 0 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAM,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/components/common/Pagination.js"], "sourcesContent": ["import React from \"react\";\r\n\r\nexport default function Pagination({\r\n  setPageSize,\r\n  setCurrentPage,\r\n  getListing,\r\n  currentPage,\r\n  totalPages,\r\n  totalItems,\r\n  pageSize,\r\n}) {\r\n  console.log(\"totalPages inpagination\", totalPages);\r\n  const handlePageChange = (number) => {\r\n    setCurrentPage(number);\r\n  };\r\n  if(totalPages>1)\r\n  return (\r\n    <div className=\"flex flex-col md:flex-row items-center justify-end mt-4 gap-4\">\r\n      {/* <div className=\"flex items-center\">\r\n        <div>\r\n          <span className=\"mr-2 text-sm\">Items per page:</span>\r\n          <select\r\n            value={pageSize}\r\n            onChange={(e) => {\r\n              const newSize = parseInt(e.target.value);\r\n              setPageSize(newSize);\r\n              setCurrentPage(1);\r\n              getListing(undefined, 1);\r\n            }}\r\n            className=\"border rounded px-2 py-1 text-sm\"\r\n          >\r\n            <option value={5}>5</option>\r\n            <option value={10}>10</option>\r\n            <option value={20}>20</option>\r\n            <option value={50}>50</option>\r\n          </select>\r\n        </div>\r\n        <div className=\"text-sm ml-2\">\r\n          Showing {Math.min((currentPage - 1) * pageSize + 1, totalItems)}-\r\n          {Math.min(currentPage * pageSize, totalItems)} of {totalItems} items\r\n        </div>\r\n      </div> */}\r\n\r\n      <div className=\"flex items-center gap-2\">\r\n        <button\r\n          onClick={() => handlePageChange(currentPage - 1)}\r\n          disabled={currentPage === 1}\r\n          className={`px-3 py-1 rounded border text-sm ${currentPage === 1\r\n              ? \"bg-gray-100 cursor-not-allowed\"\r\n              : \"hover:bg-gray-100\"\r\n            }`}\r\n        >\r\n          Previous\r\n        </button>\r\n\r\n        {Array.from({ length: totalPages }, (_, i) => {\r\n          let pageNum;\r\n          if (totalPages <= 5) {\r\n            pageNum = i + 1;\r\n          } else if (currentPage <= 3) {\r\n            pageNum = i + 1;\r\n          } else if (currentPage >= totalPages - 2) {\r\n            pageNum = totalPages - 4 + i;\r\n          } else {\r\n            pageNum = currentPage - 2 + i;\r\n          }\r\n\r\n          return (\r\n            <button\r\n              key={pageNum}\r\n              onClick={() => handlePageChange(pageNum)}\r\n              className={`px-3 py-1 rounded border text-sm ${currentPage === pageNum\r\n                  ? \"bg-[linear-gradient(268deg,_#211f54_11.09%,_#0161ab_98.55%)] text-white\"\r\n                  : \"hover:bg-gray-100\"\r\n                }`}\r\n            >\r\n              {pageNum}\r\n            </button>\r\n          );\r\n        })}\r\n\r\n        {totalPages > 5 && currentPage < totalPages - 2 && (\r\n          <span className=\"px-2\">...</span>\r\n        )}\r\n\r\n        {totalPages > 5 && currentPage < totalPages - 1 && (\r\n          <button\r\n            onClick={() => handlePageChange(totalPages)}\r\n            className={`px-3 py-1 rounded border text-sm ${currentPage === totalPages\r\n                ? \"bg-blue-500 text-white\"\r\n                : \"hover:bg-gray-100\"\r\n              }`}\r\n          >\r\n            {totalPages}\r\n          </button>\r\n        )}\r\n\r\n        <button\r\n          onClick={() => handlePageChange(currentPage + 1)}\r\n          disabled={currentPage === totalPages}\r\n          className={`px-3 py-1 rounded border text-sm ${currentPage === totalPages\r\n              ? \"bg-gray-100 cursor-not-allowed\"\r\n              : \"hover:bg-gray-100\"\r\n            }`}\r\n        >\r\n          Next\r\n        </button>\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n  else return \"\"\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,WAAW,EACjC,WAAW,EACX,cAAc,EACd,UAAU,EACV,WAAW,EACX,UAAU,EACV,UAAU,EACV,QAAQ,EACT;IACC,QAAQ,GAAG,CAAC,2BAA2B;IACvC,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IACA,IAAG,aAAW,GACd,qBACE,8OAAC;QAAI,WAAU;kBA0Bb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,SAAS,IAAM,iBAAiB,cAAc;oBAC9C,UAAU,gBAAgB;oBAC1B,WAAW,CAAC,iCAAiC,EAAE,gBAAgB,IACzD,mCACA,qBACF;8BACL;;;;;;gBAIA,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAW,GAAG,CAAC,GAAG;oBACtC,IAAI;oBACJ,IAAI,cAAc,GAAG;wBACnB,UAAU,IAAI;oBAChB,OAAO,IAAI,eAAe,GAAG;wBAC3B,UAAU,IAAI;oBAChB,OAAO,IAAI,eAAe,aAAa,GAAG;wBACxC,UAAU,aAAa,IAAI;oBAC7B,OAAO;wBACL,UAAU,cAAc,IAAI;oBAC9B;oBAEA,qBACE,8OAAC;wBAEC,SAAS,IAAM,iBAAiB;wBAChC,WAAW,CAAC,iCAAiC,EAAE,gBAAgB,UACzD,4EACA,qBACF;kCAEH;uBAPI;;;;;gBAUX;gBAEC,aAAa,KAAK,cAAc,aAAa,mBAC5C,8OAAC;oBAAK,WAAU;8BAAO;;;;;;gBAGxB,aAAa,KAAK,cAAc,aAAa,mBAC5C,8OAAC;oBACC,SAAS,IAAM,iBAAiB;oBAChC,WAAW,CAAC,iCAAiC,EAAE,gBAAgB,aACzD,2BACA,qBACF;8BAEH;;;;;;8BAIL,8OAAC;oBACC,SAAS,IAAM,iBAAiB,cAAc;oBAC9C,UAAU,gBAAgB;oBAC1B,WAAW,CAAC,iCAAiC,EAAE,gBAAgB,aACzD,mCACA,qBACF;8BACL;;;;;;;;;;;;;;;;;SAQF,OAAO;AACd", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/book-listing/page.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport moment from \"moment\";\r\nimport dynamic from \"next/dynamic\";\r\nimport {useEffect, useState} from \"react\";\r\n\r\nimport {USER_ROUTES} from \"@/app/config/api\";\r\nimport {debouncFunc, getToken} from \"@/app/utils/utils\";\r\nimport {toast} from \"react-toastify\";\r\nimport SubmitButton from \"../components/common/SubmitButton\";\r\nimport {deleteItem, getList} from \"../service/booklisting\";\r\nimport Popup from \"../components/common/PopUp\";\r\nimport Image from \"next/image\";\r\nimport MagnifierIcon from \"@/public/icons/magnifier_icon.svg\";\r\nimport Pagination from \"../components/common/Pagination\";\r\n\r\nconst TableComponent = dynamic(() =>\r\n  import(\"@/app/components/common/TableComponent/TableComponent\")\r\n);\r\n\r\nconst BookListing = () => {\r\n  const [openActionDropdown, setOpenActionDropdown] = useState(null);\r\n  const [selectedAll, setSelectedAll] = useState(false);\r\n  const [selectedData, setSelectedData] = useState([]);\r\n  const [isLoading, setisLoading] = useState(false);\r\n  const [submitisLoading, setsubmitisLoading] = useState(false);\r\n  const [deleteBoolean, setdeleteBoolean] = useState(false);\r\n  //Pagination\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [pageSize, setPageSize] = useState(10);\r\n  const [totalItems, setTotalItems] = useState(0);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [declineReason, setdeclineReason] = useState(\"\");\r\n  const [itemToDecline, setitemToDecline] = useState({});\r\n  console.log(\"totalPages\", totalPages);\r\n  console.log(\"pageSize\", pageSize);\r\n  const [deleteObject, setdeleteObject] = useState({\r\n    title: \"\",\r\n    message: \"\",\r\n    confirmText: \"\",\r\n    cancelText: \"\",\r\n    onConfirm: {},\r\n    onCancel: () => {},\r\n  });\r\n  const [tableDataObj, setTableDataObj] = useState({\r\n    headings: [\r\n      {\r\n        key: \"Name Item\",\r\n        width: \"25\",\r\n      },\r\n      {\r\n        key: \"Seller\",\r\n        width: \"15\",\r\n      },\r\n      {\r\n        key: \"Price\",\r\n        width: \"15\",\r\n      },\r\n      {\r\n        key: \"Location\",\r\n        width: \"25\",\r\n      },\r\n      {\r\n        key: \"Listed Date\",\r\n        width: \"15\",\r\n      },\r\n      {\r\n        key: \"Action\",\r\n        width: \"15\",\r\n        center: true,\r\n      },\r\n      {\r\n        key: \"\",\r\n        width: \"5\",\r\n      },\r\n    ],\r\n    content: [\r\n      // {\r\n      //     name_book: {\r\n      //         type: 'profileMix',\r\n      //         image: \"\",\r\n      //         name: \"Can’t Hurt me\" || \"-\",\r\n      //         secondaryValue: \"ISBN: 9898763\" || '-',\r\n      //         id: \"2\"\r\n      //     },\r\n      //     seller: {\r\n      //         type: 'default',\r\n      //         value: \"Himanshu\" || '-',\r\n      //     },\r\n      //     location: {\r\n      //         type: 'withoutLineClamp',\r\n      //         value: \"123, Hope Road, kingston 6, St.Andrew, Jamaica\" || '-',\r\n      //     },\r\n      //     listed_date: {\r\n      //         type: 'default',\r\n      //         value: `15-03-2025` || '-',\r\n      //     },\r\n      //     actions: {\r\n      //         type: 'actionMultiButtons',\r\n      //         buttons: [\r\n      //             {\r\n      //                 type: 'action_btns',\r\n      //                 btn1: \"Approved\",\r\n      //                 pendingBtn1: \"Decline\",\r\n      //                 pendingBtn2: \"Approve\",\r\n      //                 class1: \"text-xs leading-normal py-1.5 px-6 text-white border border-[#211F54] rounded-full global_text_linear_gradient\",\r\n      //                 pendingClass1: \"text-xs leading-normal py-1.5 px-2.5 text-white bg-[linear-gradient(90deg,#E1020C_0%,#4D7906_100%)] rounded-full\",\r\n      //                 pendingClass2: \"text-xs leading-normal py-1.5 px-2.5 text-white rounded-full global_linear_gradient\",\r\n      //                 onClick1: () => console.log('Primary action clicked'),\r\n      //                 onClick2: () => console.log('Secondary action clicked'),\r\n      //             },\r\n      //         ],\r\n      //         value: \"approved\",\r\n      //         class: '',\r\n      //     },\r\n      //     action: {\r\n      //         type: 'dropdownElem',\r\n      //         id: \"2\",\r\n      //         dropdownHandlerFn: dropdownHandler,\r\n      //         dropdownList: [\r\n      //             {\r\n      //                 label: \"Option1\"\r\n      //             },\r\n      //             {\r\n      //                 label: \"Option2\"\r\n      //             },\r\n      //         ]\r\n      //     },\r\n      // },\r\n    ],\r\n  });\r\n\r\n  console.log(\"book table\", tableDataObj);\r\n  console.log(\"isLoading\", isLoading);\r\n  console.log(\"declineReason\", declineReason);\r\n  const updateItemStatus = (item, status) => {\r\n    try {\r\n      let payload = {\r\n        status: status,\r\n      };\r\n      if (status == \"rejected\") {\r\n        payload.reason = declineReason;\r\n      }\r\n      console.log(\"item is \", item);\r\n\r\n      let userToken = getToken();\r\n      fetch(USER_ROUTES.UPDATE_LIST_ITEM + \"/\" + item?._id, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${userToken}`,\r\n        },\r\n        body: JSON.stringify(payload),\r\n      }).then(async (res) => {\r\n        const response = await res.json();\r\n        let bookData = response.data;\r\n        if (!bookData?.error) {\r\n          toast.success(\"Updated successfully!\");\r\n          getListing();\r\n        } else {\r\n          console.log(\"bookData err\", bookData);\r\n          toast.error(response?.message || \"Internal Server Error\");\r\n        }\r\n        setdeclineReason(\"\");\r\n        setitemToDecline({});\r\n        document.getElementById(\"myModal\").classList.add(\"hidden\");\r\n      });\r\n\r\n      setOpenActionDropdown(null);\r\n    } catch (error) {\r\n      console.log(\"error\", error);\r\n    }\r\n  };\r\n\r\n  console.log(deleteObject, \"deleteObject\");\r\n\r\n  const deleteItemSoft = async (item) => {\r\n    try {\r\n      setdeleteBoolean(true);\r\n      setdeleteObject({\r\n        title: \"Remove Item\",\r\n        confirmText: \"Remove\",\r\n        cancelText: \"Cancel\",\r\n        message: \"Are You sure want to remove it?\",\r\n        onConfirm: async () => {\r\n          setisLoading(true);\r\n          let response = await deleteItem(`/${item?._id}`);\r\n          if (response.status == 200) {\r\n            toast.success(\"Removed successfully!\");\r\n            getListing();\r\n            setdeleteBoolean(false);\r\n          }\r\n          setisLoading(false);\r\n        },\r\n        onCancel: () => setdeleteBoolean(false),\r\n      });\r\n    } catch (error) {\r\n      console.log(\"error\", error);\r\n    }\r\n  };\r\n\r\n  const copyFunction = () => {};\r\n\r\n  const deleteItemPermanent = async (item) => {\r\n    try {\r\n      setdeleteBoolean(true);\r\n      setdeleteObject({\r\n        title: \"Delete Item Permanently\",\r\n        confirmText: \"Delete Permanently\",\r\n        cancelText: \"Cancel\",\r\n        message: \"Are You sure want to Delete it Permanently it?\",\r\n        onConfirm: async () => {\r\n          setisLoading(true);\r\n          let response = await deleteItem(`/${item?._id}` + \"?hard=true\");\r\n          if (response.status == 200) {\r\n            toast.success(\"Deleted successfully!\");\r\n            getListing();\r\n            setdeleteBoolean(false);\r\n          }\r\n          setisLoading(false);\r\n        },\r\n        onCancel: () => setdeleteBoolean(false),\r\n      });\r\n\r\n      // let response = await deleteItem(`/${item?._id}` + \"?hard=true\")\r\n      // if (response.status == 200) {\r\n      //     toast.success(\"Deleted successfully!\")\r\n      //     getListing()\r\n      // }\r\n    } catch (error) {\r\n      console.log(\"error\", error);\r\n    }\r\n  };\r\n\r\n  const getListing = async (search, page = currentPage) => {\r\n    try {\r\n      let userToken = getToken();\r\n      setisLoading(true);\r\n      let payload = {\r\n        filters: {},\r\n        sort: {createdAt: -1},\r\n      };\r\n\r\n      // Build query parameters\r\n      const queryParams = new URLSearchParams({\r\n        page: page,\r\n        pageSize: pageSize,\r\n        // sort: JSON.stringify({createdAt: -1}),\r\n      });\r\n\r\n      // Construct the URL with query parameters\r\n      const url = `${USER_ROUTES.LIST_ITEM}?${queryParams.toString()}`;\r\n\r\n      // Make GET request with query parameters\r\n      //   const response = await fetch(url, {\r\n      //     method: \"GET\",\r\n      //     headers: {\r\n      //       \"Content-Type\": \"application/json\",\r\n      //       Authorization: `Bearer ${getToken()}`,\r\n      //     },\r\n      //   });\r\n\r\n      if (search) {\r\n        payload.filters = {keyword: search};\r\n        payload.sort = {};\r\n      }\r\n      let query = \"?\";\r\n      if (!search && currentPage) {\r\n        query = query + `&page=${currentPage}`;\r\n      }\r\n      if (pageSize) {\r\n        query = query + `&pageSize=${pageSize}`;\r\n      }\r\n      let bookData = await getList(query, payload);\r\n      bookData = bookData?.data;\r\n      console.log(\"bookData\", bookData);\r\n\r\n      setTotalItems(bookData?.totalCount || 0);\r\n      setTotalPages(bookData?.totalPages || 1);\r\n      //   setCurrentPage(bookData?.page || 1);\r\n\r\n      setTableDataObj((prev) => ({\r\n        ...prev,\r\n        content: bookData.data?.map((book) => {\r\n          const approvedClass = \"bg-green-200 font-semibold text-green-600\";\r\n          const rejectedClass = \"bg-red-200 font-semibold text-red-600 \";\r\n          const pendingClass = \"global_text_linear_gradient\";\r\n          return {\r\n            name_book: {\r\n              type: \"profileMix\",\r\n              image: book.images.length ? book.images[0] : \"\",\r\n              name: book?.title,\r\n              secondaryValue: book?.categoryDoc?.name,\r\n              // secondaryValue: book?.price,\r\n              id: book._id,\r\n            },\r\n            seller: {\r\n              type: \"default\",\r\n              value:\r\n                `${book?.createdByDoc?.firstName} ${book?.createdByDoc?.lastName}` ||\r\n                \"-\",\r\n            },\r\n            price: {\r\n              type: \"default\",\r\n              value: \"J$\" + book?.price || \"-\",\r\n            },\r\n            location: {\r\n              type: \"withoutLineClamp\",\r\n              value: book.address?.formatted_address || \"-\",\r\n            },\r\n            listed_date: {\r\n              type: \"default\",\r\n              value: moment(book?.createdAt).format(\"DD-MM-YYYY\") || \"-\",\r\n            },\r\n            actions: {\r\n              type: \"actionMultiButtons\",\r\n              buttons: [\r\n                {\r\n                  type: \"action_btns\",\r\n                  btn1: book?.status,\r\n                  pendingBtn1: \"Decline\",\r\n                  pendingBtn2: \"Approve\",\r\n                  class1:\r\n                    book?.status === \"accepted\"\r\n                      ? `text-xs py-1.5 px-6  rounded-full ${approvedClass}`\r\n                      : book?.status === \"removed by admin\"\r\n                      ? `text-xs py-1.5 px-6  rounded-full ${rejectedClass}`\r\n                      : `text-xs py-1.5 px-6  border border-[#211F54] rounded-full ${pendingClass}`,\r\n                  pendingClass1:\r\n                    \"text-xs leading-normal py-1.5 px-2.5 text-white bg-[linear-gradient(90deg,#E1020C_0%,#4D7906_100%)] rounded-full\",\r\n                  pendingClass2:\r\n                    \"text-xs leading-normal py-1.5 px-2.5 text-white rounded-full global_linear_gradient\",\r\n                  onClick1:\r\n                    book?.status === \"pending\"\r\n                      ? () => openModelForDeclineReason(book, \"rejected\")\r\n                      : undefined,\r\n                  onClick2:\r\n                    book?.status === \"pending\"\r\n                      ? () => updateItemStatus(book, \"accepted\")\r\n                      : undefined,\r\n                },\r\n              ],\r\n              value: book.status,\r\n              class: \"\",\r\n            },\r\n            action: {\r\n              type: \"dropdownElem\",\r\n              id: book._id,\r\n              dropdownHandlerFn: dropdownHandler,\r\n              dropdownList: [\r\n                {\r\n                  label: \"Delete\",\r\n                  clickFn: () => deleteItemSoft(book),\r\n                },\r\n                {\r\n                  label: \"Delete Permanently\",\r\n                  clickFn: () => deleteItemPermanent(book),\r\n                },\r\n              ],\r\n            },\r\n          };\r\n        }),\r\n      }));\r\n\r\n      setisLoading(false);\r\n    } catch (error) {\r\n      setisLoading(false);\r\n      console.log(\"error\", error);\r\n    }\r\n  };\r\n  useEffect(() => {\r\n    getListing();\r\n  }, [currentPage, pageSize]);\r\n\r\n  // Handle page size change\r\n  const handlePageChange = (number) => {\r\n    setCurrentPage(number);\r\n  };\r\n  const handlePageSizeChange = (e) => {\r\n    const newValue = parseInt(e.target.value);\r\n    setPageSize(newValue);\r\n    setCurrentPage(1);\r\n    getListing(undefined, 1);\r\n  };\r\n\r\n  console.log(\"pageSize\", pageSize);\r\n  //   // Handle items per page change\r\n  //   const handleItemsPerPageChange = (e) => {\r\n  //     const newValue = parseInt(e.target.value);\r\n  //     setItemsPerPage(newValue);\r\n  //     setCurrentPage(1);\r\n  //     getListing(undefined, 1);\r\n  //   };\r\n\r\n  function dropdownHandler(id) {\r\n    setOpenActionDropdown((prevId) => (prevId === id ? null : id));\r\n  }\r\n\r\n  function closeDropdown() {\r\n    setOpenActionDropdown(null);\r\n  }\r\n\r\n  const selectAllHandler = () => {\r\n    setSelectedAll(!selectedAll);\r\n    if (selectedAll) {\r\n      setSelectedData([]);\r\n    } else {\r\n      // Todo - apiData is used when api is added\r\n      // setSelectedData(tableDataObj.content?.map((item) => item.id));\r\n    }\r\n  };\r\n\r\n  // console.log(\"selectedData\", typeof selectedData)\r\n\r\n  const singleSelectHandler = (itemId) => {\r\n    setSelectedData((prev) => {\r\n      const currentSelection = Array.isArray(prev)\r\n        ? prev\r\n        : [prev].filter(Boolean);\r\n\r\n      if (currentSelection.includes(itemId)) {\r\n        return currentSelection.filter((id) => id !== itemId);\r\n      } else {\r\n        return [...currentSelection, itemId];\r\n      }\r\n    });\r\n  };\r\n  const openModelForDeclineReason = (item, status) => {\r\n    if (status == \"rejected\") {\r\n      setitemToDecline(item);\r\n      document.getElementById(\"myModal\").classList.remove(\"hidden\");\r\n    } else {\r\n      // updateItemStatus(item, status)\r\n    }\r\n  };\r\n  const InnerDiv = () => {\r\n    return <div> Decline</div>;\r\n  };\r\n  const submitQuestion = () => {\r\n    setsubmitisLoading(true);\r\n    updateItemStatus(itemToDecline, \"rejected\");\r\n    setTimeout(() => {\r\n      setsubmitisLoading(false);\r\n    }, 1000);\r\n  };\r\n  const searchHandle = (event) => {\r\n    console.log(event.target.value, \"debounce event\");\r\n    let payload = {\r\n      searchTerm: event.target.value || \"\",\r\n    };\r\n    getListing(event.target.value || \"\");\r\n  };\r\n  let debounceHandle = debouncFunc(searchHandle, 1000);\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl p-5\">\r\n      <div className=\"flex  items-center justify-between mb-2\">\r\n        <p className=\"font-semibold text-[20px]\">Item Listing</p>\r\n        <div className=\"bg-gray-50 px-2 rounded-lg flex items-center\">\r\n          <div className=\"w-6 h-6  relative overflow-hidden\">\r\n            <Image\r\n              src={MagnifierIcon}\r\n              alt=\"Search Icon\"\r\n              fill\r\n              className=\"object-cover\"\r\n              sizes=\"24px\"\r\n            />\r\n          </div>\r\n          <input\r\n            placeholder=\"Search Items...\"\r\n            className=\"rounded-lg outline-none bg-gray-50 ps-2 p-2\"\r\n            onChange={debounceHandle}\r\n          />\r\n        </div>\r\n      </div>\r\n      <TableComponent\r\n        tableDataObj={tableDataObj}\r\n        loading={isLoading}\r\n        selectAllHandler={selectAllHandler}\r\n        selectedAll={selectedAll}\r\n        selectedData={selectedData}\r\n        singleSelectHandler={singleSelectHandler}\r\n        openActionDropdown={openActionDropdown}\r\n        closeDropdown={closeDropdown}\r\n      />\r\n\r\n      {/* +++++++++++++++ PAGINATION ++++++++++++++++++++++++++ */}\r\n      <Pagination\r\n        setPageSize={setPageSize}\r\n        setCurrentPage={setCurrentPage}\r\n        getListing={getListing}\r\n        currentPage={currentPage}\r\n        totalPages={totalPages}\r\n        totalItems={totalItems}\r\n        pageSize={pageSize}\r\n      />\r\n      {/* +++++++++++++++ PAGINATION ++++++++++++++++++++++++++ */}\r\n      <div\r\n        id=\"myModal\"\r\n        className=\" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]\"\r\n      >\r\n        <div className=\"bg-[#EAEAEA]  rounded-lg w-full max-w-lg shadow-lg relative\">\r\n          <div\r\n            className=\"flex bg-white w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full  \"\r\n            onClick={() => {\r\n              let docElement = document\r\n                .getElementById(\"myModal\")\r\n                .classList.add(\"hidden\");\r\n              console.log(\"docElement\", docElement);\r\n              // setaddQuestionInput(\"\")\r\n              // setmodelForAnswer(false)\r\n            }}\r\n          >\r\n            <button className=\"text-gray-500 hover:text-red-600 text-xl font-bold\">\r\n              &times;\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"py-3 bg-white rounded-lg \">\r\n            <h2 className=\"text-xl font-semibold mb-4  border-b w-fit mx-auto\">\r\n              {\"Enter Reason\"}\r\n            </h2>\r\n          </div>\r\n\r\n          <div className=\"px-3 mt-2\">\r\n            <textarea\r\n              className=\"rounded-lg outline-none w-full border border-gray-400 p-2\"\r\n              rows={4}\r\n              onChange={(e) => setdeclineReason(e.target.value)}\r\n            />\r\n          </div>\r\n          {/* <!-- Action Button --> */}\r\n          <div className=\"my-2 flex justify-start mx-4\">\r\n            {/* <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>\r\n                                        <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'\r\n                                            onClick={submitQuestion}\r\n                                        >Submit</button>\r\n            \r\n                                    </div> */}\r\n            <div className=\"max-w-[300px] \">\r\n              <SubmitButton\r\n                isLoading={submitisLoading}\r\n                InnerDiv={InnerDiv}\r\n                type={\"button\"}\r\n                btnAction={submitQuestion}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* <div className=\"mb-[40px] h-[100px]\"></div> */}\r\n      <Popup\r\n        isOpen={deleteBoolean}\r\n        actionLoading={isLoading}\r\n        {...deleteObject}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BookListing;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAdA;;;;;;;;;;;;;;AAgBA,MAAM,iBAAiB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;AAI/B,MAAM,cAAc;IAClB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,YAAY;IACZ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,QAAQ,GAAG,CAAC,cAAc;IAC1B,QAAQ,GAAG,CAAC,YAAY;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,OAAO;QACP,SAAS;QACT,aAAa;QACb,YAAY;QACZ,WAAW,CAAC;QACZ,UAAU,KAAO;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,UAAU;YACR;gBACE,KAAK;gBACL,OAAO;YACT;YACA;gBACE,KAAK;gBACL,OAAO;YACT;YACA;gBACE,KAAK;gBACL,OAAO;YACT;YACA;gBACE,KAAK;gBACL,OAAO;YACT;YACA;gBACE,KAAK;gBACL,OAAO;YACT;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;YACV;YACA;gBACE,KAAK;gBACL,OAAO;YACT;SACD;QACD,SAAS,EAqDR;IACH;IAEA,QAAQ,GAAG,CAAC,cAAc;IAC1B,QAAQ,GAAG,CAAC,aAAa;IACzB,QAAQ,GAAG,CAAC,iBAAiB;IAC7B,MAAM,mBAAmB,CAAC,MAAM;QAC9B,IAAI;YACF,IAAI,UAAU;gBACZ,QAAQ;YACV;YACA,IAAI,UAAU,YAAY;gBACxB,QAAQ,MAAM,GAAG;YACnB;YACA,QAAQ,GAAG,CAAC,YAAY;YAExB,IAAI,YAAY,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;YACvB,MAAM,oHAAA,CAAA,cAAW,CAAC,gBAAgB,GAAG,MAAM,MAAM,KAAK;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,eAAe,CAAC,OAAO,EAAE,WAAW;gBACtC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB,GAAG,IAAI,CAAC,OAAO;gBACb,MAAM,WAAW,MAAM,IAAI,IAAI;gBAC/B,IAAI,WAAW,SAAS,IAAI;gBAC5B,IAAI,CAAC,UAAU,OAAO;oBACpB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC,gBAAgB;oBAC5B,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,UAAU,WAAW;gBACnC;gBACA,iBAAiB;gBACjB,iBAAiB,CAAC;gBAClB,SAAS,cAAc,CAAC,WAAW,SAAS,CAAC,GAAG,CAAC;YACnD;YAEA,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;QACvB;IACF;IAEA,QAAQ,GAAG,CAAC,cAAc;IAE1B,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,iBAAiB;YACjB,gBAAgB;gBACd,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,SAAS;gBACT,WAAW;oBACT,aAAa;oBACb,IAAI,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK;oBAC/C,IAAI,SAAS,MAAM,IAAI,KAAK;wBAC1B,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd;wBACA,iBAAiB;oBACnB;oBACA,aAAa;gBACf;gBACA,UAAU,IAAM,iBAAiB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;QACvB;IACF;IAEA,MAAM,eAAe,KAAO;IAE5B,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,iBAAiB;YACjB,gBAAgB;gBACd,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,SAAS;gBACT,WAAW;oBACT,aAAa;oBACb,IAAI,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,GAAG;oBAClD,IAAI,SAAS,MAAM,IAAI,KAAK;wBAC1B,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd;wBACA,iBAAiB;oBACnB;oBACA,aAAa;gBACf;gBACA,UAAU,IAAM,iBAAiB;YACnC;QAEA,kEAAkE;QAClE,gCAAgC;QAChC,6CAA6C;QAC7C,mBAAmB;QACnB,IAAI;QACN,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,SAAS;QACvB;IACF;IAEA,MAAM,aAAa,OAAO,QAAQ,OAAO,WAAW;QAClD,IAAI;YACF,IAAI,YAAY,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;YACvB,aAAa;YACb,IAAI,UAAU;gBACZ,SAAS,CAAC;gBACV,MAAM;oBAAC,WAAW,CAAC;gBAAC;YACtB;YAEA,yBAAyB;YACzB,MAAM,cAAc,IAAI,gBAAgB;gBACtC,MAAM;gBACN,UAAU;YAEZ;YAEA,0CAA0C;YAC1C,MAAM,MAAM,GAAG,oHAAA,CAAA,cAAW,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI;YAEhE,yCAAyC;YACzC,wCAAwC;YACxC,qBAAqB;YACrB,iBAAiB;YACjB,4CAA4C;YAC5C,+CAA+C;YAC/C,SAAS;YACT,QAAQ;YAER,IAAI,QAAQ;gBACV,QAAQ,OAAO,GAAG;oBAAC,SAAS;gBAAM;gBAClC,QAAQ,IAAI,GAAG,CAAC;YAClB;YACA,IAAI,QAAQ;YACZ,IAAI,CAAC,UAAU,aAAa;gBAC1B,QAAQ,QAAQ,CAAC,MAAM,EAAE,aAAa;YACxC;YACA,IAAI,UAAU;gBACZ,QAAQ,QAAQ,CAAC,UAAU,EAAE,UAAU;YACzC;YACA,IAAI,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,UAAO,AAAD,EAAE,OAAO;YACpC,WAAW,UAAU;YACrB,QAAQ,GAAG,CAAC,YAAY;YAExB,cAAc,UAAU,cAAc;YACtC,cAAc,UAAU,cAAc;YACtC,yCAAyC;YAEzC,gBAAgB,CAAC,OAAS,CAAC;oBACzB,GAAG,IAAI;oBACP,SAAS,SAAS,IAAI,EAAE,IAAI,CAAC;wBAC3B,MAAM,gBAAgB;wBACtB,MAAM,gBAAgB;wBACtB,MAAM,eAAe;wBACrB,OAAO;4BACL,WAAW;gCACT,MAAM;gCACN,OAAO,KAAK,MAAM,CAAC,MAAM,GAAG,KAAK,MAAM,CAAC,EAAE,GAAG;gCAC7C,MAAM,MAAM;gCACZ,gBAAgB,MAAM,aAAa;gCACnC,+BAA+B;gCAC/B,IAAI,KAAK,GAAG;4BACd;4BACA,QAAQ;gCACN,MAAM;gCACN,OACE,GAAG,MAAM,cAAc,UAAU,CAAC,EAAE,MAAM,cAAc,UAAU,IAClE;4BACJ;4BACA,OAAO;gCACL,MAAM;gCACN,OAAO,OAAO,MAAM,SAAS;4BAC/B;4BACA,UAAU;gCACR,MAAM;gCACN,OAAO,KAAK,OAAO,EAAE,qBAAqB;4BAC5C;4BACA,aAAa;gCACX,MAAM;gCACN,OAAO,CAAA,GAAA,gIAAA,CAAA,UAAM,AAAD,EAAE,MAAM,WAAW,MAAM,CAAC,iBAAiB;4BACzD;4BACA,SAAS;gCACP,MAAM;gCACN,SAAS;oCACP;wCACE,MAAM;wCACN,MAAM,MAAM;wCACZ,aAAa;wCACb,aAAa;wCACb,QACE,MAAM,WAAW,aACb,CAAC,kCAAkC,EAAE,eAAe,GACpD,MAAM,WAAW,qBACjB,CAAC,kCAAkC,EAAE,eAAe,GACpD,CAAC,0DAA0D,EAAE,cAAc;wCACjF,eACE;wCACF,eACE;wCACF,UACE,MAAM,WAAW,YACb,IAAM,0BAA0B,MAAM,cACtC;wCACN,UACE,MAAM,WAAW,YACb,IAAM,iBAAiB,MAAM,cAC7B;oCACR;iCACD;gCACD,OAAO,KAAK,MAAM;gCAClB,OAAO;4BACT;4BACA,QAAQ;gCACN,MAAM;gCACN,IAAI,KAAK,GAAG;gCACZ,mBAAmB;gCACnB,cAAc;oCACZ;wCACE,OAAO;wCACP,SAAS,IAAM,eAAe;oCAChC;oCACA;wCACE,OAAO;wCACP,SAAS,IAAM,oBAAoB;oCACrC;iCACD;4BACH;wBACF;oBACF;gBACF,CAAC;YAED,aAAa;QACf,EAAE,OAAO,OAAO;YACd,aAAa;YACb,QAAQ,GAAG,CAAC,SAAS;QACvB;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;KAAS;IAE1B,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IACA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;QACxC,YAAY;QACZ,eAAe;QACf,WAAW,WAAW;IACxB;IAEA,QAAQ,GAAG,CAAC,YAAY;IACxB,oCAAoC;IACpC,8CAA8C;IAC9C,iDAAiD;IACjD,iCAAiC;IACjC,yBAAyB;IACzB,gCAAgC;IAChC,OAAO;IAEP,SAAS,gBAAgB,EAAE;QACzB,sBAAsB,CAAC,SAAY,WAAW,KAAK,OAAO;IAC5D;IAEA,SAAS;QACP,sBAAsB;IACxB;IAEA,MAAM,mBAAmB;QACvB,eAAe,CAAC;QAChB,IAAI,aAAa;YACf,gBAAgB,EAAE;QACpB,OAAO;QACL,2CAA2C;QAC3C,iEAAiE;QACnE;IACF;IAEA,mDAAmD;IAEnD,MAAM,sBAAsB,CAAC;QAC3B,gBAAgB,CAAC;YACf,MAAM,mBAAmB,MAAM,OAAO,CAAC,QACnC,OACA;gBAAC;aAAK,CAAC,MAAM,CAAC;YAElB,IAAI,iBAAiB,QAAQ,CAAC,SAAS;gBACrC,OAAO,iBAAiB,MAAM,CAAC,CAAC,KAAO,OAAO;YAChD,OAAO;gBACL,OAAO;uBAAI;oBAAkB;iBAAO;YACtC;QACF;IACF;IACA,MAAM,4BAA4B,CAAC,MAAM;QACvC,IAAI,UAAU,YAAY;YACxB,iBAAiB;YACjB,SAAS,cAAc,CAAC,WAAW,SAAS,CAAC,MAAM,CAAC;QACtD,OAAO;QACL,iCAAiC;QACnC;IACF;IACA,MAAM,WAAW;QACf,qBAAO,8OAAC;sBAAI;;;;;;IACd;IACA,MAAM,iBAAiB;QACrB,mBAAmB;QACnB,iBAAiB,eAAe;QAChC,WAAW;YACT,mBAAmB;QACrB,GAAG;IACL;IACA,MAAM,eAAe,CAAC;QACpB,QAAQ,GAAG,CAAC,MAAM,MAAM,CAAC,KAAK,EAAE;QAChC,IAAI,UAAU;YACZ,YAAY,MAAM,MAAM,CAAC,KAAK,IAAI;QACpC;QACA,WAAW,MAAM,MAAM,CAAC,KAAK,IAAI;IACnC;IACA,IAAI,iBAAiB,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE,cAAc;IAE/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA4B;;;;;;kCACzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,kSAAA,CAAA,UAAa;oCAClB,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;;;;;;0CAGV,8OAAC;gCACC,aAAY;gCACZ,WAAU;gCACV,UAAU;;;;;;;;;;;;;;;;;;0BAIhB,8OAAC;gBACC,cAAc;gBACd,SAAS;gBACT,kBAAkB;gBAClB,aAAa;gBACb,cAAc;gBACd,qBAAqB;gBACrB,oBAAoB;gBACpB,eAAe;;;;;;0BAIjB,8OAAC,yIAAA,CAAA,UAAU;gBACT,aAAa;gBACb,gBAAgB;gBAChB,YAAY;gBACZ,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;;;;;;0BAGZ,8OAAC;gBACC,IAAG;gBACH,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,SAAS;gCACP,IAAI,aAAa,SACd,cAAc,CAAC,WACf,SAAS,CAAC,GAAG,CAAC;gCACjB,QAAQ,GAAG,CAAC,cAAc;4BAC1B,0BAA0B;4BAC1B,2BAA2B;4BAC7B;sCAEA,cAAA,8OAAC;gCAAO,WAAU;0CAAqD;;;;;;;;;;;sCAKzE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX;;;;;;;;;;;sCAIL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,MAAM;gCACN,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;sCAOb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2IAAA,CAAA,UAAY;oCACX,WAAW;oCACX,UAAU;oCACV,MAAM;oCACN,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,8OAAC,oIAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,eAAe;gBACd,GAAG,YAAY;;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}]}