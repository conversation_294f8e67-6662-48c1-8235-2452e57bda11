(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/public/landing/testbook.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/testbook.9fc8f82a.png");}}),
"[project]/public/landing/testbook.png.mjs { IMAGE => \"[project]/public/landing/testbook.png (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$testbook$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/landing/testbook.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$testbook$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 101,
    height: 131,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAYAAADaxo44AAAA00lEQVR42gHIADf/AMcpHfDQNB350C0g+c4gIvnRKCL5zCEf8gDIIRr2zzIi/8pCOP/IQT7/23gr/8xGI/YAviAc9sQvKP/JQC//zjgl/9RJIf/KOBz2AHJlTPaIbDv/h35Q/6OTX/+noXj/o6J49gBZfm72e31E/1B5bv9pkHf/O3Z7/yt+j/YAWoZx9pR6NP9Ig37/YJN4/0NgW/87dYH2AH+ETvaqgCz/dYVb/26KZf9DZGT/SHWA9gC+oV31pqJy/tWuZf7BsYL+wdDQ/sfS1PZCXm9QAGlHSAAAAABJRU5ErkJggg==",
    blurWidth: 6,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/BookCard.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FeaturedBooks)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// import featuredCss from "./featurebook.module.scss"
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$testbook$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$landing$2f$testbook$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/landing/testbook.png.mjs { IMAGE => "[project]/public/landing/testbook.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/lu/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function FeaturedBooks() {
    _s();
    const [book, setBooks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        name: "Psycology of moeny",
        image: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$testbook$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$landing$2f$testbook$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        price: 500
    });
    const tempData = [
        {
            image: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$testbook$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$landing$2f$testbook$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
            name: "Level 3 Geography Grade 3 2022",
            price: "3500",
            link: ""
        }
    ];
    const [featuredBooks, setFeaturedBooks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FeaturedBooks.useEffect": ()=>{
            setFeaturedBooks(tempData);
        }
    }["FeaturedBooks.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: ` px-2.5 md:px-[50px] lg:px-[100px]`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container-wrapper",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "my-5 grid grid-cols-2 lg:grid-cols-4 flex-wrap gap-2.5 md:my-14 md:gap-8 md:justify-between lg:mt-[30px]",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("article", {
                    className: "border border-[#80808017] pt-1 px-[3px] pb-[15px] lg:border-0",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative flex justify-center items-center py-1.5 bg-[#f1f1f1] lg:h-[224px] lg:py-2.5 lg:px-2.5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: book.image,
                                    alt: "book image",
                                    objectFit: "cover",
                                    sizes: "50w",
                                    className: "w-1/2"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/BookCard.js",
                                    lineNumber: 85,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-7 w-7 p-1 rounded-full border border-gray-200 bg-white flex justify-center items-center absolute top-1 right-1 cursor-pointer",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LuHeart"], {
                                        size: 17,
                                        color: "#000",
                                        className: "opacity-90"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/BookCard.js",
                                        lineNumber: 87,
                                        columnNumber: 37
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/BookCard.js",
                                    lineNumber: 86,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/common/BookCard.js",
                            lineNumber: 84,
                            columnNumber: 29
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "pt-2 pl-2 pr-2.5 md:text-[20px] lg:pt-5 lg:pb-[28px]",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm leading-[19px] font-semibold lg:text-[20px] lg:leading-[26px] lg:w-11/12",
                                    children: book.name
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/BookCard.js",
                                    lineNumber: 92,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex justify-between items-center my-2.5",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "font-bold self-end",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-[#4D7906] lg:text-[20px] lg:leading-[22px]",
                                                    children: "JMD "
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/common/BookCard.js",
                                                    lineNumber: 96,
                                                    columnNumber: 41
                                                }, this),
                                                "$",
                                                book.price
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/common/BookCard.js",
                                            lineNumber: 95,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "cursor-pointer global_linear_gradient p-2 rounded-full flex justify-center items-center lg:h-[46px] lg:w-[46px]",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdArrowOutward"], {
                                                className: "h-[12px] w-[12px] lg:w-[16px] lg:h-[16px]",
                                                color: "#fff"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/common/BookCard.js",
                                                lineNumber: 99,
                                                columnNumber: 41
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/common/BookCard.js",
                                            lineNumber: 98,
                                            columnNumber: 37
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/common/BookCard.js",
                                    lineNumber: 94,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/common/BookCard.js",
                            lineNumber: 91,
                            columnNumber: 29
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/common/BookCard.js",
                    lineNumber: 83,
                    columnNumber: 25
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/components/common/BookCard.js",
                lineNumber: 81,
                columnNumber: 17
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/components/common/BookCard.js",
            lineNumber: 54,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/common/BookCard.js",
        lineNumber: 52,
        columnNumber: 9
    }, this);
}
_s(FeaturedBooks, "w5EL5LiOPd97afv6d/R13MgvKXY=");
_c = FeaturedBooks;
var _c;
__turbopack_context__.k.register(_c, "FeaturedBooks");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/book-detail/CircleRating.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
const CircularRating = ({ rating = 4.5, maxRating = 5, size = 80 })=>{
    const strokeWidth = 8;
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    // Calculate the progress as a percentage of circumference
    const progress = rating / maxRating * circumference;
    const progressPercent = rating / maxRating * 100;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            width: size,
            height: size
        },
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                width: size,
                height: size,
                className: "transform -rotate-90",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                        stroke: "#e5e7eb" // Tailwind gray-200
                        ,
                        fill: "transparent",
                        strokeWidth: strokeWidth,
                        r: radius,
                        cx: size / 2,
                        cy: size / 2
                    }, void 0, false, {
                        fileName: "[project]/app/book-detail/CircleRating.js",
                        lineNumber: 20,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                        stroke: "#fbbf24" // Tailwind yellow-400
                        ,
                        fill: "transparent",
                        strokeWidth: strokeWidth,
                        strokeLinecap: "round",
                        strokeDasharray: circumference,
                        strokeDashoffset: circumference - progress,
                        r: radius,
                        cx: size / 2,
                        cy: size / 2
                    }, void 0, false, {
                        fileName: "[project]/app/book-detail/CircleRating.js",
                        lineNumber: 29,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/book-detail/CircleRating.js",
                lineNumber: 14,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 flex items-center justify-center font-semibold text-xl text-yellow-600 select-none",
                children: rating.toFixed(1)
            }, void 0, false, {
                fileName: "[project]/app/book-detail/CircleRating.js",
                lineNumber: 43,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/book-detail/CircleRating.js",
        lineNumber: 13,
        columnNumber: 5
    }, this);
};
_c = CircularRating;
const __TURBOPACK__default__export__ = CircularRating;
var _c;
__turbopack_context__.k.register(_c, "CircularRating");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {
        // Clear local storage and redirect to /login
        localStorage.clear();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
        window.location.href = "/login";
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        // console.log("error log is", error)
        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.message)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://devapi.rebookit.club");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/profile.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addReviewForSeller": (()=>addReviewForSeller),
    "bookMarkItem": (()=>bookMarkItem),
    "boostItem": (()=>boostItem),
    "deleteChatById": (()=>deleteChatById),
    "deleteMyBooks": (()=>deleteMyBooks),
    "delete_bookMarkItem": (()=>delete_bookMarkItem),
    "editItem": (()=>editItem),
    "getAdPlanById": (()=>getAdPlanById),
    "getAdPlans": (()=>getAdPlans),
    "getAllChat": (()=>getAllChat),
    "getBooksById": (()=>getBooksById),
    "getCategories": (()=>getCategories),
    "getChatById": (()=>getChatById),
    "getFaq": (()=>getFaq),
    "getItemBySeller": (()=>getItemBySeller),
    "getMyBooks": (()=>getMyBooks),
    "getPaymentIntent": (()=>getPaymentIntent),
    "getReviewsOfUser": (()=>getReviewsOfUser),
    "getSubCategories": (()=>getSubCategories),
    "getSubSubCategories": (()=>getSubSubCategories),
    "getSubSubSubCategories": (()=>getSubSubSubCategories),
    "getTestimonials": (()=>getTestimonials),
    "get_bookMarkItems": (()=>get_bookMarkItems),
    "getsubscriptionPlans": (()=>getsubscriptionPlans),
    "listItem": (()=>listItem),
    "paymentHistory": (()=>paymentHistory),
    "searchByName": (()=>searchByName),
    "searchISBN": (()=>searchISBN),
    "searchItemByName": (()=>searchItemByName),
    "supportRequest": (()=>supportRequest),
    "uploadPhotoSingle": (()=>uploadPhotoSingle),
    "verifyUserData": (()=>verifyUserData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/axios.js [app-client] (ecmascript)");
;
;
;
const uri = {
    login: "/user/login",
    userInfo: "/user",
    editProfile: "/user/edit-profile",
    item_by_name: `item/search`,
    subscriptionPlan: "/admin/subscription/plan",
    fetch_category: "/master/category",
    fetchSubCategory: "master/sub-category",
    fetchSubSubCategory: "master/Sub-Sub-category",
    fetchSubSubSubCategory: "master/sub-sub-sub-category",
    getPaymentIntent: "/payment/payment-intent",
    verifyUserData: "user/verify-otp",
    searchISBN: "/books/isbn/{{ISBN}}",
    searchByName: "/books/search?q={search}",
    bookMarkItem_id: "/item/bookmark",
    get_bookMark_by_user: "/item/bookmarks",
    getItems: "/item/search/current-user",
    getItemById: "/item",
    createItem: "/item",
    editItem: "/item",
    deleteItemById: "/item",
    itemBySeller: "/item/user",
    addReview: "/item/addReview",
    userReviews: "/item/{:id}/reviews",
    uploadPhoto: "admin/single-upload",
    history: "/payment/history",
    supportRequest: "/user/support-request",
    boostItem: "/item/boost",
    getTestimonials: "/user/testimonials",
    getFaq: "/faqs/filter-search",
    // ad-management
    getAdPlans: "ad-management/getAdPlans",
    getAdPlanById: "ad-management/getAdPlanById"
};
const chat = {
    chat: "/chat/all",
    chatById: "/chat"
};
const listItem = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.createItem}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const getAdPlans = async ({ type, position, page = 1, limit = 10 } = {})=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlans}`, {
        params: {
            ...type && {
                type
            },
            ...position && {
                position
            },
            page,
            limit
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getAdPlanById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlanById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const editItem = async (payload, id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.editItem}/${id}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const addReviewForSeller = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.addReview}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getMyBooks = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getItems}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const deleteMyBooks = async (id, data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.deleteItemById}/${id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getBooksById = async (id, userId)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getItemById}/${id}`, {
        params: {
            userId
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getItemBySeller = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.itemBySeller}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const searchItemByName = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.item_by_name}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getsubscriptionPlans = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.subscriptionPlan}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetch_category}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getAllChat = async (payloadData)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${chat.chat}`, payloadData).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const deleteChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const bookMarkItem = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.bookMarkItem_id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const getReviewsOfUser = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.userReviews}`.replace("{:id}", id));
    // .catch(axiosErrorHandler);
    console.log("getSubCategories response", response);
    return response;
};
const delete_bookMarkItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${uri.bookMarkItem_id}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const get_bookMarkItems = async ()=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.get_bookMark_by_user}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getPaymentIntent = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getPaymentIntent}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const verifyUserData = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.verifyUserData}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchISBN = async (ISBN)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchISBN}`.replace("{{ISBN}}", ISBN)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchByName = async (search)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchByName}`.replace("{search}", search)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const uploadPhotoSingle = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.uploadPhoto}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const paymentHistory = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.history}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const supportRequest = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.supportRequest}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const boostItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.boostItem}/` + id).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getTestimonials = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getTestimonials}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getFaq = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getFaq}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
} // let data = await fetch(USER_ROUTES.SEARCH_ITEM_BY_NAME.replace("itemName", text), {
 //                 headers: {
 //                     "Content-Type": "application/json",
 //                     "Authorization": `Bearer ${userToken}`
 //                 },
 //             },)
 //             let response = await data.json()
 //             // console.log("data getAllBookOfUser", await data.json())
 //             if (response.data) {
 //                 setBookData(response.data)
 //             }
 // export const login = async (payload, guestId) => {
 //     let response = await instance
 //         .post(`${uri.login}`,payload)
 //         .catch(axiosErrorHandler);
 //         console.log("login test response",response)
 //         return response
 // };
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/InitialAvatar/CreateInitialAvatar.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createInitialsAvatar": (()=>createInitialsAvatar)
});
function createInitialsAvatar(name, options = {}) {
    // Default options
    const { size = 100, bgColor = "#cccccc", textColor = "#000000", shape = "circle" } = options;
    // Get initials from name
    const getInitials = (name)=>{
        if (!name || typeof name !== "string") return "";
        const words = name.trim().split(/\s+/).filter((word)=>word.length > 0);
        if (words.length === 0) return "";
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        return `${words[0].charAt(0)}${words[words.length - 1].charAt(0)}`.toUpperCase();
    };
    const initials = getInitials(name);
    const fontSize = size * 0.4;
    // Create SVG based on shape
    let svgContent;
    if (shape === "circle") {
        const radius = size / 2;
        svgContent = `
      <circle cx="${radius}" cy="${radius}" r="${radius}" fill="${bgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
    } else {
        svgContent = `
      <rect width="100%" height="100%" fill="${bgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
    }
    // Create full SVG
    const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" 
         width="${size}" 
         height="${size}" 
         viewBox="0 0 ${size} ${size}">
      ${svgContent}
    </svg>
  `;
    // Convert to data URL
    return `data:image/svg+xml,${encodeURIComponent(svg)}`;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/book-detail/AddRating.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AddRating)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/profile.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/InitialAvatar/CreateInitialAvatar.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
function AddRating({ bookState, getReviews }) {
    _s();
    const [comment, setComment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [rating, setRating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [hoverRating, setHoverRating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [loadingCommentBtn, setLoadingCommentBtn] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
    const reviewAdd = async (e)=>{
        if (!bookState?.createdBy?._id) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("User is not available to review");
            return;
        }
        if (comment.length < 1) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please provide a comment");
            return;
        }
        if (rating === 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Please provide a rating");
            return;
        }
        setLoadingCommentBtn(true);
        let payload = {
            userId: bookState?.createdBy?._id,
            comment: comment,
            rating: rating
        };
        try {
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addReviewForSeller"])(payload);
            if (response.status === 200) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Review Added Successfully");
                setComment("");
                setRating(0);
                getReviews(bookState);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(response.data.message || "Something went wrong");
            }
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("An error occurred while adding the review");
        } finally{
            setLoadingCommentBtn(false);
        }
    };
    // Create star rating component
    const renderStars = ()=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "mr-3",
                    children: "Rate Your Experience:"
                }, void 0, false, {
                    fileName: "[project]/app/book-detail/AddRating.js",
                    lineNumber: 63,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        1,
                        2,
                        3,
                        4,
                        5
                    ].map((star)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            className: "focus:outline-none",
                            onClick: ()=>setRating(star),
                            onMouseEnter: ()=>setHoverRating(star),
                            onMouseLeave: ()=>setHoverRating(0),
                            "aria-label": `Rate ${star} star${star !== 1 ? "s" : ""}`,
                            children: hoverRating >= star || !hoverRating && rating >= star ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdStar"], {
                                className: "text-yellow-400 transition-all duration-150",
                                size: 25
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/AddRating.js",
                                lineNumber: 76,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdStarOutline"], {
                                className: "text-gray-300 hover:text-yellow-300 transition-all duration-150",
                                size: 25
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/AddRating.js",
                                lineNumber: 81,
                                columnNumber: 17
                            }, this)
                        }, star, false, {
                            fileName: "[project]/app/book-detail/AddRating.js",
                            lineNumber: 66,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/app/book-detail/AddRating.js",
                    lineNumber: 64,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/book-detail/AddRating.js",
            lineNumber: 62,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                className: "md:text-[34px] font-bold md:text-3xl sm:text-[24px]",
                children: "Add Review"
            }, void 0, false, {
                fileName: "[project]/app/book-detail/AddRating.js",
                lineNumber: 98,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border rounded-lg p-3 border-[1.5px] border-[global_linear_gradiant] mt-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                className: "w-[50px] h-[50px] rounded-full",
                                src: userData?.profileImage || (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createInitialsAvatar"])(`${userData?.firstName}  ${userData?.lastName}`, {
                                    bgColor: "#3f51b5",
                                    textColor: "#ffffff"
                                }),
                                alt: "User profile"
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/AddRating.js",
                                lineNumber: 103,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "ml-2",
                                children: [
                                    userData?.firstName,
                                    ", ",
                                    userData?.lastName
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/book-detail/AddRating.js",
                                lineNumber: 114,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/book-detail/AddRating.js",
                        lineNumber: 102,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center my-4",
                        children: renderStars()
                    }, void 0, false, {
                        fileName: "[project]/app/book-detail/AddRating.js",
                        lineNumber: 120,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        placeholder: "Enter your review here...",
                        value: comment,
                        onChange: (e)=>setComment(e.target.value),
                        className: "w-full min-h-[120px] p-3 border-none rounded-md mt-4 focus:outline-none"
                    }, void 0, false, {
                        fileName: "[project]/app/book-detail/AddRating.js",
                        lineNumber: 122,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-end items-center mt-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "flex gap-2 items-center py-3 px-6 rounded-full text-white justify-center global_linear_gradient  transition-colors duration-200 disabled:opacity-60 disabled:cursor-not-allowed",
                            onClick: reviewAdd,
                            disabled: loadingCommentBtn,
                            children: loadingCommentBtn ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-5 h-5 animate-spin text-white mr-2",
                                        fill: "none",
                                        viewBox: "0 0 24 24",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                className: "opacity-25",
                                                cx: "12",
                                                cy: "12",
                                                r: "10",
                                                stroke: "currentColor",
                                                strokeWidth: "4"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/AddRating.js",
                                                lineNumber: 142,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                className: "opacity-75",
                                                fill: "currentColor",
                                                d: "M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/AddRating.js",
                                                lineNumber: 150,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/book-detail/AddRating.js",
                                        lineNumber: 137,
                                        columnNumber: 17
                                    }, this),
                                    "Submitting..."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/book-detail/AddRating.js",
                                lineNumber: 136,
                                columnNumber: 15
                            }, this) : "Submit Review"
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/AddRating.js",
                            lineNumber: 130,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/book-detail/AddRating.js",
                        lineNumber: 129,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/book-detail/AddRating.js",
                lineNumber: 101,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/book-detail/AddRating.js",
        lineNumber: 97,
        columnNumber: 5
    }, this);
}
_s(AddRating, "ooVltHpX/AVT2VV47SQRoZR/XNg=");
_c = AddRating;
var _c;
__turbopack_context__.k.register(_c, "AddRating");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/book-listing/Mapview.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loading-skeleton/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/slices/storeSlice.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
// const JAMAICA_CENTER = { lat: 17.9714, lng: -76.7931 };
const JAMAICA_ZOOM = 12;
const googleMapsApiKey = ("TURBOPACK compile-time value", "AIzaSyC3VuARMjQFSzXhddmwP7vqzlhGcNf7UPA");
const Mapview = ({ width, height, data, fullScreen = false, center, isSingleBookDetails, onExitFullScreen = ()=>{} })=>{
    _s();
    const userLocationData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "Mapview.useSelector[userLocationData]": (state)=>state.storeData.userLocationData
    }["Mapview.useSelector[userLocationData]"]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [zoom, setZoom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(JAMAICA_ZOOM);
    const [locationAllowed, setLocationAllowed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [mapCenter, setMapCenter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(userLocationData && typeof userLocationData.latitude === "number" && typeof userLocationData.longitude === "number" ? {
        lat: userLocationData.latitude,
        lng: userLocationData.longitude
    } : null);
    const [mapKey, setMapKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const mapRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const locations = Array.isArray(data) ? data : [
        data
    ];
    const toShoInlocation = (text)=>{
        if (!text) return "";
        if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].BookItem) {
            return "Book";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].EventItem) {
            return "Event";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].ExtracurricularActivityItem) {
            return "Activity";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].ScholarshipAwardItem) {
            return "Award";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].SchoolItem) {
            return "School";
        } else if (text.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].TutorItem) {
            return "Tutor";
        } else {
            return "";
        }
    };
    // Initial load: get current location and set in redux and local state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Mapview.useEffect": ()=>{
            let isMounted = true;
            const fetchAndSetLocation = {
                "Mapview.useEffect.fetchAndSetLocation": async ()=>{
                    try {
                        // If we have valid coordinates in userLocationData, use them
                        if (userLocationData && typeof userLocationData.latitude === "number" && typeof userLocationData.longitude === "number") {
                            if (isMounted) {
                                setMapCenter({
                                    lat: userLocationData.latitude,
                                    lng: userLocationData.longitude
                                });
                                setZoom(12);
                                setLocationAllowed(true);
                                setLoading(false);
                            }
                            return;
                        }
                        // Only fetch location if we don't have coordinates and it's not parish selection
                        if (!userLocationData?.isParishSelection) {
                            const addressData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCurrentLocationAndAddress"])("getfullAddress");
                            const parsedAddress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseGeocodeResponse"])(addressData);
                            if (parsedAddress && isMounted) {
                                // Save in redux in the required format
                                const locationData = {
                                    locality: parsedAddress.locality || "",
                                    latitude: parsedAddress.latitude || "",
                                    longitude: parsedAddress.longitude || "",
                                    currentLocation: true
                                };
                                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateUserLocationData"])(locationData));
                                // Set map center for the map and marker
                                setMapCenter({
                                    lat: parsedAddress.latitude,
                                    lng: parsedAddress.longitude
                                });
                                setZoom(12);
                                setLocationAllowed(true);
                            } else if (isMounted) {
                                setLocationAllowed(false);
                            }
                        } else {
                            // For parish selection, we need to set a default center
                            // Use Jamaica center as fallback
                            setMapCenter({
                                lat: 17.9714,
                                lng: -76.7931
                            });
                            setZoom(10);
                            setLocationAllowed(false);
                        }
                    } catch (err) {
                        // fallback to Jamaica center
                        if (isMounted) {
                            setMapCenter({
                                lat: 17.9714,
                                lng: -76.7931
                            });
                            setZoom(10);
                            setLocationAllowed(false);
                        }
                    } finally{
                        if (isMounted) setLoading(false);
                    }
                }
            }["Mapview.useEffect.fetchAndSetLocation"];
            fetchAndSetLocation();
            return ({
                "Mapview.useEffect": ()=>{
                    isMounted = false;
                }
            })["Mapview.useEffect"];
        }
    }["Mapview.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Mapview.useEffect": ()=>{
            if (userLocationData && typeof userLocationData.latitude === "number" && typeof userLocationData.longitude === "number") {
                setMapCenter({
                    lat: userLocationData.latitude,
                    lng: userLocationData.longitude
                });
                setLocationAllowed(true);
                setMapKey({
                    "Mapview.useEffect": (prev)=>prev + 1
                }["Mapview.useEffect"]);
            } else if (userLocationData?.isParishSelection) {
                // For parish selection, use Jamaica center
                setMapCenter({
                    lat: 17.9714,
                    lng: -76.7931
                });
                setZoom(10);
                setLocationAllowed(false);
                setMapKey({
                    "Mapview.useEffect": (prev)=>prev + 1
                }["Mapview.useEffect"]);
            } else {
                setLocationAllowed(false);
            }
        }
    }["Mapview.useEffect"], [
        userLocationData
    ]);
    // Handler to update mapCenter when the user moves the map
    const handleCenterChanged = ()=>{
        if (mapRef.current) {
            const center = mapRef.current.getCenter();
            if (center) {
                setMapCenter({
                    lat: center.lat(),
                    lng: center.lng()
                });
            }
        }
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "loader-container",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loading$2d$skeleton$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                height: height
            }, void 0, false, {
                fileName: "[project]/app/components/book-listing/Mapview.js",
                lineNumber: 198,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/components/book-listing/Mapview.js",
            lineNumber: 197,
            columnNumber: 7
        }, this);
    }
    const mapStyle = fullScreen ? {
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        zIndex: 9999,
        borderRadius: 0
    } : {
        width,
        height,
        borderRadius: "15px"
    };
    console.log("===", isSingleBookDetails, center, mapCenter);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["APIProvider"], {
        apiKey: googleMapsApiKey,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Map"], {
                defaultCenter: isSingleBookDetails ? center : mapCenter,
                defaultZoom: zoom,
                // Remove the center prop to allow the map to be movable
                style: mapStyle,
                mapId: "DEMO_MAP_ID",
                gestureHandling: "greedy",
                onLoad: (map)=>{
                    mapRef.current = map;
                },
                onCenterChanged: handleCenterChanged,
                options: {
                    draggable: true,
                    scrollwheel: true,
                    clickableIcons: true,
                    zoomControl: true,
                    disableDoubleClickZoom: false
                },
                disableDefaultUI: false,
                children: [
                    !isSingleBookDetails && locationAllowed && mapCenter && userLocationData?.currentLocation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AdvancedMarker"], {
                        position: mapCenter,
                        title: "Your Location",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "custom-marker p-2 bg-blue-500 text-white rounded-full border-2 border-white shadow-lg",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                role: "img",
                                "aria-label": "You",
                                children: "📍"
                            }, void 0, false, {
                                fileName: "[project]/app/components/book-listing/Mapview.js",
                                lineNumber: 250,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/components/book-listing/Mapview.js",
                            lineNumber: 249,
                            columnNumber: 13
                        }, this)
                    }, "user-location", false, {
                        fileName: "[project]/app/components/book-listing/Mapview.js",
                        lineNumber: 244,
                        columnNumber: 11
                    }, this),
                    locations.map((list, index)=>{
                        const coords = list?.address?.geometry?.location?.coordinates;
                        if (!coords || coords.length < 2) return null;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$vis$2e$gl$2f$react$2d$google$2d$maps$2f$dist$2f$index$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AdvancedMarker"], {
                            position: {
                                lat: coords[1],
                                lng: coords[0]
                            },
                            title: list?.createdByDoc?.firstName,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "custom-marker relative z-10 hover:z-[9999] p-3 bg-white rounded shadow-lg",
                                onClick: ()=>router.push(`/book-detail?id=${list._id}`),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-fit mx-auto",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            className: "border aspect-[3/4] mx-auto w-[50px]",
                                            src: list.images[0],
                                            alt: list.title
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/book-listing/Mapview.js",
                                            lineNumber: 275,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 274,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-md mt-2",
                                        children: toShoInlocation(list)
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 281,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "extra-content text-md mt-2 max-w-[100px]",
                                        children: list.title
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 282,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-md mt-2",
                                        children: [
                                            "J$",
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatWithCommas"])(list?.price)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 285,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "triangle absolute bottom-[-15px] left-[30%]"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/book-listing/Mapview.js",
                                        lineNumber: 288,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/book-listing/Mapview.js",
                                lineNumber: 270,
                                columnNumber: 15
                            }, this)
                        }, index, false, {
                            fileName: "[project]/app/components/book-listing/Mapview.js",
                            lineNumber: 262,
                            columnNumber: 13
                        }, this);
                    })
                ]
            }, mapKey, true, {
                fileName: "[project]/app/components/book-listing/Mapview.js",
                lineNumber: 222,
                columnNumber: 7
            }, this),
            fullScreen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                className: "fixed top-[10px] right-[10px] z-[10000] h-[40px] w-[40px] border-0 rounded-none font-black text-white flex items-center justify-center shadow",
                style: {
                    background: "linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%)"
                },
                onClick: onExitFullScreen,
                children: "✕"
            }, void 0, false, {
                fileName: "[project]/app/components/book-listing/Mapview.js",
                lineNumber: 295,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/book-listing/Mapview.js",
        lineNumber: 221,
        columnNumber: 5
    }, this);
};
_s(Mapview, "Fc93RnWdO9VTWiZKw+r0KQP11KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"]
    ];
});
_c = Mapview;
const __TURBOPACK__default__export__ = Mapview;
var _c;
__turbopack_context__.k.register(_c, "Mapview");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/book-detail/ItemRespectiveDetails.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ItemRespectiveDetails)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
;
;
;
;
function ItemRespectiveDetails({ bookState }) {
    const attachXYZ = (string)=>{
        if (string?.includes("http")) {
            return string;
        } else {
            return "https://" + string;
        }
    };
    if (bookState.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].BookItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px] truncate",
                    children: [
                        "Author:",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.authors?.map((a)=>a).join(", ")
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 19,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 17,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px] truncate",
                    children: [
                        "ISBN: ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.isbn_number
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 24,
                            columnNumber: 17
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 23,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-2 text-[16px] md:text-[21px] leading-[27px] truncate",
                    children: [
                        "Condition: ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.condition
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 27,
                            columnNumber: 22
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 26,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
            lineNumber: 16,
            columnNumber: 7
        }, this);
    } else if (bookState.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].TutorItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].TutorItem.experience
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.experience
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 36,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 34,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].TutorItem.highestQualification
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.highestQualification
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 40,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 38,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs  text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].TutorItem.targetClasses
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.targetClasses
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 44,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 42,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
            lineNumber: 33,
            columnNumber: 7
        }, this);
    } else if (bookState.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].EventItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].EventItem.eventStartDate
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(bookState?.eventStartDate).format("DD-MMM-YYYY HH:MM")
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 57,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 55,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].EventItem.eventEndDate
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(bookState?.eventEndDate).format("DD-MMM-YYYY HH:MM")
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 63,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 61,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs  text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].EventItem.eventMode
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.eventMode
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 69,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 67,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
            lineNumber: 54,
            columnNumber: 7
        }, this);
    } else if (bookState.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].SchoolItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].SchoolItem.classesOffered
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.classesOffered
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 82,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 80,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].SchoolItem.schoolType
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.schoolType
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 86,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 84,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
            lineNumber: 79,
            columnNumber: 7
        }, this);
    } else if (bookState.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].ScholarshipAwardItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ScholarshipAwardItem.eligibilityCriteria
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.eligibilityCriteria
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 99,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 97,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs  text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ScholarshipAwardItem.scholarshipType
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.scholarshipType
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 103,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 101,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
            lineNumber: 96,
            columnNumber: 7
        }, this);
    } else if (bookState.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].ExtracurricularActivityItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ExtracurricularActivityItem.activityType
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.activityType
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 116,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 114,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ExtracurricularActivityItem.frequency
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.frequency
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 120,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 118,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs text-[16px] md:text-[21px] leading-[27px]",
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ExtracurricularActivityItem.targetStudents
                        ],
                        ":",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: bookState?.targetStudents
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                            lineNumber: 124,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
                    lineNumber: 122,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/book-detail/ItemRespectiveDetails.js",
            lineNumber: 113,
            columnNumber: 7
        }, this);
    }
}
_c = ItemRespectiveDetails;
var _c;
__turbopack_context__.k.register(_c, "ItemRespectiveDetails");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/book-detail/bookDetailComponent.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$BookCard$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/BookCard.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$testbook$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$landing$2f$testbook$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/landing/testbook.png.mjs { IMAGE => "[project]/public/landing/testbook.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/lu/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$book$2d$detail$2f$CircleRating$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/book-detail/CircleRating.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$book$2d$detail$2f$AddRating$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/book-detail/AddRating.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi2$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/hi2/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/slices/storeSlice.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/profile.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$rx$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/rx/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loader$2d$spinner$2f$dist$2f$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loader-spinner/dist/module.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$book$2d$listing$2f$Mapview$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/book-listing/Mapview.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$book$2d$detail$2f$ItemRespectiveDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/book-detail/ItemRespectiveDetails.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/InitialAvatar/CreateInitialAvatar.js [app-client] (ecmascript)");
;
;
var _s = __turbopack_context__.k.signature();
"use client";
"use strict";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Sample data structure for the book
const sampleBook = {
    description: "This is a fascinating book about the history of programming languages and their evolution over time. The author explores how different languages shaped the way we think about software development and problem-solving.",
    averageRating: 4.5,
    totalReviews: 24,
    ratings: {
        5: 12,
        4: 8,
        3: 3,
        2: 1,
        1: 0
    },
    reviews: [
        {
            author: "Alex Johnson",
            image: "",
            date: "July 2, 2020 03:29 PM",
            rating: 5,
            comment: "Absolutely loved this book! It provided great insights into how programming languages evolved."
        },
        {
            author: "Sam Wilson",
            image: "",
            date: "July 2, 2020 03:29 PM",
            rating: 4,
            comment: "Well-researched and engaging. Could have included more examples though."
        }
    ],
    location: {
        address: "123 Book Street, Knowledge City, 54321",
        distance: "2.5 miles"
    }
};
// import testBook from "@/public/landing/testbook.png"
const CustomSlider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/app/components/common/Slider.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/app/components/common/Slider.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    }
});
_c = CustomSlider;
function BookDetailComponent() {
    _s();
    // const router = useRouter();
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const query = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const [bookState, setbookState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    console.log(bookState, "bookState");
    const [allBookSeller, setallBookSeller] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("Descriptions");
    const [address, setaddress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [seeMore, setseeMore] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const contentRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const smallcontentRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isSeller, setisSeller] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoading, setisLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [allReviews, setallReviews] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isImageLoaded, setisImageLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [height, setHeight] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("50px");
    const [ratingData, setratingData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        5: 0,
        4: 0,
        3: 0,
        2: 0,
        1: 0
    });
    const [currentImageIndex, setCurrentImageIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // let isSeller=""
    console.log("query", query.get("id"));
    let isLoggedIn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])();
    const settings = {
        dots: false,
        dotsClass: "custom_inside_dots slick-dots !bottom-4.5 md:!bottom-6",
        infinite: true,
        speed: 500,
        slidesToShow: 4,
        slidesToScroll: 4,
        arrows: false,
        adaptiveHeight: true
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookDetailComponent.useEffect": ()=>{
            if (seeMore && contentRef.current) {
                // console.log()
                setHeight(`${contentRef.current.scrollHeight}px`);
            } else {
                setHeight("50px");
            }
        }
    }["BookDetailComponent.useEffect"], [
        seeMore
    ]);
    console.log(height, "height");
    const userId = (()=>{
        try {
            const userData = JSON.parse(localStorage.getItem("userData"));
            return userData?._id || null;
        } catch (e) {
            return null;
        }
    })();
    const fetchBookDetails = async (id)=>{
        try {
            setisLoading(true);
            if (!id) {
                return;
            }
            let userToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])();
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBooksById"])(id, userId);
            // let data = await fetch(`${USER_ROUTES.LIST_ITEM}/${id}`, {
            //     headers: {
            //         "Content-Type": "application/json",
            //         "Authorization": `Bearer ${userToken}`
            //     },
            // },)
            // let response = await data.json()
            console.log("data fetchBookDetails", response);
            if (response?.status == 200) {
                setbookState(response?.data);
                let userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
                if (userData?._id == response?.data?.createdBy._id) {
                    setisSeller(true);
                }
            }
            setisLoading(false);
        } catch (err) {
            console.log("fetchBookDetails err", err);
        }
    };
    console.log(bookState, "bookState");
    const getItemsBySellerFunc = async (text)=>{
        try {
            // let userToken = getToken()
            // let data = await fetch(`${USER_ROUTES.LIST_ITEM}/user/${bookState.createdBy._id}`, {
            //     headers: {
            //         "Content-Type": "application/json",
            //         "Authorization": `Bearer ${userToken}`
            //     },
            // },)
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItemBySeller"])(bookState.createdBy._id);
            console.log("response in seller", response);
            // let response = await data.json()
            // console.log("data getAllBookOfUser", await data.json())
            if (response.data.length) {
                // setBookData(response.data)
                setallBookSeller(response.data.filter((item)=>item._id != bookState?._id));
            }
        } catch (err) {
            console.log("err", err);
        }
    // setBookData()
    };
    console.log("allBookSeller", allBookSeller);
    console.log(bookState, "bookState");
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookDetailComponent.useEffect": ()=>{
            fetchBookDetails(query.get("id"));
        }
    }["BookDetailComponent.useEffect"], [
        query.get("id")
    ]);
    let actionPayload = {};
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookDetailComponent.useEffect": ()=>{
            if (bookState.createdBy) {
                getItemsBySellerFunc();
                getReviews(bookState);
                actionPayload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecideItemAction"])(bookState.status);
            }
        }
    }["BookDetailComponent.useEffect"], [
        bookState
    ]);
    let ratings = "1";
    const total = Object.values(ratings).reduce((acc, val)=>acc + val, 0);
    const StarReviewBreakdown = ({ ratings })=>{
        // ratings is an object like {5: 1000, 4: 800, 3: 200, 2: 50, 1: 10}
        // Calculate total reviews
        const total = Object.values(ratings).reduce((acc, val)=>acc + val, 0);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: " mx-auto",
            children: Object.entries(ratings).sort((a, b)=>b[0] - a[0]) // Sort descending by star rating
            .map(([star, count])=>{
                const percentage = count / total * 100;
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-3 mb-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "w-5 font-semibold flex",
                            children: [
                                star,
                                " ",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-yellow",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdStar"], {
                                        fill: "#FFD700",
                                        size: 24
                                    }, void 0, false, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 255,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 254,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 252,
                            columnNumber: 17
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 bg-gray-300 rounded-full h-3 overflow-hidden",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-yellow-400 h-3",
                                style: {
                                    width: `${percentage}%`,
                                    backgroundColor: "#292929"
                                }
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 261,
                                columnNumber: 19
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 260,
                            columnNumber: 17
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "w-12 text-right font-medium text-gray-700",
                            children: count >= 1000 ? (count / 1000).toFixed(1) + "k" : count
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 270,
                            columnNumber: 17
                        }, this)
                    ]
                }, star, true, {
                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                    lineNumber: 250,
                    columnNumber: 15
                }, this);
            })
        }, void 0, false, {
            fileName: "[project]/app/book-detail/bookDetailComponent.js",
            lineNumber: 244,
            columnNumber: 7
        }, this);
    };
    // const ratingData = {
    //     5: 1000,
    //     4: 800,
    //     3: 200,
    //     2: 50,
    //     1: 10,
    // };
    // let allRatingData = [
    //     { name: "Kashish Akansha", disLikeCount: 5, likeCount: 10, date: "2025-05-16T11:25:26.961Z", rating: 5, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
    //     { name: "Tony Stark", disLikeCount: 30, likeCount: 1, date: "2025-05-08T11:29:26.961Z", rating: 4, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
    //     { name: "Huge Jakcman", disLikeCount: 8, likeCount: 56, date: "2025-05-17T11:29:45.961Z", rating: 4, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
    //     { name: "Richard Branson", disLikeCount: 45, likeCount: 100, date: "2025-05-16T11:54:26.961Z", rating: 3, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
    //     { name: "Captain", disLikeCount: 2, likeCount: 11, date: "2025-05-09T11:29:26.961Z", rating: 5, review: "Smooth buying experience on ReBookIt! Book was in great condition." },
    //     { name: "Kashish Akansha", disLikeCount: 3, likeCount: 5, date: "2025-05-16T11:29:26.961Z", rating: 1, review: "Smooth buying experience on ReBookIt! Book was in great condition." }
    // ]
    const getLocationFromCoordinates = async (cordinates)=>{
        console.log("cordinates check ", cordinates);
        // return
        try {
            if (!cordinates?.length) {
                return;
            }
            // let [lat, lon] = cordinates
            const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${cordinates[0]}&lon=${cordinates[1]}`);
            const data = await response.json();
            console.log(data, "location search"); // Full human-readable address
            setaddress(data?.display_name || "NA");
            return data?.display_name || "NA";
        } catch (err) {
            console.error("Error fetching location:", err);
            setaddress(bookState.address);
        }
    };
    // console.log("address", address);
    console.log("bookState?.geometry?.location?.coordinates", bookState?.address?.geometry?.location?.coordinates);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookDetailComponent.useEffect": ()=>{
            if (bookState?.address?.geometry?.location?.coordinates?.length) getLocationFromCoordinates(bookState?.address?.geometry?.location?.coordinates);
        }
    }["BookDetailComponent.useEffect"], [
        bookState?.address?.geometry?.location?.coordinates
    ]);
    // getLocationFromCoordinates("33.425125", "-94.04768820000001")
    console.log("bookState.description", bookState.description?.length);
    console.log("see More", seeMore);
    // Navigation of chat button
    const chatNavigationHandler = (e, itemId)=>{
        e.stopPropagation();
        const profileIndex = 3;
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateProfileComponentIndex"])(profileIndex));
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateItemId"])(itemId));
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            router.push(`/profile/messages?itemId=${itemId}`);
        } else {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RedirectToLoginIfNot"])(`${window.location.pathname}${window.location.search}`, router);
        }
    };
    console.log("window.location.", window.location);
    console.log("bookState.authors[0]", bookState?.authors?.length && bookState?.authors[0]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const bookTheItemMark = async (id)=>{
        setLoading(true);
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            let url = `${window.location.href}`;
            url = url.replace(window.location.origin, "");
            router.push(`/login?redirect=${url}`);
        }
        if (!isSeller) {
            try {
                let bookMarkResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bookMarkItem"])({
                    itemId: bookState._id || id,
                    itemId: bookState._id || id
                });
                // console.log("bookMarkResponse", bookMarkResponse)
                if (bookMarkResponse.data?._id) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Item added");
                }
                setbookState({
                    ...bookState,
                    isBookmarked: true,
                    bookmarkDoc: {
                        _id: bookMarkResponse.data?._id
                    }
                });
                setLoading(false);
            } catch (err) {
                setLoading(false);
                console.log("err bookTheItemMark", err);
            }
        } else {
            if (bookState.status == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemListStatusEnum"].MARKED_AS_SOLD) {
                return;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RedirectToLoginIfNot"])("/become-seller", router);
            let payloadToSet = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editItemPayload"])(bookState);
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editListingPrefillData"])(payloadToSet));
        }
    };
    function Spinner() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin",
            style: {
                verticalAlign: "middle"
            },
            "aria-label": "Loading"
        }, void 0, false, {
            fileName: "[project]/app/book-detail/bookDetailComponent.js",
            lineNumber: 400,
            columnNumber: 7
        }, this);
    }
    const deleteBookMarkItem = async (id)=>{
        setLoading(true);
        let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delete_bookMarkItem"])(id);
        if (response.status == 200) {
            setLoading(false);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Removed Wishlist Item");
            setbookState({
                ...bookState,
                isBookmarked: false,
                bookmarkDoc: {}
            });
        } else {
            setLoading(false);
        }
    };
    const getReviews = async (bookState)=>{
        try {
            let userReviewsData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getReviewsOfUser"])(bookState?.createdBy?._id);
            console.log("userReviewsData", userReviewsData);
            if (userReviewsData.status == 200) {
                setallReviews(userReviewsData.data);
                if (userReviewsData.data.reviews) {
                    let ratingDataObj = {
                        5: 0,
                        4: 0,
                        3: 0,
                        2: 0,
                        1: 0
                    };
                    if (userReviewsData.data.reviews.length) {
                        userReviewsData.data.reviews.map((item)=>{
                            if (item.rating == 5) {
                                ratingDataObj[5]++;
                            } else if (item.rating == 4) {
                                ratingDataObj[4]++;
                            } else if (item.rating == 3) {
                                ratingDataObj[3]++;
                            } else if (item.rating == 2) {
                                ratingDataObj[2]++;
                            } else {
                                ratingDataObj[1]++;
                            }
                        });
                        console.log(ratingDataObj, "ratingDataObj");
                        setratingData(ratingDataObj);
                    }
                }
            }
        } catch (err) {
            console.log("getReviews err", err);
        }
    };
    console.log("allReviews", allReviews);
    console.log("contentRef.current", contentRef?.current?.scrollHeight);
    const chatFunc = (e)=>{
        if (!isSeller) {
            chatNavigationHandler(e, query.get("id"));
        } else {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecideItemAction"])(bookState.status).boost) {
                let docElement = document.getElementById("myModal").classList.remove("hidden");
            }
        }
    };
    // const bookMarkfunc = () => {
    //     if (!isSeller) {
    //         toast.success("Work in progress")
    //     } else {
    //         let payloadToSet = {
    //             currentStep: 3,
    //             completedStep: 2,
    //             category: { _id: bookState.categoryId._id, name:bookStatecategoryId.name|| "" },
    //             subCategory
    //                 : { _id: bookState.subCategoryId._id||"", name:bookState.subCategoryId.name ||"" },
    //             listData: {
    //                 tags: bookState.tags||[],
    //                 ISBN: bookState.isbn_number||"",
    //                 bookName: bookState.itemName||"",
    //                 desciption: bookState.description||"",
    //                 bookAuthor: bookState.author||"",
    //                 price: bookState.price||"",
    //                 bookCondition: "",
    //                 quantity: 0,
    //                 address: bookState.address||"",
    //                 locationCoordinates: {
    //                     lat: bookState.location.coordinates[0], lng:bookState.location.coordinates[1]
    //                 },
    //                 OTP: "",
    //                 isVerified: false,
    //                 bookImages: {
    //                     cover: bookState.coverImage||"",
    //                     front: bookState.frontImage||"",
    //                     middle: middleImage.middleImage|| "",
    //                     spine: bookState.spineImage||"",
    //                 }
    //             }
    //         }
    //         // dispatch(updateUserListingData(payloadToSet))
    //     }
    // }
    //    const bookTheItemMark = async (id) => {
    //         try {
    //             let bookMarkResponse = await bookMarkItem({ itemId: id })
    //             console.log("bookMarkResponse", bookMarkResponse)
    //             if (bookMarkResponse.data?._id) {
    //                 toast.success("Item added")
    //             }
    //         } catch (err) {
    //             console.log("err bookTheItemMark", err)
    //         }
    //     }
    const attachXYZ = (string)=>{
        if (string?.includes("http")) {
            return string;
        } else {
            return "https://" + string;
        }
    };
    const btnTextBoostChat = ()=>{
        if (isSeller) {
            if (bookState.status == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemListStatusEnum"].ACCEPTED) {
                return {
                    text: "Boost Item",
                    enable: true
                };
            } else {
                return {
                    text: "Boost Item",
                    enable: false
                };
            }
        } else {
            return {
                text: "Chat",
                enable: true
            };
        }
    };
    let starRate = Array.from({
        length: Math.ceil(allReviews?.averageRating)
    }).map((item)=>"hii");
    console.log("array", Array.from({
        length: Math.ceil(allReviews?.averageRating)
    }).map((item)=>"hii"));
    console.log("center check", bookState?.address?.geometry?.location?.coordinates);
    const boostItemFunc = async (id)=>{
        try {
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["boostItem"])(id);
            console.log(response);
            if (response.status == 200) {
                document.getElementById("myModal").classList.add("hidden");
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Boost Initiated Successfull`);
            }
        } catch (err) {}
    };
    console.log("setisLoading", isLoading);
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100vh"
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loader$2d$spinner$2f$dist$2f$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Circles"], {
                height: "80",
                width: "80",
                color: "#4fa94d",
                ariaLabel: "circles-loading",
                wrapperStyle: {},
                wrapperClass: "",
                visible: true
            }, void 0, false, {
                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                lineNumber: 582,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/book-detail/bookDetailComponent.js",
            lineNumber: 574,
            columnNumber: 7
        }, this);
    } else {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bookDetailContainer max-w-[1500px] mx-auto mt-[10px] md:mt-[50px] lg:mt-[90px] md-px-4 sm:px-12 md:px-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                    className: "w-full mb-12 flex flex-col md:flex-row gap-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-full  md:w-[35%] flex flex-col 0 gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative w-full bg-[#FFFBF6] border-[1.42px] border-[#F1F1F1] overflow-hidden",
                                    children: bookState?.images?.[currentImageIndex] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: bookState.images[currentImageIndex] || "/images/book_1.jpg",
                                        alt: `Book view ${currentImageIndex + 1}`,
                                        onLoad: ()=>setisImageLoaded(true),
                                        className: `
        w-full 
        h-auto 
        max-h-[300px]       /* constrain height if you like */
        mx-auto 
        object-contain      /* show entire image */
        transition-opacity 
        duration-300 
        ${isImageLoaded ? "opacity-100" : "opacity-0"}
      `
                                    }, void 0, false, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 602,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 600,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex gap-2 overflow-x-auto no_scrollbar py-2",
                                    children: bookState?.images?.map((src, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setCurrentImageIndex(idx),
                                            className: `flex-shrink-0 w-[22%] sm:w-[18%] p-1 rounded bg-[#FFFBF6] ${idx === currentImageIndex ? "" : "border-[#F1F1F1] border"} transition`,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                src: src || "/images/book_1.jpg",
                                                alt: `Thumbnail ${idx + 1}`,
                                                onLoad: ()=>setisImageLoaded(true),
                                                className: "w-full h-auto object-cover",
                                                style: {
                                                    aspectRatio: "3/4"
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 631,
                                                columnNumber: 19
                                            }, this)
                                        }, idx, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 625,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 623,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 598,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-full md:w-[60%] flex flex-col md:gap-4 pl-0 md:pl-[3%]",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-[#212A30] text-[16px] font-medium md:text-[19px]",
                                            children: [
                                                bookState?.categoryId?.name || "Sample Category",
                                                bookState?.subCategoryId?.name && `/${bookState?.subCategoryId?.name}`,
                                                bookState?.subSubCategoryId?.name && `/${bookState?.subSubCategoryId?.name}`,
                                                bookState?.subSubSubCategoryId?.name && `/${bookState?.subSubSubCategoryId?.name}`
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 647,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-[#212A30] text-2xl md:text-[30px] font-bold mb-2 md:mb-[20px]",
                                            children: bookState?.title
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 656,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-wrap justify-between items-center gap-2 mb-2 md:mb-[20px]",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: "text-white text-[20px] md:text-[33px] global_linear_gradient rounded-full py-1.5 px-5",
                                                    children: [
                                                        "J$",
                                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatWithCommas"])(bookState?.price)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 661,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: " md:text-[21px] text-lg leading-[27px]",
                                                    children: [
                                                        "Date:",
                                                        " ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "font-medium",
                                                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(bookState?.createdAt).format("DD-MM-YYYY")
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                            lineNumber: 666,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 664,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 660,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 646,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                                    className: "border-t border-gray-300 hidden md:block"
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 673,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-lg md:text-[21px] leading-[27px]",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "font-medium",
                                                        children: `${bookState?.createdBy?.firstName} ${bookState?.createdBy?.lastName}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                        lineNumber: 679,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 678,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-1",
                                                    children: [
                                                        allReviews?.averageRating && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "bg-[#14884C] text-white text-[8px] md:text-[12px] py-[1px] px-2 rounded",
                                                            children: allReviews?.averageRating
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                            lineNumber: 685,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex",
                                                            children: starRate.map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoMdStar"], {
                                                                    size: 16
                                                                }, i, false, {
                                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                    lineNumber: 691,
                                                                    columnNumber: 23
                                                                }, this))
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                            lineNumber: 689,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 683,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 677,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$book$2d$detail$2f$ItemRespectiveDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            bookState: bookState
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 697,
                                            columnNumber: 15
                                        }, this),
                                        bookState?.website && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "mb-4 text-md md:text-[21px] leading-[27px]",
                                            children: [
                                                "Website:",
                                                " ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium text-blue-500",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                        href: attachXYZ(bookState?.website),
                                                        target: "_blank",
                                                        children: bookState?.website
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                        lineNumber: 705,
                                                        columnNumber: 61
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 705,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 703,
                                            columnNumber: 38
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: " text-md md:text-[21px] leading-[27px]",
                                            children: [
                                                "Location:",
                                                " ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: bookState?.address?.formatted_address
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 709,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 707,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 676,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-wrap gap-3 mt-5",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            disabled: !btnTextBoostChat().enable,
                                            onClick: chatFunc,
                                            className: `flex gap-2 items-center py-2 px-4 rounded-full text-lg md:px-20 md:text-[20px] lg:text-[24px] ${btnTextBoostChat().enable ? "global_linear_gradient text-white cursor-pointer" : "bg-[gray] text-[white] border-[2px] cursor-not-allowed opacity-50 pointer-events-none"}`,
                                            children: [
                                                !isSeller && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi2$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HiOutlineChatBubbleLeftRight"], {}, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 746,
                                                    columnNumber: 31
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: btnTextBoostChat().text
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 747,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 738,
                                            columnNumber: 15
                                        }, this),
                                        isSeller ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: bookTheItemMark,
                                            className: "flex gap-2 items-center py-2 px-4 md:px-15 rounded-full text-lg md:text-[20px] lg:text-[24px] bg-white border-2 border-[#211F54] text-[#211F54]",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Edit"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 754,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 750,
                                            columnNumber: 17
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>bookState?.isBookmarked ? deleteBookMarkItem(bookState?.bookmarkDoc?._id) : bookTheItemMark(),
                                            className: `flex gap-2 items-center py-2 px-4 md:px-15 rounded-full text-lg md:text-[20px] lg:text-[24px] border-2 border-[#211F54] ${bookState?.isBookmarked ? "bg-[#211F54] text-white" : "bg-white text-[#211F54]"}`,
                                            children: [
                                                loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "inline-block w-5 h-5 border-2 border-blue-500 border-t-white rounded-full animate-spin",
                                                            style: {
                                                                verticalAlign: "middle"
                                                            },
                                                            "aria-label": "Loading"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                            lineNumber: 770,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "ml-2 "
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                            lineNumber: 775,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaHeart"], {
                                                        size: 20
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                        lineNumber: 784,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "Wishlist"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 788,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 757,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 737,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 644,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                    lineNumber: 596,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "tabbed-interface",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "tab-navigation flex overflow-x-auto whitespace-nowrap border-b border-[#DBDBDB] bg-[#FAFAFA]",
                            children: [
                                "Descriptions",
                                "Reviews",
                                "Location"
                            ].map((tab)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: `tab-button flex-shrink-0 px-4 py-2 font-medium text-sm md:text-base relative ${activeTab === tab ? "text-[#3C3C3C] border-b-2 border-[#3C3C3C]" : "text-gray-500 hover:text-gray-700"}`,
                                    onClick: ()=>{
                                        setActiveTab(tab);
                                        document.getElementById(`${tab.toLowerCase()}-content`).scrollIntoView({
                                            behavior: "smooth",
                                            block: "start"
                                        });
                                    },
                                    children: [
                                        tab,
                                        activeTab === tab && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute bottom-0 left-0 right-0 h-0.5 bg-[#3C3C3C]"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 814,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, tab, true, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 799,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 797,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "tab-content",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "description-content p-4 md:p-[24px]",
                                    id: "descriptions-content",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-gray-700",
                                        children: bookState?.description1 || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: bookState.description
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 832,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 830,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 828,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 823,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                                    className: "my-2 md:my-[20px] border-t border-gray-300"
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 838,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "location-content px-4 md:px-[24px] py-4 md:py-[24px]",
                                    id: "location-content",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-xl md:text-[40px] font-bold mb-2",
                                            children: "Seller's Location"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 849,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$book$2d$listing$2f$Mapview$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            width: "100%",
                                            height: "300px",
                                            data: bookState,
                                            isSingleBookDetails: true,
                                            center: {
                                                lat: bookState?.address?.geometry?.location?.coordinates?.[1],
                                                lng: bookState?.address?.geometry?.location?.coordinates?.[0]
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 852,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 844,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                                    className: "my-5 md:my-[40px] border-t border-gray-300"
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 864,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "reviews-content px-4 md:px-[24px]",
                                    id: "reviews-content",
                                    children: [
                                        allReviews?._id && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "text-2xl md:text-[45px] font-bold",
                                                    children: "Seller Reviews"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 877,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-4 grid grid-cols-1 md:grid-cols-10 gap-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "col-span-1 md:col-span-4 flex items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$book$2d$detail$2f$CircleRating$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    rating: allReviews.averageRating || 0,
                                                                    maxRating: 5,
                                                                    size: 80
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                    lineNumber: 882,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "ml-3",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex gap-1",
                                                                            children: Array.from({
                                                                                length: 5
                                                                            }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdStar"], {
                                                                                    fill: "#FFD700",
                                                                                    size: 20
                                                                                }, i, false, {
                                                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                                    lineNumber: 890,
                                                                                    columnNumber: 29
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                            lineNumber: 888,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "text-sm text-gray-600",
                                                                            children: [
                                                                                "From ",
                                                                                allReviews?.reviews?.length || 0,
                                                                                " Reviews"
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                            lineNumber: 893,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                    lineNumber: 887,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                            lineNumber: 881,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "col-span-1 md:col-span-6",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StarReviewBreakdown, {
                                                                ratings: ratingData
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                lineNumber: 900,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                            lineNumber: 899,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 880,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 876,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                            className: "mt-6 space-y-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "text-xl md:text-[34px] font-bold",
                                                    children: "Review Lists"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 907,
                                                    columnNumber: 17
                                                }, this),
                                                allReviews?._id ? allReviews.reviews.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "individual_review",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex mb-2",
                                                                children: Array.from({
                                                                    length: item.rating
                                                                }).map((_, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdStar"], {
                                                                        fill: "#FFD700",
                                                                        size: 18
                                                                    }, idx, false, {
                                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                        lineNumber: 915,
                                                                        columnNumber: 27
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                lineNumber: 913,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "font-semibold text-base mb-1.5",
                                                                children: [
                                                                    '"',
                                                                    item.comment,
                                                                    '"'
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                lineNumber: 918,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-500 mb-2",
                                                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(item.createdAt).format("MMMM D, YYYY hh:mm a")
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                lineNumber: 921,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-between items-center",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                            className: "w-10 h-10 rounded-full",
                                                                            src: item?.reviewer?.profileImage || (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createInitialsAvatar"])(`${item?.reviewer?.firstName}  ${item?.reviewer?.lastName || ""}`, {
                                                                                bgColor: "#3f51b5",
                                                                                textColor: "#ffffff"
                                                                            }),
                                                                            alt: "Reviewer"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                            lineNumber: 926,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "ml-2 text-base font-semibold",
                                                                            children: item?.reviewer?.firstName
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                            lineNumber: 934,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                    lineNumber: 925,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                lineNumber: 924,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                                                                className: "my-4 border-t border-gray-300"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                lineNumber: 939,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, item._id, true, {
                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                        lineNumber: 912,
                                                        columnNumber: 21
                                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: "No Review Available"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 943,
                                                    columnNumber: 19
                                                }, this),
                                                isLoggedIn && !isSeller && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$book$2d$detail$2f$AddRating$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    bookState: bookState,
                                                    getReviews: getReviews
                                                }, void 0, false, {
                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                    lineNumber: 947,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 906,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 870,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 821,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                    lineNumber: 795,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                    className: "my-5 border-t border-gray-300 md:my-[40px]"
                }, void 0, false, {
                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                    lineNumber: 954,
                    columnNumber: 9
                }, this),
                !isSeller && allBookSeller.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                                            className: "md:text-[45px]  font-bold md:text-3xl sm:text-[24px] ",
                                            children: "BOOKS BY SAME SELLER"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 960,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "max-w-[700px] my-8",
                                            children: "Explore stories, knowledge, and imagination with ReBookIt. Find academic essentials, timeless novels, and rare gems—all in one place."
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 963,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 959,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "hidden md:block",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        width: "178",
                                        height: "72",
                                        viewBox: "0 0 178 72",
                                        fill: "none",
                                        xmlns: "http://www.w3.org/2000/svg",
                                        className: "cursor-pointer",
                                        onClick: ()=>{
                                            router.push("/search");
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285",
                                                stroke: "#211F54",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 983,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017",
                                                stroke: "#0161AB",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 988,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285",
                                                stroke: "#EFDC2A",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 993,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285",
                                                stroke: "#0161AB",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 998,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M140.693 6L36.937 5.99999",
                                                stroke: "#FF0009",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 1003,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M140.693 65.457L36.937 65.457",
                                                stroke: "#4A8B40",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 1008,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                                x: "11.6016",
                                                y: "7.93848",
                                                width: "154.01",
                                                height: "54.6036",
                                                rx: "27.3018",
                                                fill: "white"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 1013,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                                                x: "50%",
                                                y: "50%",
                                                dominantBaseline: "middle",
                                                textAnchor: "middle",
                                                fontSize: "20",
                                                fill: "#211F54",
                                                fontFamily: "Poppins, sans-serif",
                                                children: "View All"
                                            }, void 0, false, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 1021,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 972,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 970,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 958,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            // className="my-5 grid grid-cols-2 lg:grid-cols-4 gap-2.5 md:my-14 md:gap-8 md:justify-between lg:mt-[30px]"
                            className: "",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CustomSlider, {
                                sliderSettings: settings,
                                children: allBookSeller.map((item, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("article", {
                                        className: "border border-[#80808017] pt-1 px-[3px] pb-[15px] lg:border-0",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-gray px-[30px] relative flex justify-center items-center py-1.5 bg-[#f1f1f1] w-full aspect-square lg:aspect-square lg:py-2.5 lg:px-2.5",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                        // src={item.coverImage || item.backCoverImage}
                                                        src: item.images,
                                                        alt: "book image",
                                                        fill: true,
                                                        className: `object-cover px-[30px] max-h-[250px]  transition-opacity duration-300 ${isImageLoaded ? "opacity-100" : "opacity-0"}`,
                                                        sizes: "(min-width: 1024px) 25vw, (min-width: 768px) 50vw, 100vw",
                                                        onLoad: ()=>setisImageLoaded(true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                        lineNumber: 1048,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "h-7 w-7 p-1 rounded-full border border-gray-200 bg-white flex justify-center items-center absolute top-1 right-1 cursor-pointer",
                                                        onClick: ()=>bookTheItemMark(item._id),
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LuHeart"], {
                                                            size: 17,
                                                            color: "#000",
                                                            className: "opacity-90"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                            lineNumber: 1062,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                        lineNumber: 1058,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 1047,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "pt-2 pl-2 pr-2.5 md:text-[20px] lg:pt-5 lg:pb-[28px]",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        title: item.title,
                                                        className: "text-sm leading-[19px] font-semibold lg:text-[20px] lg:leading-[26px] lg:w-11/12 line-clamp-1",
                                                        children: item.title
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                        lineNumber: 1071,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between items-center my-2.5",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "font-bold self-end",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-[#4D7906] lg:text-[20px] lg:leading-[22px]",
                                                                        children: [
                                                                            "JMD",
                                                                            " "
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                        lineNumber: 1080,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    "$",
                                                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatWithCommas"])(item.price)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                lineNumber: 1079,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "cursor-pointer global_linear_gradient p-2 rounded-full flex justify-center items-center lg:h-[46px] lg:w-[46px]",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: {
                                                                        pathname: "/book-detail",
                                                                        query: {
                                                                            id: item._id
                                                                        }
                                                                    },
                                                                    onClick: ()=>document.body.scrollTop(),
                                                                    "aria-label": "View all book categories",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdArrowOutward"], {
                                                                        className: "h-[12px] w-[12px] lg:w-[16px] lg:h-[16px]",
                                                                        color: "#fff"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                        lineNumber: 1094,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                    lineNumber: 1086,
                                                                    columnNumber: 27
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                                lineNumber: 1085,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                        lineNumber: 1078,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                                lineNumber: 1070,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, idx, true, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 1043,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1041,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 1037,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "block md:hidden mx-auto flex justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                "aria-label": "View all book categories",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    width: "178",
                                    height: "72",
                                    viewBox: "0 0 178 72",
                                    fill: "none",
                                    xmlns: "http://www.w3.org/2000/svg",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            d: "M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285",
                                            stroke: "#211F54",
                                            strokeWidth: "11.679"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 1115,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            d: "M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017",
                                            stroke: "#0161AB",
                                            strokeWidth: "11.679"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 1120,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            d: "M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285",
                                            stroke: "#EFDC2A",
                                            strokeWidth: "11.679"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 1125,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            d: "M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285",
                                            stroke: "#0161AB",
                                            strokeWidth: "11.679"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 1130,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            d: "M140.693 6L36.937 5.99999",
                                            stroke: "#FF0009",
                                            strokeWidth: "11.679"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 1135,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            d: "M140.693 65.457L36.937 65.457",
                                            stroke: "#4A8B40",
                                            strokeWidth: "11.679"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 1140,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                            x: "11.6016",
                                            y: "7.93848",
                                            width: "154.01",
                                            height: "54.6036",
                                            rx: "27.3018",
                                            fill: "white"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 1145,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                                            x: "50%",
                                            y: "50%",
                                            dominantBaseline: "middle",
                                            textAnchor: "middle",
                                            fontSize: "20",
                                            fill: "#211F54",
                                            fontFamily: "Poppins, sans-serif",
                                            children: "View All"
                                        }, void 0, false, {
                                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                            lineNumber: 1153,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 1108,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1107,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/book-detail/bookDetailComponent.js",
                            lineNumber: 1106,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                    lineNumber: 957,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    id: "myModal",
                    className: "fixed inset-0 flex items-center justify-center z-50 bg-[#EAEAEA]/60 backdrop-blur-sm transition-opacity hidden duration-300 ease-in-out",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-[#fdfdfd] p-[50px] w-[500px] rounded-lg shadow-lg relative transform transition-all duration-300 ease-in-out scale-95 opacity-0 animate-fadeIn",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute top-[-10px] right-[-10px] w-[50px] h-[50px] rounded-full bg-white flex items-center justify-center cursor-pointer",
                                onClick: ()=>{
                                    document.getElementById("myModal").classList.add("hidden");
                                    setmodelForAnswer(false);
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$rx$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RxCross1"], {
                                    className: "",
                                    size: 23
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 1187,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1180,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative w-full h-full  bg-[#FFFBF6] bookDetailMainImageShadow",
                                children: bookState?.images?.length && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                    src: bookState.images[0] || "/images/book_1.jpg",
                                    alt: "...loading",
                                    fill: true,
                                    className: `object-cover mx-auto min-w-350px  transition-opacity duration-300 ${isImageLoaded ? "opacity-100" : "opacity-0"}`,
                                    onLoad: ()=>setisImageLoaded(true),
                                    sizes: "33w"
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 1192,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1190,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-[#212A30] text-[14px] md:text-[28px] font-bold my-4",
                                children: bookState?.itemName
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1203,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-[#212A30] text-[12px] md:text-[19px] text-sm my-2",
                                children: [
                                    "Category:",
                                    " ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: bookState?.categoryId?.name || "Sample Category"
                                    }, void 0, false, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 1209,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1207,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-[12px] md:text-[21px] leading-[12px] md:leading-[27px] my-2 ",
                                children: [
                                    "Location:",
                                    " ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: bookState.address?.formatted_address || address
                                    }, void 0, false, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 1215,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1213,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center my-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-[27px] font-semibold",
                                        children: "Price"
                                    }, void 0, false, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 1221,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        className: "text-white text-[20px] ml-2 bg-[#1E2858] rounded-full py-1.5 px-3",
                                        children: [
                                            "J$",
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatWithCommas"])(bookState?.price)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                        lineNumber: 1222,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1220,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-6 flex justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>boostItemFunc(bookState._id),
                                    className: "py-[1rem] px-[30px] text-[25px]   leading-[18px] text-center text-white rounded-full global_linear_gradient",
                                    children: "Confirm Boost"
                                }, void 0, false, {
                                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                    lineNumber: 1228,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/book-detail/bookDetailComponent.js",
                                lineNumber: 1227,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/book-detail/bookDetailComponent.js",
                        lineNumber: 1178,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/book-detail/bookDetailComponent.js",
                    lineNumber: 1174,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/book-detail/bookDetailComponent.js",
            lineNumber: 595,
            columnNumber: 7
        }, this);
    }
}
_s(BookDetailComponent, "aTLTZgXjLAXcxQg80o7ohx5mulg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"]
    ];
});
_c1 = BookDetailComponent;
const __TURBOPACK__default__export__ = BookDetailComponent;
var _c, _c1;
__turbopack_context__.k.register(_c, "CustomSlider");
__turbopack_context__.k.register(_c1, "BookDetailComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_1c219334._.js.map