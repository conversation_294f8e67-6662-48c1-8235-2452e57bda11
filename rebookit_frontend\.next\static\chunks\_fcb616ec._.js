(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/search/book-listing.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "bookListingContainer": "book-listing-module-scss-module__PX20Ea__bookListingContainer",
  "boxShadow": "book-listing-module-scss-module__PX20Ea__boxShadow",
  "gradientSwitch": "book-listing-module-scss-module__PX20Ea__gradientSwitch",
  "tagText": "book-listing-module-scss-module__PX20Ea__tagText",
});
}}),
"[project]/public/images/No_result_found_search_page.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/No_result_found_search_page.a2275c72.svg");}}),
"[project]/public/images/No_result_found_search_page.svg.mjs { IMAGE => \"[project]/public/images/No_result_found_search_page.svg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$No_result_found_search_page$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/images/No_result_found_search_page.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$No_result_found_search_page$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 372,
    height: 372,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {
        // Clear local storage and redirect to /login
        localStorage.clear();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
        window.location.href = "/login";
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        // console.log("error log is", error)
        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.message)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://devapi.rebookit.club");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/profile.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addReviewForSeller": (()=>addReviewForSeller),
    "bookMarkItem": (()=>bookMarkItem),
    "boostItem": (()=>boostItem),
    "deleteChatById": (()=>deleteChatById),
    "deleteMyBooks": (()=>deleteMyBooks),
    "delete_bookMarkItem": (()=>delete_bookMarkItem),
    "editItem": (()=>editItem),
    "getAdPlanById": (()=>getAdPlanById),
    "getAdPlans": (()=>getAdPlans),
    "getAllChat": (()=>getAllChat),
    "getBooksById": (()=>getBooksById),
    "getCategories": (()=>getCategories),
    "getChatById": (()=>getChatById),
    "getFaq": (()=>getFaq),
    "getItemBySeller": (()=>getItemBySeller),
    "getMyBooks": (()=>getMyBooks),
    "getPaymentIntent": (()=>getPaymentIntent),
    "getReviewsOfUser": (()=>getReviewsOfUser),
    "getSubCategories": (()=>getSubCategories),
    "getSubSubCategories": (()=>getSubSubCategories),
    "getSubSubSubCategories": (()=>getSubSubSubCategories),
    "getTestimonials": (()=>getTestimonials),
    "get_bookMarkItems": (()=>get_bookMarkItems),
    "getsubscriptionPlans": (()=>getsubscriptionPlans),
    "listItem": (()=>listItem),
    "paymentHistory": (()=>paymentHistory),
    "searchByName": (()=>searchByName),
    "searchISBN": (()=>searchISBN),
    "searchItemByName": (()=>searchItemByName),
    "supportRequest": (()=>supportRequest),
    "uploadPhotoSingle": (()=>uploadPhotoSingle),
    "verifyUserData": (()=>verifyUserData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/axios.js [app-client] (ecmascript)");
;
;
;
const uri = {
    login: "/user/login",
    userInfo: "/user",
    editProfile: "/user/edit-profile",
    item_by_name: `item/search`,
    subscriptionPlan: "/admin/subscription/plan",
    fetch_category: "/master/category",
    fetchSubCategory: "master/sub-category",
    fetchSubSubCategory: "master/Sub-Sub-category",
    fetchSubSubSubCategory: "master/sub-sub-sub-category",
    getPaymentIntent: "/payment/payment-intent",
    verifyUserData: "user/verify-otp",
    searchISBN: "/books/isbn/{{ISBN}}",
    searchByName: "/books/search?q={search}",
    bookMarkItem_id: "/item/bookmark",
    get_bookMark_by_user: "/item/bookmarks",
    getItems: "/item/search/current-user",
    getItemById: "/item",
    createItem: "/item",
    editItem: "/item",
    deleteItemById: "/item",
    itemBySeller: "/item/user",
    addReview: "/item/addReview",
    userReviews: "/item/{:id}/reviews",
    uploadPhoto: "admin/single-upload",
    history: "/payment/history",
    supportRequest: "/user/support-request",
    boostItem: "/item/boost",
    getTestimonials: "/user/testimonials",
    getFaq: "/faqs/filter-search",
    // ad-management
    getAdPlans: "ad-management/getAdPlans",
    getAdPlanById: "ad-management/getAdPlanById"
};
const chat = {
    chat: "/chat/all",
    chatById: "/chat"
};
const listItem = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.createItem}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const getAdPlans = async ({ type, position, page = 1, limit = 10 } = {})=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlans}`, {
        params: {
            ...type && {
                type
            },
            ...position && {
                position
            },
            page,
            limit
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getAdPlanById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlanById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const editItem = async (payload, id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.editItem}/${id}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const addReviewForSeller = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.addReview}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getMyBooks = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getItems}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const deleteMyBooks = async (id, data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.deleteItemById}/${id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getBooksById = async (id, userId)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getItemById}/${id}`, {
        params: {
            userId
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getItemBySeller = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.itemBySeller}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const searchItemByName = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.item_by_name}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getsubscriptionPlans = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.subscriptionPlan}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetch_category}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getAllChat = async (payloadData)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${chat.chat}`, payloadData).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const deleteChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const bookMarkItem = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.bookMarkItem_id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const getReviewsOfUser = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.userReviews}`.replace("{:id}", id));
    // .catch(axiosErrorHandler);
    console.log("getSubCategories response", response);
    return response;
};
const delete_bookMarkItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${uri.bookMarkItem_id}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const get_bookMarkItems = async ()=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.get_bookMark_by_user}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getPaymentIntent = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getPaymentIntent}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const verifyUserData = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.verifyUserData}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchISBN = async (ISBN)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchISBN}`.replace("{{ISBN}}", ISBN)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchByName = async (search)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchByName}`.replace("{search}", search)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const uploadPhotoSingle = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.uploadPhoto}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const paymentHistory = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.history}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const supportRequest = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.supportRequest}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const boostItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.boostItem}/` + id).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getTestimonials = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getTestimonials}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getFaq = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getFaq}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
} // let data = await fetch(USER_ROUTES.SEARCH_ITEM_BY_NAME.replace("itemName", text), {
 //                 headers: {
 //                     "Content-Type": "application/json",
 //                     "Authorization": `Bearer ${userToken}`
 //                 },
 //             },)
 //             let response = await data.json()
 //             // console.log("data getAllBookOfUser", await data.json())
 //             if (response.data) {
 //                 setBookData(response.data)
 //             }
 // export const login = async (payload, guestId) => {
 //     let response = await instance
 //         .post(`${uri.login}`,payload)
 //         .catch(axiosErrorHandler);
 //         console.log("login test response",response)
 //         return response
 // };
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/ShareCard.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ShareCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io5/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$rx$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/rx/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
function ShareCard({ url }) {
    // allresponses?id=684bad555ed120f70572e24c
    let urlLocation = `${window.location.origin}/${url}`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        id: "myShareModal",
        className: " fixed inset-0 flex items-center justify-center  bg-opacity-10 z-50  hidden bg-[#000000bf] px-[20px] ",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white flex flex-col  h-[400px] rounded-lg w-full max-w-lg shadow-lg relative rounded-[20px]",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex w-[50px] cursor-pointer mx-3 flex items-center h-[50px] justify-center absolute top-[-10px] right-[-20px] rounded-full bg-[#fefefe] ",
                    onClick: ()=>{
                        let docElement = document.getElementById('myShareModal').classList.add("hidden");
                        console.log("docElement", docElement);
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$rx$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RxCross1"], {
                        className: "text-[#000000]"
                    }, void 0, false, {
                        fileName: "[project]/app/components/common/ShareCard.js",
                        lineNumber: 22,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/common/ShareCard.js",
                    lineNumber: 17,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-[220px] flex items-center justify-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                        className: "w-[50px]",
                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/49e5dbbe-fb83-49e2-8475-9ff3a11971c3.png"
                    }, void 0, false, {
                        fileName: "[project]/app/components/common/ShareCard.js",
                        lineNumber: 25,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/common/ShareCard.js",
                    lineNumber: 24,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-[220px] w-full  global_linear_gradient rounded-t-[10px] rounded-b-[20px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-center mt-4",
                            onClick: ()=>{
                                navigator.clipboard.writeText(urlLocation);
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("URL copied");
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: " w-[50px] h-[50px] flex items-center p-3 bg-white rounded-full cursor-pointer",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoCopy"], {
                                        size: 25,
                                        className: "text-[#211F54]"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 33,
                                        columnNumber: 108
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 33,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-white ml-4 flex items-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-[20px]",
                                        children: "Copy Link"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 36,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 35,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/common/ShareCard.js",
                            lineNumber: 29,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center text-white mt-2",
                            children: urlLocation
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/ShareCard.js",
                            lineNumber: 42,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-5  justify-center mt-[25px] pb-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.facebook.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/4cea7427-6894-463a-bff6-34aa09bc1f06.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 46,
                                        columnNumber: 154
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 46,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.instagram.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/47f5618b-0452-4f67-b2db-6c5fec6827a1.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 47,
                                        columnNumber: 155
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 47,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.whatsapp.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/18681b6c-c4e2-4072-9a82-be38416d1d17.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 48,
                                        columnNumber: 154
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 48,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.x.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/8652b904-0d14-4d3e-a913-eb999d96cf06.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 49,
                                        columnNumber: 147
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 49,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.pintrest.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/76225cab-d847-4a93-a24d-0ff3b1bdff6d.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 50,
                                        columnNumber: 154
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 50,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/common/ShareCard.js",
                            lineNumber: 45,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/common/ShareCard.js",
                    lineNumber: 28,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/common/ShareCard.js",
            lineNumber: 14,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/common/ShareCard.js",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = ShareCard;
var _c;
__turbopack_context__.k.register(_c, "ShareCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/utils/restCall.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const RestCall = async (data)=>{
    try {
        let response = await axios(data);
        return response;
    } catch (err) {
        return {
            message: err.message,
            code: err.statusCode
        };
    }
};
_c = RestCall;
const __TURBOPACK__default__export__ = RestCall;
var _c;
__turbopack_context__.k.register(_c, "RestCall");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/bookDetails.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { default: RestCall } = __turbopack_context__.r("[project]/app/utils/restCall.js [app-client] (ecmascript)");
const { ELASTIC_DB_ROUTES } = __turbopack_context__.r("[project]/app/config/api.js [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const instance = __turbopack_context__.r("[project]/app/services/axios.js [app-client] (ecmascript)");
const { axiosErrorHandler } = __turbopack_context__.r("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
const bookDetails = async (data)=>{
    try {
        let userToken = getToken();
        let response = await RestCall({
            method: "get",
            url: `${USER_ROUTES.LIST_ITEM}/${id}`,
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${userToken}`
            },
            data: data
        });
        return response;
    } catch (err) {
        return {
            message: err.message,
            code: err.statusCode
        };
    }
};
const bookSearch = async (url, data)=>{
    try {
        let userToken = getToken();
        let response = await RestCall({
            method: "POST",
            url: url,
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${userToken}`
            },
            data: data
        });
        return response;
    } catch (err) {
        return {
            message: err.message,
            code: err.statusCode
        };
    }
};
// const bookSuggestion =async(url)=>{
//      try{
//      let userToken = getToken()
//         let response = await RestCall({
//             method:"get",
//             url: url,
//             headers: {
//                 "Content-Type": "application/json",
//                 "Authorization": `Bearer ${userToken}`
//             }
//         })
//         return response
//     }catch(err){
//         return { message: err.message, code: err.statusCode }
//     }
// }
// const markAsSold=async(url,data)=>{
//      try{
//      let userToken = getToken()
//         let response = await RestCall({
//             method:"put",
//             url: url,
//             headers: {
//                 "Content-Type": "application/json",
//                 "Authorization": `Bearer ${userToken}`
//             },
//             data
//         })
//         return response
//     }catch(err){
//         return { message: err.message, code: err.statusCode }
//     }
// }
const bookSuggestion = async (url)=>{
    let response = await instance.get(url).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const markAsSold = async (url, data)=>{
    let response = await instance.put(url, data).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
module.exports = {
    bookDetails,
    bookSearch,
    bookSuggestion,
    markAsSold
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/search/ItemRespectiveDetails.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ItemRespectiveDetails)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/constant.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
;
;
;
;
function ItemRespectiveDetails({ item }) {
    if (item.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].BookItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        "Type: ",
                        item?.condition || ""
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 8,
                    columnNumber: 13
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "line-clamp-1 mt-2 max-w-[60%] md:max-w-[80%]",
                    children: [
                        "Author:",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            title: item?.authors,
                            className: "font-medium",
                            children: item?.authors
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 15,
                            columnNumber: 17
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 13,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/search/ItemRespectiveDetails.js",
            lineNumber: 7,
            columnNumber: 9
        }, this);
    } else if (item.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].TutorItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].TutorItem.experience
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item?.experience || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 28,
                            columnNumber: 59
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 25,
                    columnNumber: 14
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].TutorItem.targetClasses
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item?.targetClasses || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 38,
                            columnNumber: 62
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 35,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/search/ItemRespectiveDetails.js",
            lineNumber: 24,
            columnNumber: 15
        }, this);
    } else if (item.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].EventItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].EventItem.eventStartDate
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(item?.eventStartDate).format("DD-MMM-YYYY") || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 48,
                            columnNumber: 63
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 45,
                    columnNumber: 14
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].EventItem.eventMode
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item.eventMode || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 53,
                            columnNumber: 58
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 50,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/search/ItemRespectiveDetails.js",
            lineNumber: 44,
            columnNumber: 15
        }, this);
    } else if (item.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].SchoolItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal line-clamp-1  md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].SchoolItem.classesOffered
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item?.classesOffered || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 62,
                            columnNumber: 64
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 59,
                    columnNumber: 14
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].SchoolItem.schoolType
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item?.schoolType || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 67,
                            columnNumber: 60
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 64,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/search/ItemRespectiveDetails.js",
            lineNumber: 58,
            columnNumber: 15
        }, this);
    } else if (item.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].ScholarshipAwardItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ScholarshipAwardItem.eligibilityCriteria
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item?.eligibilityCriteria || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 76,
                            columnNumber: 79
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 73,
                    columnNumber: 14
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ScholarshipAwardItem.scholarshipType
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item?.scholarshipType || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 81,
                            columnNumber: 75
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 78,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/search/ItemRespectiveDetails.js",
            lineNumber: 72,
            columnNumber: 15
        }, this);
    } else if (item.__t == __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ItemKindEnum"].ExtracurricularActivityItem) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ExtracurricularActivityItem.targetStudents
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item?.targetStudents || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 91,
                            columnNumber: 81
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 88,
                    columnNumber: 14
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: `mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`,
                    children: [
                        [
                            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["labelItemToKind"].ExtracurricularActivityItem.activityType
                        ],
                        ": ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: item?.activityType || ""
                        }, void 0, false, {
                            fileName: "[project]/app/search/ItemRespectiveDetails.js",
                            lineNumber: 96,
                            columnNumber: 79
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/search/ItemRespectiveDetails.js",
                    lineNumber: 93,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/search/ItemRespectiveDetails.js",
            lineNumber: 87,
            columnNumber: 15
        }, this);
    }
}
_c = ItemRespectiveDetails;
var _c;
__turbopack_context__.k.register(_c, "ItemRespectiveDetails");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/hooks/useDebounce.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDebounce": (()=>useDebounce)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function useDebounce(value, delay) {
    _s();
    const [debouncedValue, setDebouncedValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(value);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDebounce.useEffect": ()=>{
            const timer = setTimeout({
                "useDebounce.useEffect.timer": ()=>{
                    setDebouncedValue(value);
                }
            }["useDebounce.useEffect.timer"], delay);
            return ({
                "useDebounce.useEffect": ()=>clearTimeout(timer)
            })["useDebounce.useEffect"]; // cleanup on value or delay change
        }
    }["useDebounce.useEffect"], [
        value,
        delay
    ]);
    return debouncedValue;
}
_s(useDebounce, "KDuPAtDOgxm8PU6legVJOb3oOmA=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/search/Search.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Search)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$bookDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/bookDetails.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/hooks/useDebounce.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
function Search() {
    _s();
    const [searchSuggestions, setSearchSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [isLoading, setisLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [changeSearchText, setchangeSearchText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const fetchSearchData = async (event)=>{
        setisLoading(true);
        try {
            console.log("event in search data", event.target.value);
            let text = event.target.value;
            setchangeSearchText(text);
            if (text.length) {
                let urlString = `${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELASTIC_DB_ROUTES"].SUGGESTION}?searchTerm=${text.trim()}&page=1`;
                let searchData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$bookDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bookSuggestion"])(urlString);
                console.log("searchData items", searchData);
                if (searchData.status == 200) {
                    if (searchData?.data?.data.length) {
                        setSearchSuggestions(searchData.data.data);
                    } else {
                        setSearchSuggestions([]);
                    }
                } else {
                    setSearchSuggestions([]);
                }
            } else {
                setSearchSuggestions([]);
            }
            setisLoading(false);
        } catch (error) {
            console.log("error", error);
            setisLoading(false);
        }
    };
    console.log("searchSuggestions", searchSuggestions);
    console.log("setchangeSearchText", changeSearchText);
    //  border-[#D9E2E8]
    let defaultHeight = 'full';
    const debounceHandle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Debounce"])(fetchSearchData, 700);
    // Loader spinner component
    const Loader = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            className: "animate-spin h-5 w-5 text-[#0161ab]",
            xmlns: "http://www.w3.org/2000/svg",
            fill: "none",
            viewBox: "0 0 24 24",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                    className: "opacity-25",
                    cx: "12",
                    cy: "12",
                    r: "10",
                    stroke: "currentColor",
                    strokeWidth: "4"
                }, void 0, false, {
                    fileName: "[project]/app/search/Search.js",
                    lineNumber: 53,
                    columnNumber: 13
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    className: "opacity-75",
                    fill: "currentColor",
                    d: "M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                }, void 0, false, {
                    fileName: "[project]/app/search/Search.js",
                    lineNumber: 54,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/search/Search.js",
            lineNumber: 52,
            columnNumber: 9
        }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `absolute z-[1] bg-white border border-[#ccc] ml-auto mr-4  rounded-xl w-full  top-[10px] ${searchSuggestions.length ? "h-fit" : "defaultHeight"}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        className: "w-full pl-4 pr-3 p-[9px] placeholder:text-sm text-[#757575] placeholder:text-[#757575] placeholder:font-light outline-none",
                        type: "text",
                        onChange: debounceHandle,
                        placeholder: "Search by Books, E-Directory, Events, Scholarship & Awards."
                    }, void 0, false, {
                        fileName: "[project]/app/search/Search.js",
                        lineNumber: 61,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: "m-2 px-2.5 cursor-auto gap-1 rounded-full flex justify-center items-center",
                        type: "submit",
                        onClick: ()=>{
                        // alert("hello");
                        },
                        disabled: isLoading,
                        children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Loader, {}, void 0, false, {
                            fileName: "[project]/app/search/Search.js",
                            lineNumber: 76,
                            columnNumber: 34
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoIosSearch"], {}, void 0, false, {
                            fileName: "[project]/app/search/Search.js",
                            lineNumber: 76,
                            columnNumber: 47
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/search/Search.js",
                        lineNumber: 68,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/search/Search.js",
                lineNumber: 60,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: searchSuggestions.length ? searchSuggestions.map((item)=>{
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between px-3 py-1 cursor-pointer hover:bg-[#211F54] hover:text-white",
                        onClick: ()=>router.push(`/book-detail?id=${item._id}`),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: item.title
                            }, void 0, false, {
                                fileName: "[project]/app/search/Search.js",
                                lineNumber: 84,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                    className: "h-[30] w-[25px] rounded-md",
                                    src: item.images[0]
                                }, void 0, false, {
                                    fileName: "[project]/app/search/Search.js",
                                    lineNumber: 85,
                                    columnNumber: 30
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/search/Search.js",
                                lineNumber: 85,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/search/Search.js",
                        lineNumber: 83,
                        columnNumber: 28
                    }, this);
                }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "px-3 text-center",
                    children: !isLoading && changeSearchText ? "No data found" : ""
                }, void 0, false, {
                    fileName: "[project]/app/search/Search.js",
                    lineNumber: 87,
                    columnNumber: 22
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/search/Search.js",
                lineNumber: 81,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/search/Search.js",
        lineNumber: 59,
        columnNumber: 9
    }, this);
}
_s(Search, "HsrH3EwU7Maq+aswJJkZVik9Yzo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = Search;
var _c;
__turbopack_context__.k.register(_c, "Search");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/search/bookListingComponent.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$book$2d$listing$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/search/book-listing.module.scss.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa6$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa6/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/lu/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/hi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ci$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/ci/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/slices/storeSlice.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$No_result_found_search_page$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$No_result_found_search_page$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/images/No_result_found_search_page.svg.mjs { IMAGE => "[project]/public/images/No_result_found_search_page.svg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/profile.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$ShareCard$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/ShareCard.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$bookDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/bookDetails.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$ItemRespectiveDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/search/ItemRespectiveDetails.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$Search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/search/Search.js [app-client] (ecmascript)");
;
;
;
;
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const CustomSlider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/app/components/common/Slider.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/app/components/common/Slider.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    }
});
_c = CustomSlider;
const MapView = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/app/components/book-listing/Mapview.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/app/components/book-listing/Mapview.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    }
});
_c1 = MapView;
const GoogleSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_c2 = ()=>__turbopack_context__.r("[project]/app/search/GoogleMapSearch.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/app/search/GoogleMapSearch.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    }
});
_c3 = GoogleSearch;
const BuyAndSellComponent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/app/components/about/BuyAndSellComponent.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/app/components/about/BuyAndSellComponent.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    }
});
_c4 = BuyAndSellComponent;
function BookListing() {
    _s();
    ////////////////////////////////
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const categoryId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "BookListing.useMemo[categoryId]": ()=>searchParams.get("category")
    }["BookListing.useMemo[categoryId]"], [
        searchParams
    ]);
    const subCategoryId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "BookListing.useMemo[subCategoryId]": ()=>searchParams.get("subCategory")
    }["BookListing.useMemo[subCategoryId]"], [
        searchParams
    ]);
    ////////////////////////////////
    let GoogleSearchBack = GoogleSearch;
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [bookmarkedItems, setBookmarkedItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [location, setLocation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const filterRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const isListCheck = searchParams.get("isList");
    const [isList, setIsList] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(isListCheck ? false : true);
    const [filterOccurred, setFilterOccurred] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedId, setselectedId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [selectedParish, setselectedParish] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [searchCoordinates, setsearchCoordinates] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isParish, setIsParish] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    console.log(searchCoordinates, selectedParish, isParish, "searchCoordinates");
    /////////////new
    const userLocationData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "BookListing.useSelector[userLocationData]": (state)=>state.storeData.userLocationData
    }["BookListing.useSelector[userLocationData]"]);
    const [locationData, setLocationData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            // If userLocationData is not present in the store, fetch and store it
            if (!userLocationData || !userLocationData.latitude || !userLocationData.longitude) {
                ({
                    "BookListing.useEffect": async ()=>{
                        try {
                            const locationData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ipBasedLocationFinder"])();
                            // Store in redux
                            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateUserLocationData"])(locationData));
                            // Store in local state as well
                            setLocationData({
                                locality: locationData.locality || "",
                                latitude: locationData.latitude || "",
                                longitude: locationData.longitude || ""
                            });
                        } catch (e) {
                            console.warn("Failed to get user location data:", e);
                        }
                    }
                })["BookListing.useEffect"]();
            } else {
                setLocationData({
                    locality: userLocationData.locality || "",
                    latitude: userLocationData.latitude || "",
                    longitude: userLocationData.longitude || ""
                });
            }
        }
    }["BookListing.useEffect"], [
        userLocationData,
        dispatch
    ]);
    console.log(locationData, "locationData");
    const [bookList, setBookList] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [pagination, setPagination] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        page: 1,
        pageSize: 10,
        totalPages: 1,
        totalItems: 0,
        showLoader: false
    });
    const [booksLoading, setBooksLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // New Filters State
    // Load selected category and subcategory from localStorage if available
    const getInitialCategory = ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            try {
                const stored = localStorage.getItem("selectedCategory");
                if (stored) {
                    const parsed = JSON.parse(stored);
                    return {
                        id: parsed.id || "",
                        name: parsed.name || "Category"
                    };
                }
            } catch (e) {}
        }
        return {
            id: "",
            name: "Category"
        };
    };
    const getInitialSubCategory = ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            try {
                const stored = localStorage.getItem("selectedSubCategory");
                if (stored) {
                    const parsed = JSON.parse(stored);
                    return {
                        id: parsed.id || "",
                        name: parsed.name || "Sub-Category"
                    };
                }
            } catch (e) {}
        }
        return {
            id: "",
            name: "Sub-Category"
        };
    };
    const [categories, setCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [subCategories, setSubCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedCategoryId, setSelectedCategoryId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getInitialCategory().id);
    const [selectedCategoryName, setSelectedCategoryName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getInitialCategory().name);
    const [selectedSubCategoryId, setSelectedSubCategoryId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getInitialSubCategory().id);
    const [selectedSubCategoryName, setSelectedSubCategoryName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getInitialSubCategory().name);
    const [openCategory, setOpenCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [openSubCategory, setOpenSubCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Skeleton Components
    const ListViewSkeleton = ({ count = 4 })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col gap-5 w-full lg:w-[65%] xl:w-[60%]  lg:gap-[30px] order-2 lg:order-1",
            children: Array.from({
                length: count
            }).map((_, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border border-[#EAEAEA] p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 h-fit bg-white rounded animate-pulse",
                    style: {
                        minHeight: 180
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "col-span-1 w-full aspect-[3/4] sm:aspect-[2/3] md:aspect-auto overflow-hidden bg-gray-200 rounded relative flex items-center justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-[80%] h-[80%] bg-gray-300 rounded"
                            }, void 0, false, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 201,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/search/bookListingComponent.js",
                            lineNumber: 200,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "col-span-1 md:col-span-3 flex flex-col gap-3 justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-5 w-2/3 bg-gray-200 rounded mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 206,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 w-1/3 bg-gray-200 rounded mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 207,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 w-1/2 bg-gray-200 rounded mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 208,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 w-1/4 bg-gray-200 rounded mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 209,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 w-1/3 bg-gray-200 rounded mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 210,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-4 w-1/2 bg-gray-200 rounded mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 211,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 205,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2 mt-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-8 w-20 bg-gray-200 rounded"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 214,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-8 w-8 bg-gray-200 rounded-full"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 215,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-8 w-8 bg-gray-200 rounded-full"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 216,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 213,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/search/bookListingComponent.js",
                            lineNumber: 204,
                            columnNumber: 11
                        }, this)
                    ]
                }, `list-skeleton-${idx}`, true, {
                    fileName: "[project]/app/search/bookListingComponent.js",
                    lineNumber: 194,
                    columnNumber: 9
                }, this))
        }, void 0, false, {
            fileName: "[project]/app/search/bookListingComponent.js",
            lineNumber: 192,
            columnNumber: 5
        }, this);
    const GridViewSkeleton = ({ count = 8 })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: Array.from({
                length: count
            }).map((_, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative p-[13px] pb-4 border border-[#EAEAEA] bg-white rounded animate-pulse",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative h-[218px] aspect-[3/4] bg-[#FFFBF6] py-2.5 flex justify-center items-center box-border",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-[90%] h-[80%] bg-gray-200 rounded"
                            }, void 0, false, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 233,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/search/bookListingComponent.js",
                            lineNumber: 232,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-[11px]",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-6 w-3/4 bg-gray-200 rounded mb-2"
                                }, void 0, false, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 238,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-4 w-1/2 bg-gray-200 rounded mb-2"
                                }, void 0, false, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 239,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-4 w-2/3 bg-gray-200 rounded mb-2"
                                }, void 0, false, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 240,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-4 w-1/3 bg-gray-200 rounded mb-2"
                                }, void 0, false, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 241,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-4 w-1/2 bg-gray-200 rounded mb-2"
                                }, void 0, false, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 242,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-4 w-1/4 bg-gray-200 rounded mb-2"
                                }, void 0, false, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 243,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-4 w-1/3 bg-gray-200 rounded mb-2"
                                }, void 0, false, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 244,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-4 flex gap-[34px] justify-between items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-10 w-[120px] bg-gray-200 rounded-full"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 247,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "h-6 w-20 bg-gray-200 rounded"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 248,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 246,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/search/bookListingComponent.js",
                            lineNumber: 237,
                            columnNumber: 11
                        }, this)
                    ]
                }, `grid-skeleton-${idx}`, true, {
                    fileName: "[project]/app/search/bookListingComponent.js",
                    lineNumber: 227,
                    columnNumber: 9
                }, this))
        }, void 0, false);
    const GridViewSkeletonContainer = ({ count = 8 })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "grid grid-cols-1  md:grid-cols-2 lg:grid-cols-3 gap-[33px] mt-5 pb-10 md:pb-[70px]",
            style: {
                display: "contents"
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GridViewSkeleton, {
                count: count
            }, void 0, false, {
                fileName: "[project]/app/search/bookListingComponent.js",
                lineNumber: 261,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/search/bookListingComponent.js",
            lineNumber: 257,
            columnNumber: 5
        }, this);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            fetchCategories();
        }
    }["BookListing.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            // Handle category selection
            if (categoryId && categories.length) {
                if (categoryId !== selectedCategoryId) {
                    const cat = categories.find({
                        "BookListing.useEffect.cat": (c)=>c._id === categoryId
                    }["BookListing.useEffect.cat"]);
                    setSelectedCategoryId(categoryId);
                    setSelectedCategoryName(cat ? cat.name : "Category");
                    localStorage.setItem("selectedCategory", JSON.stringify({
                        id: categoryId,
                        name: cat ? cat.name : "Category"
                    }));
                    fetchSubCategories(categoryId);
                    // Reset subcategory if category changes
                    setSelectedSubCategoryId("");
                    setSelectedSubCategoryName("Sub-Category");
                    localStorage.removeItem("selectedSubCategory");
                }
            }
            // Handle subcategory selection (only if subCategories are loaded)
            if (subCategoryId && subCategories.length) {
                if (subCategoryId !== selectedSubCategoryId) {
                    const subCat = subCategories.find({
                        "BookListing.useEffect.subCat": (s)=>s._id === subCategoryId
                    }["BookListing.useEffect.subCat"]);
                    setSelectedSubCategoryId(subCategoryId);
                    setSelectedSubCategoryName(subCat ? subCat.name : "Sub-Category");
                    localStorage.setItem("selectedSubCategory", JSON.stringify({
                        id: subCategoryId,
                        name: subCat ? subCat.name : "Sub-Category"
                    }));
                }
            }
        // eslint-disable-next-line
        }
    }["BookListing.useEffect"], [
        categoryId,
        subCategoryId
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            if (locationData) {
                fetchSearchData();
            }
        }
    }["BookListing.useEffect"], [
        categoryId,
        subCategoryId,
        selectedCategoryId,
        selectedSubCategoryId,
        searchCoordinates,
        selectedParish,
        isParish,
        locationData
    ]);
    // Additional effect to handle location data changes more robustly
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            // If we have userLocationData but no locationData, sync them
            if (userLocationData && !locationData) {
                setLocationData({
                    locality: userLocationData.locality || "",
                    latitude: userLocationData.latitude || "",
                    longitude: userLocationData.longitude || ""
                });
            }
        }
    }["BookListing.useEffect"], [
        userLocationData,
        locationData
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            if (pagination.page !== 1) {
                fetchSearchData(pagination.page);
            }
        }
    }["BookListing.useEffect"], [
        pagination.page
    ]);
    let userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
    const fetchSearchData = async (isLoadMore)=>{
        try {
            if (!isLoadMore) {
                setBooksLoading(true);
            }
            let userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
            let urlstring = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ELASTIC_DB_ROUTES"].SEARCH?.replace("{{page}}", isLoadMore ? pagination.page : 1);
            if (userData) {
                urlstring = urlstring + `&userId=${userData._id}`;
            }
            const finalCategoryId = categoryId || selectedCategoryId || null;
            const finalSubCategoryId = subCategoryId || selectedSubCategoryId || null;
            // Ensure we have valid location data
            let locationFilter = {};
            if (userLocationData?.isParishSelection && userLocationData?.locality) {
                locationFilter = {
                    parish: [
                        userLocationData.locality
                    ]
                };
            } else if (locationData?.latitude && locationData?.longitude) {
                locationFilter = {
                    coordinates: [
                        locationData.longitude,
                        locationData.latitude
                    ],
                    maxDistanceInKm: 100
                };
            } else if (userLocationData?.latitude && userLocationData?.longitude) {
                // Fallback to userLocationData if locationData is not available
                locationFilter = {
                    coordinates: [
                        userLocationData.longitude,
                        userLocationData.latitude
                    ],
                    maxDistanceInKm: 100
                };
            }
            let payload = {
                filters: {
                    ...finalCategoryId ? {
                        category: [
                            finalCategoryId
                        ]
                    } : {},
                    ...finalSubCategoryId ? {
                        subCategory: [
                            finalSubCategoryId
                        ]
                    } : {},
                    ...locationFilter
                },
                sort: {}
            };
            let fetchedBookData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$bookDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bookSearch"])(urlstring, payload);
            if (isLoadMore) {
                setBookList((prevList)=>[
                        ...prevList,
                        ...fetchedBookData?.data?.data || []
                    ]);
                setPagination((prev)=>({
                        ...prev,
                        showLoader: false
                    }));
            } else {
                setBookList(fetchedBookData?.data?.data);
            }
            setPagination({
                page: fetchedBookData?.data?.page,
                pageSize: fetchedBookData?.data?.pageSize,
                totalPages: fetchedBookData?.data?.totalPages,
                totalItems: fetchedBookData?.data?.totalCount
            });
            setBooksLoading(false);
        } catch (error) {
            console.log("error", error);
            setBooksLoading(false);
            setPagination((prev)=>({
                    ...prev,
                    showLoader: false
                }));
        }
    };
    const fetchCategories = async ()=>{
        try {
            let fetchedCategories = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategories"])();
            if (fetchedCategories?.status == 200) {
                const cats = fetchedCategories?.data?.categories || [];
                setCategories(cats);
                // Check for categoryId in params or local state
                let selectedCatId = categoryId || getInitialCategory().id || "";
                let selectedCat = cats.find((cat)=>cat._id === selectedCatId);
                // If not found, fallback to first category
                if (!selectedCat && cats.length > 0) {
                    selectedCat = cats[0];
                    selectedCatId = cats[0]._id;
                }
                setSelectedCategoryId(selectedCat ? selectedCat._id : "");
                setSelectedCategoryName(selectedCat ? selectedCat.name : "Category");
                localStorage.setItem("selectedCategory", JSON.stringify({
                    id: selectedCat?._id || "",
                    name: selectedCat?.name || "Category"
                }));
                if (selectedCat && selectedCat._id) {
                    await fetchSubCategories(selectedCat._id);
                }
            }
        } catch (error) {
            console.error("Category fetch error:", error);
        }
    };
    // fetchSubCategories should accept a categoryId and actually fetch subcategories
    const fetchSubCategories = async (categoryId)=>{
        try {
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSubCategories"])(categoryId);
            const subs = response?.data?.subCategories ?? [];
            setSubCategories(subs);
            // Check for subCategoryId in params or local state
            let selectedSubCatId = subCategoryId || getInitialSubCategory().id || "";
            let selectedSubCat = subs.find((sub)=>sub._id === selectedSubCatId);
            // If found, set as selected, otherwise fallback to default
            if (selectedSubCat) {
                setSelectedSubCategoryId(selectedSubCat._id);
                setSelectedSubCategoryName(selectedSubCat.name);
                localStorage.setItem("selectedSubCategory", JSON.stringify({
                    id: selectedSubCat._id,
                    name: selectedSubCat.name
                }));
            } else {
                setSelectedSubCategoryId("");
                setSelectedSubCategoryName("Sub-Category");
                localStorage.removeItem("selectedSubCategory");
            }
        } catch (error) {
            console.error("Subcategory fetch error:", error);
        }
    };
    // this remove the param from teh url
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            if (filterOccurred && (categoryId && subCategoryId || categoryId)) {
                router.replace("/search", undefined, {
                    shallow: true
                });
            }
            setFilterOccurred(false);
        }
    }["BookListing.useEffect"], [
        filterOccurred
    ]);
    /////////////////
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            // Modern Navigation Timing API
            const navEntries = window.performance.getEntriesByType("navigation");
            const navType = navEntries.length > 0 ? navEntries[0].type : null;
            // Fallback for older browsers
            console.log("navEntries", navEntries);
            const legacyNav = window.performance.navigation && window.performance.navigation.type === 1;
            if (navType === "navigate" || legacyNav) {
                const hasQuery = Object.keys(router.query || {}).length > 0;
                if (hasQuery) {
                    // replace URL to "/search" without a full reload
                    router.replace("/search", undefined, {
                        shallow: true
                    });
                }
            }
        }
    }["BookListing.useEffect"], [
        router
    ]);
    const settings = {
        dots: true,
        dotsClass: "custom_inside_dots slick-dots !bottom-4.5 md:!bottom-6",
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        adaptiveHeight: true
    };
    // Navigation of chat button
    const chatNavigationHandler = (e, itemId)=>{
        e.stopPropagation();
        const profileIndex = 3;
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateProfileComponentIndex"])(profileIndex));
        dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateItemId"])(itemId));
        let redirectpath = `${window.location.pathname}` + `${window.location.search}`;
        console.log("redirectpath", redirectpath);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            router.push("/profile/messages");
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RedirectToLoginIfNot"])(redirectpath, router);
    // router.push("/profile/messages");
    };
    // Handle click outside to close new dropdowns
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            function handleClickOutside(event) {
                if (filterRef.current && !filterRef.current.contains(event.target)) {
                    setOpenCategory(false);
                    setOpenSubCategory(false);
                }
            }
            document.addEventListener("mousedown", handleClickOutside);
            return ({
                "BookListing.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleClickOutside);
                }
            })["BookListing.useEffect"];
        }
    }["BookListing.useEffect"], [
        filterRef
    ]);
    // const bookTheItemMark = async (id) => {
    //   try {
    //     let bookMarkResponse = await bookMarkItem({itemId: id});
    //     console.log("bookMarkResponse", bookMarkResponse);
    //     if (bookMarkResponse.data?._id) {
    //       toast.success("Item added");
    //     }
    //   } catch (err) {
    //     console.log("err bookTheItemMark", err);
    //   }
    // };
    const bookTheItemMark = async (id)=>{
        try {
            setBookmarkedItems((prev)=>({
                    ...prev,
                    [id]: !prev[id]
                }));
            let bookMarkResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bookMarkItem"])({
                itemId: id
            });
            if (bookMarkResponse.data?._id) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(bookmarkedItems[id] ? "Item removed from Wishlist" : "Item Added In Wishlist");
            }
            fetchSearchData();
        } catch (err) {
            setBookmarkedItems((prev)=>({
                    ...prev,
                    [id]: !prev[id]
                }));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to update Wishlist");
        }
    };
    const returnCategorySubCat = (item)=>{
        let str = item?.categoryDoc?.name;
        if (item?.subcategoryDoc?.name) {
            str = str + `/${item?.subcategoryDoc?.name}`;
        }
        return str;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BookListing.useEffect": ()=>{
            if ("geolocation" in navigator) {
                navigator.geolocation.getCurrentPosition({
                    "BookListing.useEffect": (position)=>{
                        const { latitude, longitude } = position.coords;
                        setLocation({
                            lat: latitude,
                            lng: longitude
                        });
                    }
                }["BookListing.useEffect"], {
                    "BookListing.useEffect": (error)=>{
                        console.error("Error getting location:", error);
                    }
                }["BookListing.useEffect"]);
            } else {
                console.log("Geolocation is not supported");
            }
        }
    }["BookListing.useEffect"], []);
    const deleteBookMarkItem = async (id)=>{
        let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delete_bookMarkItem"])(id);
        if (response.status == 200) {
            fetchSearchData();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Removed Wishlist Item");
        }
    };
    const debouncePriceHandler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Debounce"])((e)=>{
        setFilterOccurred(true);
        setnumberFilter(e.target.value);
    }, 800);
    const [showMap, setShowMap] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$book$2d$listing$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].bookListingContainer} py-5  md:px-[50px] lg:px-[100px]`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
            className: "container-wrapper",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `mt-2.5 mb-10 md:mt-[50px] lg:mt-[100px] `,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: "",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-3 mx-3 ",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "col-span-3  md:col-span-1 flex items-center gap-3   ",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-4xl font-semibold",
                                                children: "Items"
                                            }, void 0, false, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 638,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-semibold mb-1 text-gray-400",
                                                children: [
                                                    "(",
                                                    pagination?.totalItems || 0,
                                                    " Results)"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 639,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 637,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: " col-span-3 md:col-span-1 w-full relative flex items-center  h-[70px] ",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$Search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 645,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 644,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "col-span-3 md:col-span-1 flex items-center relative w-full h-[70px] ",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GoogleSearchBack, {}, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 649,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 648,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 636,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-full md:flex justify-between items-center gap-[9px] relative",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-5 flex justify-between pr-4 z  items-center gap-[9px]  w-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex gap-2 mx-[10px]  no_scrollbar",
                                            ref: filterRef,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: `flex flex-row items-center gap-2 bg-white border border-[#EAEAEA] rounded-md py-2 px-3 text-[#192024] relative transition-all ease-in-out duration-200 shadow-sm ${openCategory ? "rounded-b-none" : ""}`,
                                                    onClick: ()=>{
                                                        setOpenCategory(!openCategory);
                                                        setOpenSubCategory(false);
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `text-xs leading-normal md:w-[200px] max-w-[160px] line-clamp-1 ${selectedCategoryId ? "font-medium" : "text-[#6b7280]"}`,
                                                            children: selectedCategoryName
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 670,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdKeyboardArrowDown"], {
                                                            className: `w-4 h-4 transition-transform ${openCategory ? "rotate-180" : "rotate-0"}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 677,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `absolute top-full left-0 z-[1000] bg-white w-full overflow-y-auto overflow-x-hidden h-fit flex flex-col no_scrollbar  transition-all duration-200 linear shadow-md ${openCategory ? "max-h-[300px] md:max-h-[360px]" : "max-h-0"}`,
                                                            children: categories?.map((cat, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `py-2 px-3 text-sm flex items-center justify-between cursor-pointer hover:bg-[#F7F7F7] ${selectedCategoryId === cat?._id ? "bg-[#F2F8FF]" : ""}`,
                                                                    title: cat?.name,
                                                                    onClick: ()=>{
                                                                        setSelectedCategoryId(cat?._id);
                                                                        fetchSubCategories(cat?._id);
                                                                        setSelectedCategoryName(cat?.name);
                                                                        localStorage.setItem("selectedCategory", JSON.stringify({
                                                                            id: cat?._id,
                                                                            name: cat?.name
                                                                        }));
                                                                        localStorage.removeItem("selectedSubCategory");
                                                                        // clear subcategory
                                                                        setSelectedSubCategoryId("");
                                                                        setSelectedSubCategoryName("Sub-Category");
                                                                        setOpenCategory(false);
                                                                        setFilterOccurred(true);
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "truncate pr-2",
                                                                            children: cat?.name
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 714,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        selectedCategoryId === cat?._id && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaCheck"], {
                                                                            className: "w-3.5 h-3.5 text-[#0161ab]"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 716,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, `cat-${cat?._id}`, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 690,
                                                                    columnNumber: 25
                                                                }, this))
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 682,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 661,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: `flex flex-row items-center gap-2 ${selectedCategoryId ? "bg-white" : "bg-[#F9FAFB]"} border border-[#EAEAEA] rounded-md py-2 px-3 text-[#192024] relative transition-all ease-in-out duration-200 shadow-sm ${openSubCategory ? "rounded-b-none" : ""}`,
                                                    onClick: ()=>{
                                                        if (!selectedCategoryId) {
                                                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn("Kindly select a category first.");
                                                            return;
                                                        }
                                                        setOpenSubCategory(!openSubCategory);
                                                        setOpenCategory(false);
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `text-xs leading-normal md:w-[200px] max-w-[160px] line-clamp-1 ${selectedSubCategoryId ? "font-medium" : "text-[#6b7280]"}`,
                                                            children: selectedSubCategoryName
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 739,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdKeyboardArrowDown"], {
                                                            className: `w-4 h-4 transition-transform ${openSubCategory ? "rotate-180" : "rotate-0"}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 746,
                                                            columnNumber: 21
                                                        }, this),
                                                        selectedSubCategoryId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "ml-2  text-[#ab0101] underline cursor-pointer text-xl ",
                                                            onClick: (e)=>{
                                                                e.stopPropagation();
                                                                setSelectedSubCategoryId("");
                                                                setSelectedSubCategoryName("Sub-Category");
                                                                try {
                                                                    localStorage.removeItem("selectedSubCategory");
                                                                } catch (e) {}
                                                                setFilterOccurred(true);
                                                            },
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ci$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CiCircleRemove"], {}, void 0, false, {
                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                lineNumber: 764,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 752,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `absolute top-full left-0 bg-white w-full overflow-y-auto overflow-x-hidden h-fit flex flex-col no_scrollbar z-[1000]  transition-all duration-200 linear  shadow-md ${openSubCategory ? "max-h-[300px] md:max-h-[360px]" : "max-h-0"}`,
                                                            children: subCategories?.map((sub, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `py-2 px-3 text-sm flex items-center justify-between cursor-pointer hover:bg-[#F7F7F7] ${selectedSubCategoryId === sub?._id ? "bg-[#F2F8FF]" : ""}`,
                                                                    title: sub?.name,
                                                                    onClick: ()=>{
                                                                        setSelectedSubCategoryId(sub?._id);
                                                                        setSelectedSubCategoryName(sub?.name);
                                                                        localStorage.setItem("selectedSubCategory", JSON.stringify({
                                                                            id: sub?._id,
                                                                            name: sub?.name
                                                                        }));
                                                                        setOpenSubCategory(false);
                                                                        setFilterOccurred(true);
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "truncate pr-2",
                                                                            children: sub?.name
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 799,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        selectedSubCategoryId === sub?._id && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaCheck"], {
                                                                            className: "w-3.5 h-3.5 text-[#0161ab]"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 801,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, `sub-${sub?._id}`, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 775,
                                                                    columnNumber: 25
                                                                }, this))
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 767,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 724,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 656,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "md:hidden w-[32px] h-[32px] ml-[14px] flex justify-center text-white cursor-pointer rounded-sm items-center",
                                            style: {
                                                background: "linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%)"
                                            },
                                            onClick: ()=>setShowMap(!showMap),
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa6$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaMapLocation"], {}, void 0, false, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 816,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 808,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "hidden  md:flex gap-[8px] md:ml-auto",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: `border-[0.7px] border-[#EFEFEF] w-[46px] h-[46px] flex items-center justify-center transition-colors duration-200 ease-in-out ${isList ? `bg-[#211F54]` : "bg-white"} `,
                                                    onClick: ()=>setIsList(true),
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa6$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaListUl"], {
                                                        className: "w-3.5 h-3.5",
                                                        fill: isList ? "#ffffff" : "#858585"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                        lineNumber: 826,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 820,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: `border-[0.7px] border-[#EFEFEF] w-[46px] h-[46px] flex items-center justify-center transition-colors duration-200 ease-in-out ${!isList ? `bg-[#211F54]` : "bg-white"}`,
                                                    onClick: ()=>setIsList(false),
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LuLayoutGrid"], {
                                                        className: "w-3.5 h-3.5",
                                                        stroke: !isList ? "#ffffff" : "#858585"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                        lineNumber: 838,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 832,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 819,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 655,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 654,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/search/bookListingComponent.js",
                        lineNumber: 635,
                        columnNumber: 11
                    }, this),
                    isList ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: "px-2.5 pb-10 mt-5 flex flex-col lg:flex-row gap-5 md:pb-[70px]",
                        children: [
                            booksLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ListViewSkeleton, {}, void 0, false, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 853,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col w-full lg:w-[65%] xl:w-[60%] gap-5 lg:gap-[30px] order-2 lg:order-1",
                                children: [
                                    bookList?.length > 0 ? bookList.map((item, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "border border-[#EAEAEA] p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 h-fit cursor-pointer",
                                            onClick: ()=>router.push(`/book-detail?id=${item._id}`),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-span-1 w-full aspect-[3/4] sm:aspect-[2/3] md:aspect-auto overflow-hidden bg-amber-100  rounded relative",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "absolute top-2 right-2 z-1 flex gap-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    className: `p-2 rounded-full shadow-md ${item.hasBookmarked ? "bg-white text-red-500" : "global_linear_gradient text-white hover:bg-white"} transition-colors`,
                                                                    onClick: (e)=>{
                                                                        e.stopPropagation();
                                                                        if (!userData?._id) {
                                                                            router.push("/login");
                                                                            return;
                                                                        }
                                                                        if (item.hasBookmarked && item.bookmarkDoc[0].itemId) {
                                                                            deleteBookMarkItem(item.bookmarkDoc[0]._id);
                                                                        } else {
                                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["globleBookmarkFunc"])(item.createdByDoc._id, ()=>bookTheItemMark(item._id));
                                                                        }
                                                                    },
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaHeart"], {
                                                                        size: 16
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                                        lineNumber: 897,
                                                                        columnNumber: 35
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 870,
                                                                    columnNumber: 33
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    className: "p-2 rounded-full bg-white/80 global_linear_gradient text-white hover:bg-white transition-colors",
                                                                    onClick: (e)=>{
                                                                        e.stopPropagation();
                                                                        setselectedId(item._id);
                                                                        document.getElementById("myShareModal")?.classList.remove("hidden");
                                                                    },
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiShareFatFill"], {
                                                                        size: 16
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                                        lineNumber: 910,
                                                                        columnNumber: 33
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 900,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 868,
                                                            columnNumber: 29
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CustomSlider, {
                                                            sliderSettings: settings,
                                                            className: "h-full ",
                                                            children: item.images.map((src, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "relative w-full h-full flex items-center  justify-center",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "w-full h-0 pb-[133.33%] relative",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                            src: src || "/images/book_1.jpg",
                                                                            alt: `book image ${i + 1}`,
                                                                            className: "absolute inset-0 object-cover w-full h-full rounded",
                                                                            sizes: "(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 924,
                                                                            columnNumber: 37
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                                        lineNumber: 923,
                                                                        columnNumber: 35
                                                                    }, this)
                                                                }, i, false, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 919,
                                                                    columnNumber: 33
                                                                }, this))
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 914,
                                                            columnNumber: 29
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 866,
                                                    columnNumber: 27
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "col-span-1 sm:col-span-1 md:col-span-3 flex flex-col justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                    className: "text-base md:text-xl font-semibold leading-tight line-clamp-2",
                                                                    children: item.title || "Untitled"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 939,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "mt-1 text-sm md:text-base capitalize",
                                                                    children: [
                                                                        "Category:",
                                                                        " ",
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "font-medium",
                                                                            children: returnCategorySubCat(item)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 944,
                                                                            columnNumber: 33
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 942,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$ItemRespectiveDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    item: item
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 948,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "mt-2 flex flex-wrap items-center text-xs md:text-sm gap-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "truncate max-w-[60%]",
                                                                            children: [
                                                                                "Seller:",
                                                                                " ",
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "font-medium",
                                                                                    children: item.createdByDoc?.firstName
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                                    lineNumber: 952,
                                                                                    columnNumber: 35
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 950,
                                                                            columnNumber: 33
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex items-center gap-1",
                                                                            children: [
                                                                                item?.reviewDoc[0]?.averageRating && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "bg-[#14884C] text-white text-[10px] px-2 py-[2px] rounded-sm",
                                                                                    children: item.reviewDoc[0]?.averageRating
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                                    lineNumber: 959,
                                                                                    columnNumber: 39
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "flex",
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        className: "flex items-center",
                                                                                        children: item?.reviewDoc[0]?.averageRating ? Array.from({
                                                                                            length: item?.reviewDoc[0]?.averageRating
                                                                                        }).map((item)=>{
                                                                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaStar"], {
                                                                                                    size: 12,
                                                                                                    fill: "#14884C",
                                                                                                    className: item?.reviewDoc[0]?.averageRating ? "text-yellow-600" : "text-gray-300"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                                                    lineNumber: 973,
                                                                                                    columnNumber: 51
                                                                                                }, this)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                                                lineNumber: 972,
                                                                                                columnNumber: 49
                                                                                            }, this);
                                                                                        }) : Array.from({
                                                                                            length: 5
                                                                                        }).map((item)=>{
                                                                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaStar"], {
                                                                                                    size: 12,
                                                                                                    fill: "gray",
                                                                                                    className: item?.reviewDoc[0]?.averageRating ? "text-yellow-600" : "text-gray-300"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                                                    lineNumber: 991,
                                                                                                    columnNumber: 51
                                                                                                }, this)
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                                                lineNumber: 990,
                                                                                                columnNumber: 49
                                                                                            }, this);
                                                                                        })
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                                                        lineNumber: 964,
                                                                                        columnNumber: 39
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                                    lineNumber: 963,
                                                                                    columnNumber: 37
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 957,
                                                                            columnNumber: 35
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 949,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "mt-2 text-xs md:text-sm truncate",
                                                                    title: item.address?.formatted_address || item.address,
                                                                    children: [
                                                                        "Location:",
                                                                        " ",
                                                                        item.address?.formatted_address || item.address
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 1010,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        title: item?.address,
                                                                        className: "line-clamp-1",
                                                                        children: [
                                                                            "Parish: ",
                                                                            item?.address?.parish || "NA"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                                        lineNumber: 1023,
                                                                        columnNumber: 33
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 1022,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "mt-2 text-xs md:text-sm",
                                                                    children: [
                                                                        "Date:",
                                                                        " ",
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "font-medium",
                                                                            children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(item.createdAt).format("DD-MM-YYYY")
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 1032,
                                                                            columnNumber: 33
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 1030,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 938,
                                                            columnNumber: 29
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mt-4 flex flex-wrap justify-between items-center gap-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-base md:text-3xl font-bold truncate",
                                                                    children: [
                                                                        "J$ ",
                                                                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatWithCommas"])(item.price)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 1040,
                                                                    columnNumber: 31
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    className: "py-2 px-4 text-xs md:text-sm font-semibold rounded-full text-white global_linear_gradient",
                                                                    onClick: (e)=>{
                                                                        e.stopPropagation();
                                                                        let user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userDataFromLocal"])() || "{}";
                                                                        if (item.createdByDoc?._id === user._id) {
                                                                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("You cannot message yourself");
                                                                        } else {
                                                                            chatNavigationHandler(e, item._id);
                                                                        }
                                                                    },
                                                                    children: "Chat Now"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 1043,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1039,
                                                            columnNumber: 29
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 937,
                                                    columnNumber: 27
                                                }, this)
                                            ]
                                        }, `book-list-${idx}`, true, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 858,
                                            columnNumber: 25
                                        }, this)) : !booksLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-col items-center justify-center space-y-5",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$No_result_found_search_page$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$No_result_found_search_page$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                alt: "No Result Found",
                                                width: 300,
                                                height: 300,
                                                objectFit: "cover"
                                            }, void 0, false, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 1064,
                                                columnNumber: 27
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center space-y-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "text-2xl font-medium",
                                                        children: "No Results Found"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                        lineNumber: 1072,
                                                        columnNumber: 29
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-lg text-[#838282]",
                                                        children: [
                                                            "Nothing on our list yet.",
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                lineNumber: 1077,
                                                                columnNumber: 31
                                                            }, this),
                                                            "It's never too late to change it 😊"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                        lineNumber: 1075,
                                                        columnNumber: 29
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 1071,
                                                columnNumber: 27
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "mt-4 py-3 px-8 text-base font-medium text-white rounded-full global_linear_gradient",
                                                onClick: ()=>router.push("/become-seller"),
                                                children: "Create A New Listing"
                                            }, void 0, false, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 1081,
                                                columnNumber: 27
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 1063,
                                        columnNumber: 25
                                    }, this),
                                    pagination.showLoader && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ListViewSkeleton, {
                                        count: 4
                                    }, void 0, false, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 1091,
                                        columnNumber: 45
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 855,
                                columnNumber: 17
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `w-full md:block lg:w-[35%] xl:w-[40%] order-1 lg:order-2 ${!showMap ? " hidden" : ""}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MapView, {
                                    width: "100%",
                                    height: "500px",
                                    data: bookList,
                                    center: userLocationData?.isParishSelection ? null // Let MapView handle parish selection internally
                                     : locationData?.latitude && locationData?.longitude ? {
                                        lat: locationData.latitude,
                                        lng: locationData.longitude
                                    } : null,
                                    searchCoordinates: searchCoordinates,
                                    fullScreen: showMap,
                                    onExitFullScreen: ()=>setShowMap(false)
                                }, void 0, false, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 1101,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 1096,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/search/bookListingComponent.js",
                        lineNumber: 850,
                        columnNumber: 13
                    }, this) : // Grid View
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[33px] mt-5 pb-10 md:pb-[70px]",
                        children: [
                            booksLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GridViewSkeletonContainer, {}, void 0, false, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 1127,
                                columnNumber: 17
                            }, this) : bookList?.length > 0 ? bookList?.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative p-[13px] pb-4 border border-[#EAEAEA]",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute top-0 left-[-8] z-10"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 1134,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CustomSlider, {
                                            sliderSettings: settings,
                                            className: "",
                                            children: // [
                                            //   item?.backCoverImage || item?.coverImage,
                                            //   item?.frontCoverImage,
                                            //   item?.middlePageImage,
                                            //   item?.spineImage,
                                            // ]
                                            item?.images.map((src, imageIdx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    // className="relative  h-[218px] aspect-[3/4] bg-[#73877B] py-2.5 !flex justify-center items-center box-border"
                                                    className: "relative  h-[218px] aspect-[3/4] bg-[#FFFBF6] py-2.5 !flex justify-center items-center box-border",
                                                    children: [
                                                        userData?._id && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `p-2.5 rounded-full absolute top-12 right-2.5 z-1 bg-[#fff6] cursor-pointer ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$book$2d$listing$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].boxShadow}`,
                                                            onClick: (e)=>{
                                                                // e.preventDefault()
                                                                // e.stopPropagation()
                                                                if (userData?._id && !item.hasBookmarked) {
                                                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["globleBookmarkFunc"])(item?.createdBy?._id, ()=>bookTheItemMark(item._id));
                                                                } else {
                                                                    if (item.bookmarkDoc.length) {
                                                                        deleteBookMarkItem(item.bookmarkDoc[0]._id);
                                                                    }
                                                                }
                                                            },
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HiOutlineHeart"], {
                                                                stroke: "#5F5F5F",
                                                                fill: item.hasBookmarked ? "#e4000d" : "white",
                                                                width: "43",
                                                                height: "43"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                lineNumber: 1175,
                                                                columnNumber: 33
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1156,
                                                            columnNumber: 31
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            className: `p-2.5 rounded-full absolute top-2 right-2.5 z-1 bg-[#fff6] cursor-pointer ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$book$2d$listing$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].boxShadow}`,
                                                            onClick: (e)=>{
                                                                e.stopPropagation();
                                                                setselectedId(item._id);
                                                                document.getElementById("myShareModal")?.classList.remove("hidden");
                                                            },
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiShareFatFill"], {
                                                                size: 16
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                lineNumber: 1196,
                                                                columnNumber: 31
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1186,
                                                            columnNumber: 29
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative  h-full flex items-center justify-center cursor-pointer",
                                                            onClick: (e)=>{
                                                                e.stopPropagation();
                                                                router.push(`/book-detail?id=${item?._id}`);
                                                            },
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                // src={item?.coverImage || item?.backCoverImage}
                                                                src: src,
                                                                alt: `book image-${imageIdx}`,
                                                                // fill
                                                                className: "object-cover w-[90%] h-[80%] rounded "
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                lineNumber: 1205,
                                                                columnNumber: 31
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1198,
                                                            columnNumber: 29
                                                        }, this)
                                                    ]
                                                }, `image-${imageIdx}`, true, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 1150,
                                                    columnNumber: 27
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 1141,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-[11px]",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "cursor-pointer",
                                                    onClick: (e)=>{
                                                        e.stopPropagation();
                                                        router.push(`/book-detail?id=${item?._id}`);
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                            className: "text-xl leading-normal font-semibold line-clamp-1 overflow-hidden",
                                                            title: item?.title,
                                                            children: item?.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1227,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "mt-0.5 text-[15px] font-light leading-normal line-clamp-1",
                                                            children: [
                                                                "Category: ",
                                                                returnCategorySubCat(item)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1234,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$search$2f$ItemRespectiveDetails$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            item: item
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1237,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                title: item?.address,
                                                                className: "line-clamp-1",
                                                                children: [
                                                                    "Location:",
                                                                    " ",
                                                                    item?.address.formatted_address || item?.address
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                lineNumber: 1240,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1239,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                title: item?.address,
                                                                className: "line-clamp-1",
                                                                children: [
                                                                    "Parish: ",
                                                                    item?.address.parish || "NA"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                lineNumber: 1246,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1245,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]",
                                                            children: [
                                                                "Date: ",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "font-medium",
                                                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(item?.createdAt).format("DD-MM-YYYY")
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 1253,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1251,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex flex-wrap gap-2 mt-[11.3px] text-base leading-5 items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "line-clamp-1 max-w-[60%] md:max-w-[80%]",
                                                                    children: [
                                                                        "Seller:",
                                                                        " ",
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "font-medium",
                                                                            children: item?.createdByDoc?.firstName + " " + item?.createdByDoc?.lastName
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 1261,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 1259,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center gap-1",
                                                                    children: [
                                                                        item?.reviewDoc[0]?.averageRating && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "bg-[#14884C] text-white text-[10px] px-2 py-[2px] rounded-sm",
                                                                            children: item.reviewDoc[0]?.averageRating
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 1270,
                                                                            columnNumber: 33
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "flex items-center",
                                                                                children: item?.reviewDoc[0]?.averageRating ? Array.from({
                                                                                    length: item?.reviewDoc[0]?.averageRating
                                                                                }).map((item)=>{
                                                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaStar"], {
                                                                                            size: 12,
                                                                                            fill: "#14884C",
                                                                                            className: item?.reviewDoc[0]?.averageRating ? "text-yellow-600" : "text-gray-300"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                                            lineNumber: 1283,
                                                                                            columnNumber: 45
                                                                                        }, this)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                                                        lineNumber: 1282,
                                                                                        columnNumber: 43
                                                                                    }, this);
                                                                                }) : Array.from({
                                                                                    length: 5
                                                                                }).map((item)=>{
                                                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaStar"], {
                                                                                            size: 12,
                                                                                            fill: "gray",
                                                                                            className: item?.reviewDoc[0]?.averageRating ? "text-yellow-600" : "text-gray-300"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                                            lineNumber: 1301,
                                                                                            columnNumber: 45
                                                                                        }, this)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                                                        lineNumber: 1300,
                                                                                        columnNumber: 43
                                                                                    }, this);
                                                                                })
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                                                lineNumber: 1275,
                                                                                columnNumber: 33
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                                            lineNumber: 1274,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                                    lineNumber: 1268,
                                                                    columnNumber: 29
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1258,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mt-[11px] text-[15px] leading-normal flex gap-[11px] items-center"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1327,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 1220,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-4 flex gap-[34px] justify-between items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            className: "py-[9px] w-[120px] px-[20px] text-base font-semibold leading-6 text-center text-white rounded-full global_linear_gradient",
                                                            onClick: (e)=>{
                                                                e.stopPropagation();
                                                                let userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
                                                                if (item?.createdByDoc?._id == userData?._id) {
                                                                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("You can not send message to your self");
                                                                } else {
                                                                    chatNavigationHandler(e, item?._id);
                                                                }
                                                            },
                                                            children: "Chat Now"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1330,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            title: item?.price,
                                                            className: "text-xl font-semibold leading-normal line-clamp-1 truncate max-w-[180px] md:max-w-[120px] text-ellipsis",
                                                            children: [
                                                                "J$",
                                                                item?.price
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/search/bookListingComponent.js",
                                                            lineNumber: 1349,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/search/bookListingComponent.js",
                                                    lineNumber: 1329,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 1219,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, `grid-card-${index}`, true, {
                                    fileName: "[project]/app/search/bookListingComponent.js",
                                    lineNumber: 1130,
                                    columnNumber: 19
                                }, this)) : // No Data Found
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col col-span-full justify-center items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative overflow-hidden w-[372px] h-[372px]",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$No_result_found_search_page$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$No_result_found_search_page$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                            alt: "No Result Found",
                                            fill: true,
                                            objectFit: "cover",
                                            sizes: "100w"
                                        }, void 0, false, {
                                            fileName: "[project]/app/search/bookListingComponent.js",
                                            lineNumber: 1363,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 1362,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-col gap-2.5 mt-5 justify-center items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "text-2xl leading-normal font-medium",
                                                children: "No Result Found"
                                            }, void 0, false, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 1373,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-center text-lg leading-normal text-[#838282]",
                                                children: [
                                                    "Nothing on our list yet.",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                        fileName: "[project]/app/search/bookListingComponent.js",
                                                        lineNumber: 1378,
                                                        columnNumber: 23
                                                    }, this),
                                                    "It's never too late to change it 😊"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 1376,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 1372,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        className: "mt-[30px] global_linear_gradient text-white py-3 px-[30px] rounded-full text-[15px] font-medium -tracking-[0.3px] leading-[21px]",
                                        onClick: ()=>router.push("/become-seller"),
                                        children: "Create A New Listing"
                                    }, void 0, false, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 1383,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 1361,
                                columnNumber: 17
                            }, this),
                            pagination.showLoader && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GridViewSkeleton, {
                                count: 8
                            }, void 0, false, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 1394,
                                columnNumber: 41
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/search/bookListingComponent.js",
                        lineNumber: 1123,
                        columnNumber: 13
                    }, this),
                    pagination?.page < pagination?.totalPages && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-center my-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "py-4 px-8 text-xs md:text-sm font-semibold rounded-full text-white global_linear_gradient flex items-center justify-center min-w-[120px]",
                            onClick: ()=>{
                                if (!pagination.showLoader) {
                                    setPagination((prev)=>({
                                            ...prev,
                                            page: prev.page + 1,
                                            showLoader: true
                                        }));
                                }
                            },
                            disabled: pagination.showLoader,
                            children: pagination.showLoader ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "animate-spin h-5 w-5 text-white",
                                        xmlns: "http://www.w3.org/2000/svg",
                                        fill: "none",
                                        viewBox: "0 0 24 24",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                className: "opacity-25",
                                                cx: "12",
                                                cy: "12",
                                                r: "10",
                                                stroke: "currentColor",
                                                strokeWidth: "4"
                                            }, void 0, false, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 1420,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                className: "opacity-75",
                                                fill: "currentColor",
                                                d: "M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                                            }, void 0, false, {
                                                fileName: "[project]/app/search/bookListingComponent.js",
                                                lineNumber: 1428,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/search/bookListingComponent.js",
                                        lineNumber: 1414,
                                        columnNumber: 21
                                    }, this),
                                    "Loading..."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/search/bookListingComponent.js",
                                lineNumber: 1413,
                                columnNumber: 19
                            }, this) : "Load More"
                        }, void 0, false, {
                            fileName: "[project]/app/search/bookListingComponent.js",
                            lineNumber: 1399,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/search/bookListingComponent.js",
                        lineNumber: 1398,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$ShareCard$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        url: `/book-detail?id=${selectedId}`
                    }, void 0, false, {
                        fileName: "[project]/app/search/bookListingComponent.js",
                        lineNumber: 1442,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "px-2.5",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(BuyAndSellComponent, {}, void 0, false, {
                            fileName: "[project]/app/search/bookListingComponent.js",
                            lineNumber: 1445,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/search/bookListingComponent.js",
                        lineNumber: 1444,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/search/bookListingComponent.js",
                lineNumber: 633,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/search/bookListingComponent.js",
            lineNumber: 632,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/search/bookListingComponent.js",
        lineNumber: 629,
        columnNumber: 5
    }, this);
}
_s(BookListing, "/Q1ft9+p3T/ALrbf4bMu8rIqcK8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"]
    ];
});
_c5 = BookListing;
const __TURBOPACK__default__export__ = BookListing;
var _c, _c1, _c2, _c3, _c4, _c5;
__turbopack_context__.k.register(_c, "CustomSlider");
__turbopack_context__.k.register(_c1, "MapView");
__turbopack_context__.k.register(_c2, "GoogleSearch$dynamic");
__turbopack_context__.k.register(_c3, "GoogleSearch");
__turbopack_context__.k.register(_c4, "BuyAndSellComponent");
__turbopack_context__.k.register(_c5, "BookListing");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_fcb616ec._.js.map