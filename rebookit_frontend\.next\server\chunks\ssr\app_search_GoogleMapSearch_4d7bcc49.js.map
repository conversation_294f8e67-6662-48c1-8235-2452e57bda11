{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/search/GoogleMapSearch.js"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport { useLoadScript } from \"@react-google-maps/api\";\r\nimport { FaChevronDown } from \"react-icons/fa6\";\r\nimport { ParishesListEnum, ParishCoordinatesMap } from \"../config/constant\";\r\nimport { RxCross2 } from \"react-icons/rx\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport {\r\n  getCurrentLocationAndAddress,\r\n  ipBasedLocationFinder,\r\n  parseGeocodeResponse,\r\n} from \"../utils/utils\";\r\nimport { MdMyLocation } from \"react-icons/md\";\r\nimport { PiMapPinAreaDuotone } from \"react-icons/pi\";\r\nimport { toast } from \"react-toastify\";\r\nimport { updateUserLocationData } from \"../redux/slices/storeSlice\";\r\n\r\n// Simple spinner component\r\nconst Spinner = () => (\r\n  <svg\r\n    className=\"animate-spin h-5 w-5 text-blue-500\"\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    fill=\"none\"\r\n    viewBox=\"0 0 24 24\"\r\n    style={{ minWidth: \"20px\", minHeight: \"20px\" }}\r\n  >\r\n    <circle\r\n      className=\"opacity-25\"\r\n      cx=\"12\"\r\n      cy=\"12\"\r\n      r=\"10\"\r\n      stroke=\"currentColor\"\r\n      strokeWidth=\"4\"\r\n    ></circle>\r\n    <path\r\n      className=\"opacity-75\"\r\n      fill=\"currentColor\"\r\n      d=\"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\r\n    ></path>\r\n  </svg>\r\n);\r\n\r\nconst libraries = [\"places\"];\r\n\r\nfunction debounce(fn, delay) {\r\n  let timer;\r\n  return (...args) => {\r\n    if (timer) clearTimeout(timer);\r\n    timer = setTimeout(() => fn(...args), delay);\r\n  };\r\n}\r\n\r\nconst CustomAutocomplete = ({}) => {\r\n  const { isLoaded } = useLoadScript({\r\n    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY,\r\n    libraries,\r\n  });\r\n  const [isFocused, setIsFocused] = useState(false);\r\n  const filterRef = useRef(null);\r\n  const dispatch = useDispatch();\r\n  const [suggestions, setSuggestions] = useState([]);\r\n  const [expanded, setExpanded] = useState(false);\r\n  const [showLocationBlockedModal, setShowLocationBlockedModal] =\r\n    useState(false);\r\n\r\n  const userLocationData = useSelector(\r\n    (state) => state.storeData.userLocationData\r\n  );\r\n\r\n  const [locationData, setLocationData] = useState({ locality: \"\" });\r\n\r\n  // For Google AutocompleteService instance\r\n  const autocompleteServiceRef = useRef(null);\r\n\r\n  // Debounce for input change\r\n  const debouncedGetPredictions = useRef();\r\n\r\n  // Loading state for current location\r\n  const [isGettingCurrentLocation, setIsGettingCurrentLocation] =\r\n    useState(false);\r\n  const [isSearchingSuggestions, setIsSearchingSuggestions] = useState(false);\r\n  const latestQuerySeqRef = useRef(0);\r\n  const latestQueryValueRef = useRef(\"\");\r\n\r\n  // Handle click outside to close dropdown\r\n  useEffect(() => {\r\n    function handleClickOutside(event) {\r\n      if (filterRef.current && !filterRef.current.contains(event.target)) {\r\n        setIsFocused(false);\r\n        setExpanded(false);\r\n      }\r\n    }\r\n    if (isFocused || expanded) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    } else {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [isFocused, expanded]);\r\n\r\n  useEffect(() => {\r\n    if (isLoaded && window.google && !autocompleteServiceRef.current) {\r\n      autocompleteServiceRef.current =\r\n        new window.google.maps.places.AutocompleteService();\r\n      // If user already typed something before script loaded, trigger a search now\r\n      if (locationData?.locality) {\r\n        debouncedGetPredictions.current?.(locationData.locality);\r\n      }\r\n    }\r\n  }, [isLoaded]);\r\n\r\n  useEffect(() => {\r\n    // If userLocationData is not present in the store, fetch and store it\r\n    if (\r\n      !userLocationData ||\r\n      !userLocationData.latitude ||\r\n      !userLocationData.longitude\r\n    ) {\r\n      (async () => {\r\n        try {\r\n          const locationData = await ipBasedLocationFinder();\r\n          dispatch(updateUserLocationData(locationData));\r\n          setLocationData({\r\n            locality: locationData.locality || \"\",\r\n            latitude: locationData.latitude || \"\",\r\n            longitude: locationData.longitude || \"\",\r\n          });\r\n        } catch (e) {\r\n          console.warn(\"Failed to get user location data:\", e);\r\n        }\r\n      })();\r\n    } else {\r\n      setLocationData({\r\n        locality: userLocationData.locality || \"\",\r\n        latitude: userLocationData.latitude || \"\",\r\n        longitude: userLocationData.longitude || \"\",\r\n      });\r\n    }\r\n  }, [userLocationData, dispatch]);\r\n\r\n  // Debounced function to get place predictions\r\n  useEffect(() => {\r\n    debouncedGetPredictions.current = debounce((value) => {\r\n      const trimmed = (value || \"\").trim();\r\n      alert(\"ka\")\r\n      if (\r\n        \r\n        autocompleteServiceRef.current &&\r\n        window.google &&\r\n        window.google.maps &&\r\n        window.google.maps.places &&\r\n        trimmed\r\n      ) {\r\n        alert(\"dfddsf\")\r\n        const currentSeq = ++latestQuerySeqRef.current;\r\n        const currentQuery = trimmed;\r\n        latestQueryValueRef.current = currentQuery;\r\n        try {\r\n          autocompleteServiceRef.current.getPlacePredictions(\r\n            { input: currentQuery },\r\n            (predictions, status) => {\r\n              // Ignore stale callbacks\r\n              if (currentSeq !== latestQuerySeqRef.current) return;\r\n              if (\r\n                status === window.google.maps.places.PlacesServiceStatus.OK &&\r\n                predictions\r\n              ) {\r\n                setSuggestions(predictions);\r\n              } else {\r\n                setSuggestions([]);\r\n              }\r\n              setIsSearchingSuggestions(false);\r\n            }\r\n          );\r\n        } catch (e) {\r\n          // Ensure loader is turned off on errors\r\n          if (currentSeq === latestQuerySeqRef.current) {\r\n            setIsSearchingSuggestions(false);\r\n            setSuggestions([]);\r\n          }\r\n        }\r\n      } else {\r\n        setSuggestions([]);\r\n        setIsSearchingSuggestions(false);\r\n      }\r\n    }, 300);\r\n  }, [isLoaded]);\r\n\r\n  const handleInputChange = (e) => {\r\n    const value = e.target.value;\r\n    setLocationData((pre) => ({ ...pre, locality: value }));\r\n    // Start showing loader immediately when user types non-empty text\r\n    if ((value || \"\").trim()) {\r\n      setIsSearchingSuggestions(true);\r\n    }\r\n    if (!value || !(value || \"\").trim()) {\r\n      setSuggestions([]);\r\n      setIsSearchingSuggestions(false);\r\n    }\r\n    if (debouncedGetPredictions.current) {\r\n      debouncedGetPredictions.current(value);\r\n    }\r\n  };\r\n\r\n  const handleSelect = async (prediction) => {\r\n    if (!window.google || !window.google.maps) return;\r\n    const geocoder = new window.google.maps.Geocoder();\r\n    await geocoder.geocode(\r\n      { placeId: prediction.place_id },\r\n      (results, status) => {\r\n        if (\r\n          status === \"OK\" &&\r\n          results &&\r\n          results[0] &&\r\n          results[0].geometry?.location\r\n        ) {\r\n          const { lat, lng } = results[0].geometry.location;\r\n          const locality = prediction.description;\r\n\r\n          const locationPayload = {\r\n            locality,\r\n            latitude: typeof lat === \"function\" ? lat() : lat,\r\n            longitude: typeof lng === \"function\" ? lng() : lng,\r\n          };\r\n          dispatch(updateUserLocationData(locationPayload));\r\n          setLocationData(locationPayload);\r\n          setSuggestions([]);\r\n          setIsSearchingSuggestions(false);\r\n        }\r\n      }\r\n    );\r\n    setIsFocused(false);\r\n    setExpanded(false);\r\n  };\r\n\r\n  const handleParishSelect = (parish) => {\r\n    const parishCoordinates = ParishCoordinatesMap[parish];\r\n    if (parishCoordinates) {\r\n      const locationPayload = {\r\n        locality: parish,\r\n        latitude: parishCoordinates.lat,\r\n        longitude: parishCoordinates.lng,\r\n        isParishSelection: true,\r\n      };\r\n      dispatch(updateUserLocationData(locationPayload));\r\n      setLocationData(locationPayload);\r\n      setIsFocused(false);\r\n      setExpanded(false);\r\n    }\r\n  };\r\n\r\n  const handleGetCurrentLocation = async () => {\r\n    if (navigator.permissions && navigator.permissions.query) {\r\n      try {\r\n        const result = await navigator.permissions.query({\r\n          name: \"geolocation\",\r\n        });\r\n        if (result.state === \"denied\") {\r\n          setShowLocationBlockedModal(true);\r\n          return;\r\n        }\r\n      } catch (err) {\r\n        setShowLocationBlockedModal(true);\r\n      }\r\n    }\r\n\r\n    setIsGettingCurrentLocation(true);\r\n\r\n    const fetchAndSetLocation = async () => {\r\n      try {\r\n        const addressData = await getCurrentLocationAndAddress(\r\n          \"getfullAddress\"\r\n        );\r\n        const parsedAddress = parseGeocodeResponse(addressData);\r\n\r\n        if (parsedAddress) {\r\n          // Save in redux in the required format\r\n          const locationData = {\r\n            locality: parsedAddress.locality || \"\",\r\n            latitude: parsedAddress.latitude || \"\",\r\n            longitude: parsedAddress.longitude || \"\",\r\n            currentLocation: true,\r\n          };\r\n          dispatch(updateUserLocationData(locationData));\r\n        }\r\n      } catch (err) {\r\n        toast.error(\"Failed to get current location. Please try again.\");\r\n      } finally {\r\n        setIsGettingCurrentLocation(false);\r\n      }\r\n    };\r\n\r\n    fetchAndSetLocation();\r\n  };\r\n\r\n  return (\r\n    <div className=\"md:ms-2 absolute z-[10]  overflow-hidden  border border-[#ccc] rounded-xl   w-full top-[10px]  \">\r\n      <div\r\n        ref={filterRef}\r\n        className={`  border-[#ccc]  bg-white  transition-all duration-500 ease-in-out  ${\r\n          expanded ? \"h-[300px]\" : \"\"\r\n        }`}\r\n      >\r\n        <div className=\"flex items-center relative px-2\">\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search for a location \"\r\n            className=\"w-full p-2 !h-[42px] outline-none cursor-pointer\"\r\n            style={{ height: \"42px\" }}\r\n            value={locationData?.locality || \"\"}\r\n            onFocus={() => {\r\n              setIsFocused(true);\r\n              setExpanded(true);\r\n            }}\r\n            onChange={handleInputChange}\r\n          />\r\n          <div className=\"flex items-center gap-2 ml-2\">\r\n            {/* Show spinner if loading current location */}\r\n            {(isGettingCurrentLocation || isSearchingSuggestions) && (\r\n              <span className=\"mr-1\">\r\n                <Spinner />\r\n              </span>\r\n            )}\r\n            <FaChevronDown\r\n              onClick={() => {\r\n                setIsFocused((prev) => !prev);\r\n                setExpanded((prev) => !prev);\r\n              }}\r\n              className={`transform cursor-pointer hover:text-blue-500 transition-transform duration-300 ${\r\n                expanded ? \"rotate-180\" : \"\"\r\n              }`}\r\n            />\r\n          </div>\r\n        </div>\r\n        {isFocused &&\r\n          (suggestions?.length > 0 ? (\r\n            <ul className=\" z-10 bg-white w-full mt-1  max-h-[250px] overflow-auto\">\r\n              {suggestions?.map((prediction) => (\r\n                <li\r\n                  key={prediction.place_id}\r\n                  className=\"px-4 py-2 hover:bg-gray-100 cursor-pointer\"\r\n                  onClick={() => {\r\n                    handleSelect(prediction);\r\n                  }}\r\n                >\r\n                  {prediction.description}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          ) : (\r\n            <div className=\"absolute top-[40] z-10 bg-white w-full mt-1 max-h-[250px] overflow-auto\">\r\n              {isSearchingSuggestions ? (\r\n                <div className=\"px-4 py-3 flex items-center gap-2 text-gray-600\">\r\n                  <Spinner />\r\n                  <span>Searching...</span>\r\n                </div>\r\n              ) : (\r\n                <>\r\n                  {/* Get Current Location Button */}\r\n                  <div\r\n                    className={`px-4 py-3 border-b border-gray-200 hover:bg-blue-50 cursor-pointer flex items-center gap-2 text-blue-600 font-medium ${\r\n                      isGettingCurrentLocation\r\n                        ? \"opacity-60 pointer-events-none\"\r\n                        : \"\"\r\n                    }`}\r\n                    onClick={async () => {\r\n                      if (!isGettingCurrentLocation) {\r\n                        handleGetCurrentLocation();\r\n                        setIsFocused(false);\r\n                        setExpanded(false);\r\n                      }\r\n                    }}\r\n                  >\r\n                    <MdMyLocation size={18} />\r\n                    <span>Get Current Location</span>\r\n                    {isGettingCurrentLocation && (\r\n                      <span className=\"ml-2\">\r\n                        <Spinner />\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"px-4 py-2 text-[18px] mt-2 bg-gray-200\">\r\n                    {\" \"}\r\n                    Popular Location\r\n                  </div>\r\n                  {Object.values(ParishesListEnum).map((item) => (\r\n                    <div\r\n                      key={item}\r\n                      value={item}\r\n                      onClick={() => {\r\n                        handleParishSelect(item);\r\n                      }}\r\n                      className={`${\r\n                        userLocationData?.locality == item ? \"bg-gray-100\" : \"\"\r\n                      } flex items-center gap-2  px-4 py-2 hover:bg-gray-100 cursor-pointer`}\r\n                    >\r\n                      <PiMapPinAreaDuotone />\r\n                      {item}\r\n                    </div>\r\n                  ))}\r\n                </>\r\n              )}\r\n            </div>\r\n          ))}\r\n      </div>\r\n      {/* Location Blocked Modal */}\r\n      {showLocationBlockedModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg p-6 max-w-md mx-4 relative\">\r\n            {/* Close button */}\r\n            <button\r\n              className=\"absolute top-4 right-4 text-gray-400 hover:text-gray-600\"\r\n              onClick={() => setShowLocationBlockedModal(false)}\r\n            >\r\n              <RxCross2 size={24} />\r\n            </button>\r\n\r\n            {/* Modal content */}\r\n            <div className=\"text-center\">\r\n              <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">\r\n                Geolocation is blocked\r\n              </h2>\r\n              <p className=\"text-gray-600 mb-6\">\r\n                Looks like your geolocation permissions are blocked. Please,\r\n                provide geolocation access in your browser settings.\r\n              </p>\r\n              <button\r\n                className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\r\n                onClick={() => setShowLocationBlockedModal(false)}\r\n              >\r\n                OK\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomAutocomplete;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;;;;;;;;;;;;;AAEA,2BAA2B;AAC3B,MAAM,UAAU,kBACd,8OAAC;QACC,WAAU;QACV,OAAM;QACN,MAAK;QACL,SAAQ;QACR,OAAO;YAAE,UAAU;YAAQ,WAAW;QAAO;;0BAE7C,8OAAC;gBACC,WAAU;gBACV,IAAG;gBACH,IAAG;gBACH,GAAE;gBACF,QAAO;gBACP,aAAY;;;;;;0BAEd,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,GAAE;;;;;;;;;;;;AAKR,MAAM,YAAY;IAAC;CAAS;AAE5B,SAAS,SAAS,EAAE,EAAE,KAAK;IACzB,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,OAAO,aAAa;QACxB,QAAQ,WAAW,IAAM,MAAM,OAAO;IACxC;AACF;AAEA,MAAM,qBAAqB,CAAC,EAAE;IAC5B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE;QACjC,gBAAgB;QAChB;IACF;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEX,MAAM,mBAAmB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EACjC,CAAC,QAAU,MAAM,SAAS,CAAC,gBAAgB;IAG7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,UAAU;IAAG;IAEhE,0CAA0C;IAC1C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEtC,4BAA4B;IAC5B,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAErC,qCAAqC;IACrC,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEnC,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAK;YAC/B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;gBAClE,aAAa;gBACb,YAAY;YACd;QACF;QACA,IAAI,aAAa,UAAU;YACzB,SAAS,gBAAgB,CAAC,aAAa;QACzC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;QACA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;QAAW;KAAS;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,MAAM,IAAI,CAAC,uBAAuB,OAAO,EAAE;YAChE,uBAAuB,OAAO,GAC5B,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB;YACnD,6EAA6E;YAC7E,IAAI,cAAc,UAAU;gBAC1B,wBAAwB,OAAO,GAAG,aAAa,QAAQ;YACzD;QACF;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sEAAsE;QACtE,IACE,CAAC,oBACD,CAAC,iBAAiB,QAAQ,IAC1B,CAAC,iBAAiB,SAAS,EAC3B;YACA,CAAC;gBACC,IAAI;oBACF,MAAM,eAAe,MAAM,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD;oBAC/C,SAAS,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE;oBAChC,gBAAgB;wBACd,UAAU,aAAa,QAAQ,IAAI;wBACnC,UAAU,aAAa,QAAQ,IAAI;wBACnC,WAAW,aAAa,SAAS,IAAI;oBACvC;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,qCAAqC;gBACpD;YACF,CAAC;QACH,OAAO;YACL,gBAAgB;gBACd,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,WAAW,iBAAiB,SAAS,IAAI;YAC3C;QACF;IACF,GAAG;QAAC;QAAkB;KAAS;IAE/B,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wBAAwB,OAAO,GAAG,SAAS,CAAC;YAC1C,MAAM,UAAU,CAAC,SAAS,EAAE,EAAE,IAAI;YAClC,MAAM;YACN,IAEE,uBAAuB,OAAO,IAC9B,OAAO,MAAM,IACb,OAAO,MAAM,CAAC,IAAI,IAClB,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,IACzB,SACA;gBACA,MAAM;gBACN,MAAM,aAAa,EAAE,kBAAkB,OAAO;gBAC9C,MAAM,eAAe;gBACrB,oBAAoB,OAAO,GAAG;gBAC9B,IAAI;oBACF,uBAAuB,OAAO,CAAC,mBAAmB,CAChD;wBAAE,OAAO;oBAAa,GACtB,CAAC,aAAa;wBACZ,yBAAyB;wBACzB,IAAI,eAAe,kBAAkB,OAAO,EAAE;wBAC9C,IACE,WAAW,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,IAC3D,aACA;4BACA,eAAe;wBACjB,OAAO;4BACL,eAAe,EAAE;wBACnB;wBACA,0BAA0B;oBAC5B;gBAEJ,EAAE,OAAO,GAAG;oBACV,wCAAwC;oBACxC,IAAI,eAAe,kBAAkB,OAAO,EAAE;wBAC5C,0BAA0B;wBAC1B,eAAe,EAAE;oBACnB;gBACF;YACF,OAAO;gBACL,eAAe,EAAE;gBACjB,0BAA0B;YAC5B;QACF,GAAG;IACL,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,gBAAgB,CAAC,MAAQ,CAAC;gBAAE,GAAG,GAAG;gBAAE,UAAU;YAAM,CAAC;QACrD,kEAAkE;QAClE,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,IAAI;YACxB,0BAA0B;QAC5B;QACA,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,EAAE,IAAI,IAAI;YACnC,eAAe,EAAE;YACjB,0BAA0B;QAC5B;QACA,IAAI,wBAAwB,OAAO,EAAE;YACnC,wBAAwB,OAAO,CAAC;QAClC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,IAAI,EAAE;QAC3C,MAAM,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ;QAChD,MAAM,SAAS,OAAO,CACpB;YAAE,SAAS,WAAW,QAAQ;QAAC,GAC/B,CAAC,SAAS;YACR,IACE,WAAW,QACX,WACA,OAAO,CAAC,EAAE,IACV,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,UACrB;gBACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ;gBACjD,MAAM,WAAW,WAAW,WAAW;gBAEvC,MAAM,kBAAkB;oBACtB;oBACA,UAAU,OAAO,QAAQ,aAAa,QAAQ;oBAC9C,WAAW,OAAO,QAAQ,aAAa,QAAQ;gBACjD;gBACA,SAAS,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE;gBAChC,gBAAgB;gBAChB,eAAe,EAAE;gBACjB,0BAA0B;YAC5B;QACF;QAEF,aAAa;QACb,YAAY;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,oBAAoB,yHAAA,CAAA,uBAAoB,CAAC,OAAO;QACtD,IAAI,mBAAmB;YACrB,MAAM,kBAAkB;gBACtB,UAAU;gBACV,UAAU,kBAAkB,GAAG;gBAC/B,WAAW,kBAAkB,GAAG;gBAChC,mBAAmB;YACrB;YACA,SAAS,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE;YAChC,gBAAgB;YAChB,aAAa;YACb,YAAY;QACd;IACF;IAEA,MAAM,2BAA2B;QAC/B,IAAI,UAAU,WAAW,IAAI,UAAU,WAAW,CAAC,KAAK,EAAE;YACxD,IAAI;gBACF,MAAM,SAAS,MAAM,UAAU,WAAW,CAAC,KAAK,CAAC;oBAC/C,MAAM;gBACR;gBACA,IAAI,OAAO,KAAK,KAAK,UAAU;oBAC7B,4BAA4B;oBAC5B;gBACF;YACF,EAAE,OAAO,KAAK;gBACZ,4BAA4B;YAC9B;QACF;QAEA,4BAA4B;QAE5B,MAAM,sBAAsB;YAC1B,IAAI;gBACF,MAAM,cAAc,MAAM,CAAA,GAAA,qHAAA,CAAA,+BAA4B,AAAD,EACnD;gBAEF,MAAM,gBAAgB,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE;gBAE3C,IAAI,eAAe;oBACjB,uCAAuC;oBACvC,MAAM,eAAe;wBACnB,UAAU,cAAc,QAAQ,IAAI;wBACpC,UAAU,cAAc,QAAQ,IAAI;wBACpC,WAAW,cAAc,SAAS,IAAI;wBACtC,iBAAiB;oBACnB;oBACA,SAAS,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD,EAAE;gBAClC;YACF,EAAE,OAAO,KAAK;gBACZ,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,4BAA4B;YAC9B;QACF;QAEA;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,KAAK;gBACL,WAAW,CAAC,oEAAoE,EAC9E,WAAW,cAAc,IACzB;;kCAEF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;oCAAE,QAAQ;gCAAO;gCACxB,OAAO,cAAc,YAAY;gCACjC,SAAS;oCACP,aAAa;oCACb,YAAY;gCACd;gCACA,UAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;oCAEZ,CAAC,4BAA4B,sBAAsB,mBAClD,8OAAC;wCAAK,WAAU;kDACd,cAAA,8OAAC;;;;;;;;;;kDAGL,8OAAC,+IAAA,CAAA,gBAAa;wCACZ,SAAS;4CACP,aAAa,CAAC,OAAS,CAAC;4CACxB,YAAY,CAAC,OAAS,CAAC;wCACzB;wCACA,WAAW,CAAC,+EAA+E,EACzF,WAAW,eAAe,IAC1B;;;;;;;;;;;;;;;;;;oBAIP,aACC,CAAC,aAAa,SAAS,kBACrB,8OAAC;wBAAG,WAAU;kCACX,aAAa,IAAI,CAAC,2BACjB,8OAAC;gCAEC,WAAU;gCACV,SAAS;oCACP,aAAa;gCACf;0CAEC,WAAW,WAAW;+BANlB,WAAW,QAAQ;;;;;;;;;6CAW9B,8OAAC;wBAAI,WAAU;kCACZ,uCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;;;;8CACD,8OAAC;8CAAK;;;;;;;;;;;iDAGR;;8CAEE,8OAAC;oCACC,WAAW,CAAC,qHAAqH,EAC/H,2BACI,mCACA,IACJ;oCACF,SAAS;wCACP,IAAI,CAAC,0BAA0B;4CAC7B;4CACA,aAAa;4CACb,YAAY;wCACd;oCACF;;sDAEA,8OAAC,8IAAA,CAAA,eAAY;4CAAC,MAAM;;;;;;sDACpB,8OAAC;sDAAK;;;;;;wCACL,0CACC,8OAAC;4CAAK,WAAU;sDACd,cAAA,8OAAC;;;;;;;;;;;;;;;;8CAKP,8OAAC;oCAAI,WAAU;;wCACZ;wCAAI;;;;;;;gCAGN,OAAO,MAAM,CAAC,yHAAA,CAAA,mBAAgB,EAAE,GAAG,CAAC,CAAC,qBACpC,8OAAC;wCAEC,OAAO;wCACP,SAAS;4CACP,mBAAmB;wCACrB;wCACA,WAAW,GACT,kBAAkB,YAAY,OAAO,gBAAgB,GACtD,oEAAoE,CAAC;;0DAEtE,8OAAC,8IAAA,CAAA,sBAAmB;;;;;4CACnB;;uCAVI;;;;;;;;;;;4BAgBhB;;;;;;;YAGJ,0CACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,4BAA4B;sCAE3C,cAAA,8OAAC,8IAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,4BAA4B;8CAC5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}]}