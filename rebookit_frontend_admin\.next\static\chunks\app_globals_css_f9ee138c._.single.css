/* [project]/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-orange-200: oklch(90.1% .076 70.697);
    --color-orange-400: oklch(75% .183 55.934);
    --color-amber-200: oklch(92.4% .12 95.746);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-lime-200: oklch(93.8% .127 124.321);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-emerald-200: oklch(90.5% .093 164.15);
    --color-emerald-400: oklch(76.5% .177 163.223);
    --color-teal-200: oklch(91% .096 180.426);
    --color-cyan-200: oklch(91.7% .08 205.041);
    --color-sky-200: oklch(90.1% .058 230.902);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-indigo-100: oklch(93% .034 272.788);
    --color-indigo-200: oklch(87% .065 274.039);
    --color-indigo-800: oklch(39.8% .195 277.366);
    --color-violet-200: oklch(89.4% .057 293.283);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-200: oklch(90.2% .063 306.703);
    --color-purple-300: oklch(82.7% .119 306.383);
    --color-purple-700: oklch(49.6% .265 301.924);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-fuchsia-200: oklch(90.3% .076 319.62);
    --color-pink-200: oklch(89.9% .061 343.231);
    --color-rose-200: oklch(89.2% .058 10.001);
    --color-slate-100: oklch(96.8% .007 247.896);
    --color-slate-200: oklch(92.9% .013 255.508);
    --color-slate-300: oklch(86.9% .022 252.894);
    --color-slate-600: oklch(44.6% .043 257.281);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-6xl: 72rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --font-weight-extralight: 200;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --tracking-tight: -.025em;
    --tracking-wide: .025em;
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --drop-shadow-lg: 0 4px 4px #00000026;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-xs: 4px;
    --blur-md: 12px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .-top-3 {
    top: calc(var(--spacing) * -3);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-0\.5 {
    top: calc(var(--spacing) * .5);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-1\/4 {
    top: 25%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-3 {
    top: calc(var(--spacing) * 3);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-\[-10px\] {
    top: -10px;
  }

  .top-\[-20px\] {
    top: -20px;
  }

  .top-\[-25px\] {
    top: -25px;
  }

  .top-\[0px\] {
    top: 0;
  }

  .top-\[2px\] {
    top: 2px;
  }

  .top-\[10px\] {
    top: 10px;
  }

  .top-\[29px\] {
    top: 29px;
  }

  .top-\[35\%\] {
    top: 35%;
  }

  .top-\[150\%\] {
    top: 150%;
  }

  .top-full {
    top: 100%;
  }

  .-right-2 {
    right: calc(var(--spacing) * -2);
  }

  .-right-3 {
    right: calc(var(--spacing) * -3);
  }

  .-right-4 {
    right: calc(var(--spacing) * -4);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-\[-10px\] {
    right: -10px;
  }

  .right-\[10px\] {
    right: 10px;
  }

  .right-\[13px\] {
    right: 13px;
  }

  .right-\[15px\] {
    right: 15px;
  }

  .right-\[17px\] {
    right: 17px;
  }

  .right-\[20px\] {
    right: 20px;
  }

  .right-\[25px\] {
    right: 25px;
  }

  .right-\[80\%\] {
    right: 80%;
  }

  .-bottom-5 {
    bottom: calc(var(--spacing) * -5);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-\[-20px\] {
    bottom: -20px;
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-0\.5 {
    left: calc(var(--spacing) * .5);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-6 {
    left: calc(var(--spacing) * 6);
  }

  .left-\[-20px\] {
    left: -20px;
  }

  .left-\[2px\] {
    left: 2px;
  }

  .left-full {
    left: 100%;
  }

  .z-9 {
    z-index: 9;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-50 {
    z-index: 50;
  }

  .z-100, .z-\[100\] {
    z-index: 100;
  }

  .z-\[999\] {
    z-index: 999;
  }

  .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .col-span-3 {
    grid-column: span 3 / span 3;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .m-0 {
    margin: calc(var(--spacing) * 0);
  }

  .m-3 {
    margin: calc(var(--spacing) * 3);
  }

  .m-auto {
    margin: auto;
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }

  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }

  .my-\[26px\] {
    margin-block: 26px;
  }

  .ms-1 {
    margin-inline-start: calc(var(--spacing) * 1);
  }

  .ms-2 {
    margin-inline-start: calc(var(--spacing) * 2);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-\[26px\] {
    margin-top: 26px;
  }

  .mt-\[42px\] {
    margin-top: 42px;
  }

  .mt-\[50px\] {
    margin-top: 50px;
  }

  .mt-auto {
    margin-top: auto;
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }

  .-mb-px {
    margin-bottom: -1px;
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-\[40px\] {
    margin-bottom: 40px;
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .line-clamp-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-4\.5 {
    height: calc(var(--spacing) * 4.5);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-11 {
    height: calc(var(--spacing) * 11);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-28 {
    height: calc(var(--spacing) * 28);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-108 {
    height: calc(var(--spacing) * 108);
  }

  .h-\[8px\] {
    height: 8px;
  }

  .h-\[10px\] {
    height: 10px;
  }

  .h-\[13px\] {
    height: 13px;
  }

  .h-\[14px\] {
    height: 14px;
  }

  .h-\[17px\] {
    height: 17px;
  }

  .h-\[20px\] {
    height: 20px;
  }

  .h-\[27px\] {
    height: 27px;
  }

  .h-\[30px\] {
    height: 30px;
  }

  .h-\[36px\] {
    height: 36px;
  }

  .h-\[40px\] {
    height: 40px;
  }

  .h-\[43px\] {
    height: 43px;
  }

  .h-\[46px\] {
    height: 46px;
  }

  .h-\[47px\] {
    height: 47px;
  }

  .h-\[48px\] {
    height: 48px;
  }

  .h-\[50px\] {
    height: 50px;
  }

  .h-\[60vh\] {
    height: 60vh;
  }

  .h-\[64px\] {
    height: 64px;
  }

  .h-\[72px\] {
    height: 72px;
  }

  .h-\[83\.4\%\] {
    height: 83.4%;
  }

  .h-\[90px\] {
    height: 90px;
  }

  .h-\[100px\] {
    height: 100px;
  }

  .h-\[100vh\] {
    height: 100vh;
  }

  .h-\[120px\] {
    height: 120px;
  }

  .h-\[148px\] {
    height: 148px;
  }

  .h-\[150px\] {
    height: 150px;
  }

  .h-\[200px\] {
    height: 200px;
  }

  .h-\[250px\] {
    height: 250px;
  }

  .h-\[260px\] {
    height: 260px;
  }

  .h-\[340px\] {
    height: 340px;
  }

  .h-\[400px\] {
    height: 400px;
  }

  .h-\[530px\] {
    height: 530px;
  }

  .h-\[630px\] {
    height: 630px;
  }

  .h-\[inherit\] {
    height: inherit;
  }

  .h-fit {
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-screen {
    height: 100vh;
  }

  .max-h-0 {
    max-height: calc(var(--spacing) * 0);
  }

  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }

  .max-h-\[200px\] {
    max-height: 200px;
  }

  .max-h-\[400px\] {
    max-height: 400px;
  }

  .min-h-\[34\.71px\] {
    min-height: 34.71px;
  }

  .min-h-\[50px\] {
    min-height: 50px;
  }

  .min-h-\[60vh\] {
    min-height: 60vh;
  }

  .min-h-\[100px\] {
    min-height: 100px;
  }

  .min-h-\[120px\] {
    min-height: 120px;
  }

  .min-h-\[140px\] {
    min-height: 140px;
  }

  .min-h-\[150px\] {
    min-height: 150px;
  }

  .min-h-\[200px\] {
    min-height: 200px;
  }

  .min-h-\[220px\] {
    min-height: 220px;
  }

  .min-h-full {
    min-height: 100%;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-1\/1 {
    width: 100%;
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-1\/3 {
    width: 33.3333%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-2\/12 {
    width: 16.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-4\.5 {
    width: calc(var(--spacing) * 4.5);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-40 {
    width: calc(var(--spacing) * 40);
  }

  .w-70 {
    width: calc(var(--spacing) * 70);
  }

  .w-\[5\%\] {
    width: 5%;
  }

  .w-\[8\%\] {
    width: 8%;
  }

  .w-\[8px\] {
    width: 8px;
  }

  .w-\[9\%\] {
    width: 9%;
  }

  .w-\[11\%\] {
    width: 11%;
  }

  .w-\[12\%\] {
    width: 12%;
  }

  .w-\[13\%\] {
    width: 13%;
  }

  .w-\[14\%\] {
    width: 14%;
  }

  .w-\[16\%\] {
    width: 16%;
  }

  .w-\[18\%\] {
    width: 18%;
  }

  .w-\[20\%\] {
    width: 20%;
  }

  .w-\[20px\] {
    width: 20px;
  }

  .w-\[27px\] {
    width: 27px;
  }

  .w-\[30\%\] {
    width: 30%;
  }

  .w-\[30px\] {
    width: 30px;
  }

  .w-\[32px\] {
    width: 32px;
  }

  .w-\[35\%\] {
    width: 35%;
  }

  .w-\[40px\] {
    width: 40px;
  }

  .w-\[44px\] {
    width: 44px;
  }

  .w-\[45\%\] {
    width: 45%;
  }

  .w-\[46px\] {
    width: 46px;
  }

  .w-\[50px\] {
    width: 50px;
  }

  .w-\[55\%\] {
    width: 55%;
  }

  .w-\[65\%\] {
    width: 65%;
  }

  .w-\[80\%\] {
    width: 80%;
  }

  .w-\[90\%\] {
    width: 90%;
  }

  .w-\[96\%\] {
    width: 96%;
  }

  .w-\[100\%\] {
    width: 100%;
  }

  .w-\[100px\] {
    width: 100px;
  }

  .w-\[120px\] {
    width: 120px;
  }

  .w-\[150px\] {
    width: 150px;
  }

  .w-\[175px\] {
    width: 175px;
  }

  .w-\[180px\] {
    width: 180px;
  }

  .w-\[200px\] {
    width: 200px;
  }

  .w-\[250px\] {
    width: 250px;
  }

  .w-\[258px\] {
    width: 258px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[320px\] {
    width: 320px;
  }

  .w-\[350px\] {
    width: 350px;
  }

  .w-\[643px\] {
    width: 643px;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-\[100px\] {
    max-width: 100px;
  }

  .max-w-\[200px\] {
    max-width: 200px;
  }

  .max-w-\[220px\] {
    max-width: 220px;
  }

  .max-w-\[300px\] {
    max-width: 300px;
  }

  .max-w-\[400px\] {
    max-width: 400px;
  }

  .max-w-\[1440px\] {
    max-width: 1440px;
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-\[100px\] {
    min-width: 100px;
  }

  .min-w-\[120px\] {
    min-width: 120px;
  }

  .min-w-\[180px\] {
    min-width: 180px;
  }

  .min-w-\[200px\] {
    min-width: 200px;
  }

  .min-w-\[260px\] {
    min-width: 260px;
  }

  .min-w-\[600px\] {
    min-width: 600px;
  }

  .min-w-\[700px\] {
    min-width: 700px;
  }

  .min-w-full {
    min-width: 100%;
  }

  .min-w-max {
    min-width: max-content;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .table-auto {
    table-layout: auto;
  }

  .table-fixed {
    table-layout: fixed;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .border-separate {
    border-collapse: separate;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-4 {
    --tw-translate-x: calc(var(--spacing) * 4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .-rotate-180 {
    rotate: -180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .\!cursor-default {
    cursor: default !important;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .resize {
    resize: both;
  }

  .appearance-none {
    appearance: none;
  }

  .break-after-all {
    break-after: all;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-around {
    justify-content: space-around;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-3\.5 {
    gap: calc(var(--spacing) * 3.5);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }

  .gap-\[5px\] {
    gap: 5px;
  }

  .gap-\[7px\] {
    gap: 7px;
  }

  .gap-\[10px\] {
    gap: 10px;
  }

  .gap-\[15px\] {
    gap: 15px;
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-\[\#EFF1F4\] > :not(:last-child)) {
    border-color: #eff1f4;
  }

  :where(.divide-gray-100 > :not(:last-child)) {
    border-color: var(--color-gray-100);
  }

  :where(.divide-gray-200 > :not(:last-child)) {
    border-color: var(--color-gray-200);
  }

  .self-baseline {
    align-self: baseline;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-\[2px\] {
    border-radius: 2px;
  }

  .rounded-\[5px\] {
    border-radius: 5px;
  }

  .rounded-\[8px\] {
    border-radius: 8px;
  }

  .rounded-\[10px\] {
    border-radius: 10px;
  }

  .rounded-\[16\.87px\] {
    border-radius: 16.87px;
  }

  .rounded-\[16px\] {
    border-radius: 16px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-t-3xl {
    border-top-left-radius: var(--radius-3xl);
    border-top-right-radius: var(--radius-3xl);
  }

  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }

  .rounded-tl-lg {
    border-top-left-radius: var(--radius-lg);
  }

  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }

  .rounded-tr-lg {
    border-top-right-radius: var(--radius-lg);
  }

  .rounded-br-lg {
    border-bottom-right-radius: var(--radius-lg);
  }

  .rounded-bl-lg {
    border-bottom-left-radius: var(--radius-lg);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-\[1px\] {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-r-0 {
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-b-\[0\.5px\] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: .5px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }

  .border-\[\#1a1a1a2e\] {
    border-color: #1a1a1a2e;
  }

  .border-\[\#0161AB\] {
    border-color: #0161ab;
  }

  .border-\[\#211F54\] {
    border-color: #211f54;
  }

  .border-\[\#7878784f\] {
    border-color: #7878784f;
  }

  .border-\[\#AEAEB2\] {
    border-color: #aeaeb2;
  }

  .border-\[\#D0D5DD\] {
    border-color: #d0d5dd;
  }

  .border-\[\#DCDCDE\] {
    border-color: #dcdcde;
  }

  .border-\[\#E1E1E2\] {
    border-color: #e1e1e2;
  }

  .border-\[\#E6E6E6\] {
    border-color: #e6e6e6;
  }

  .border-\[\#E1020C\] {
    border-color: #e1020c;
  }

  .border-\[\#EFF1F4\] {
    border-color: #eff1f4;
  }

  .border-\[\#F3F3F3\] {
    border-color: #f3f3f3;
  }

  .border-\[\#F5F5F5\] {
    border-color: #f5f5f5;
  }

  .border-\[\#F8F9FA\] {
    border-color: #f8f9fa;
  }

  .border-\[\#dddddd\] {
    border-color: #ddd;
  }

  .border-\[\#eee\] {
    border-color: #eee;
  }

  .border-\[gray\] {
    border-color: gray;
  }

  .border-blue-400 {
    border-color: var(--color-blue-400);
  }

  .border-blue-500 {
    border-color: var(--color-blue-500);
  }

  .border-blue-600 {
    border-color: var(--color-blue-600);
  }

  .border-gray-100 {
    border-color: var(--color-gray-100);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-400 {
    border-color: var(--color-gray-400);
  }

  .border-gray-500 {
    border-color: var(--color-gray-500);
  }

  .border-gray-600 {
    border-color: var(--color-gray-600);
  }

  .border-gray-900 {
    border-color: var(--color-gray-900);
  }

  .border-orange-200 {
    border-color: var(--color-orange-200);
  }

  .border-orange-400 {
    border-color: var(--color-orange-400);
  }

  .border-red-300 {
    border-color: var(--color-red-300);
  }

  .border-red-400 {
    border-color: var(--color-red-400);
  }

  .border-slate-200 {
    border-color: var(--color-slate-200);
  }

  .border-slate-300 {
    border-color: var(--color-slate-300);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-white\/40 {
    border-color: #fff6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-white\/40 {
      border-color: color-mix(in oklab, var(--color-white) 40%, transparent);
    }
  }

  .border-t-transparent {
    border-top-color: #0000;
  }

  .bg-\[\#1DC9A0\] {
    background-color: #1dc9a0;
  }

  .bg-\[\#4D7906\] {
    background-color: #4d7906;
  }

  .bg-\[\#4d79061a\] {
    background-color: #4d79061a;
  }

  .bg-\[\#7A00A30A\] {
    background-color: #7a00a30a;
  }

  .bg-\[\#9BA8B0\] {
    background-color: #9ba8b0;
  }

  .bg-\[\#12B76A\] {
    background-color: #12b76a;
  }

  .bg-\[\#0161AB\] {
    background-color: #0161ab;
  }

  .bg-\[\#0161ab0d\] {
    background-color: #0161ab0d;
  }

  .bg-\[\#211F54\] {
    background-color: #211f54;
  }

  .bg-\[\#E1020C\] {
    background-color: #e1020c;
  }

  .bg-\[\#EAEAEA\] {
    background-color: #eaeaea;
  }

  .bg-\[\#EB5757\] {
    background-color: #eb5757;
  }

  .bg-\[\#ECFDF3\] {
    background-color: #ecfdf3;
  }

  .bg-\[\#EFEFEF99\] {
    background-color: #efefef99;
  }

  .bg-\[\#F2F2F7\] {
    background-color: #f2f2f7;
  }

  .bg-\[\#F4F4F4\] {
    background-color: #f4f4f4;
  }

  .bg-\[\#F5F5F5\] {
    background-color: #f5f5f5;
  }

  .bg-\[\#F5F6FA\] {
    background-color: #f5f6fa;
  }

  .bg-\[\#F5F8FE\] {
    background-color: #f5f8fe;
  }

  .bg-\[\#F9FAFB\] {
    background-color: #f9fafb;
  }

  .bg-\[\#F15046\] {
    background-color: #f15046;
  }

  .bg-\[\#FAFAFA\] {
    background-color: #fafafa;
  }

  .bg-\[\#FAFBFB\] {
    background-color: #fafbfb;
  }

  .bg-\[\#FDE333\] {
    background-color: #fde333;
  }

  .bg-\[\#FDFDFD\] {
    background-color: #fdfdfd;
  }

  .bg-\[\#FF000A\] {
    background-color: #ff000a;
  }

  .bg-\[\#FFC72C26\] {
    background-color: #ffc72c26;
  }

  .bg-\[\#FFF2EA\] {
    background-color: #fff2ea;
  }

  .bg-\[\#f4f4f4\] {
    background-color: #f4f4f4;
  }

  .bg-\[\#f5f8fe7e\] {
    background-color: #f5f8fe7e;
  }

  .bg-\[\#fafafa\] {
    background-color: #fafafa;
  }

  .bg-\[\#fcfcfc\] {
    background-color: #fcfcfc;
  }

  .bg-\[white\] {
    background-color: #fff;
  }

  .bg-amber-200 {
    background-color: var(--color-amber-200);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-200 {
    background-color: var(--color-blue-200);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-900 {
    background-color: var(--color-blue-900);
  }

  .bg-cyan-200 {
    background-color: var(--color-cyan-200);
  }

  .bg-emerald-200 {
    background-color: var(--color-emerald-200);
  }

  .bg-emerald-400 {
    background-color: var(--color-emerald-400);
  }

  .bg-fuchsia-200 {
    background-color: var(--color-fuchsia-200);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-200 {
    background-color: var(--color-green-200);
  }

  .bg-green-300 {
    background-color: var(--color-green-300);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-green-700 {
    background-color: var(--color-green-700);
  }

  .bg-indigo-100 {
    background-color: var(--color-indigo-100);
  }

  .bg-indigo-200 {
    background-color: var(--color-indigo-200);
  }

  .bg-lime-200 {
    background-color: var(--color-lime-200);
  }

  .bg-orange-200 {
    background-color: var(--color-orange-200);
  }

  .bg-orange-400 {
    background-color: var(--color-orange-400);
  }

  .bg-pink-200 {
    background-color: var(--color-pink-200);
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-200 {
    background-color: var(--color-purple-200);
  }

  .bg-purple-700 {
    background-color: var(--color-purple-700);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-200 {
    background-color: var(--color-red-200);
  }

  .bg-red-300 {
    background-color: var(--color-red-300);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-rose-200 {
    background-color: var(--color-rose-200);
  }

  .bg-sky-200 {
    background-color: var(--color-sky-200);
  }

  .bg-slate-100 {
    background-color: var(--color-slate-100);
  }

  .bg-slate-200 {
    background-color: var(--color-slate-200);
  }

  .bg-teal-200 {
    background-color: var(--color-teal-200);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-violet-200 {
    background-color: var(--color-violet-200);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/30 {
    background-color: #ffffff4d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/30 {
      background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-200 {
    background-color: var(--color-yellow-200);
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-tr {
    --tw-gradient-position: to top right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-\[linear-gradient\(90deg\,\#E1020C_0\%\,\#4D7906_100\%\)\] {
    background-image: linear-gradient(90deg, #e1020c 0%, #4d7906 100%);
  }

  .bg-\[linear-gradient\(268deg\,_\#211f54_11\.09\%\,_\#0161ab_98\.55\%\)\] {
    background-image: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  }

  .from-\[\#0161AB\] {
    --tw-gradient-from: #0161ab;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#211F54\] {
    --tw-gradient-from: #211f54;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#24194B\] {
    --tw-gradient-from: #24194b;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-\[\#E1020C\] {
    --tw-gradient-from: #e1020c;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-500 {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-700 {
    --tw-gradient-from: var(--color-blue-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-200 {
    --tw-gradient-from: var(--color-gray-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-500 {
    --tw-gradient-from: var(--color-green-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-700 {
    --tw-gradient-from: var(--color-purple-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-red-600 {
    --tw-gradient-from: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#0B5B8C\] {
    --tw-gradient-to: #0b5b8c;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#4D7906\] {
    --tw-gradient-to: #4d7906;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#0161AB\] {
    --tw-gradient-to: #0161ab;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-\[\#211F54\] {
    --tw-gradient-to: #211f54;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-900 {
    --tw-gradient-to: var(--color-blue-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-300 {
    --tw-gradient-to: var(--color-gray-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-green-700 {
    --tw-gradient-to: var(--color-green-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-900 {
    --tw-gradient-to: var(--color-purple-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-red-800 {
    --tw-gradient-to: var(--color-red-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-0\.5 {
    padding: calc(var(--spacing) * .5);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-10 {
    padding: calc(var(--spacing) * 10);
  }

  .p-\[10px\] {
    padding: 10px;
  }

  .p-\[20px\] {
    padding: 20px;
  }

  .p-\[24px\] {
    padding: 24px;
  }

  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-3\.5 {
    padding-inline: calc(var(--spacing) * 3.5);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-7 {
    padding-inline: calc(var(--spacing) * 7);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .px-12 {
    padding-inline: calc(var(--spacing) * 12);
  }

  .px-16 {
    padding-inline: calc(var(--spacing) * 16);
  }

  .px-\[7px\] {
    padding-inline: 7px;
  }

  .px-\[9px\] {
    padding-inline: 9px;
  }

  .px-\[10px\] {
    padding-inline: 10px;
  }

  .px-\[30px\] {
    padding-inline: 30px;
  }

  .px-\[35px\] {
    padding-inline: 35px;
  }

  .px-\[40px\] {
    padding-inline: 40px;
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .py-50 {
    padding-block: calc(var(--spacing) * 50);
  }

  .py-\[7\.5px\] {
    padding-block: 7.5px;
  }

  .py-\[9px\] {
    padding-block: 9px;
  }

  .py-\[10px\] {
    padding-block: 10px;
  }

  .py-\[15px\] {
    padding-block: 15px;
  }

  .py-\[18\.5px\] {
    padding-block: 18.5px;
  }

  .ps-2 {
    padding-inline-start: calc(var(--spacing) * 2);
  }

  .ps-3 {
    padding-inline-start: calc(var(--spacing) * 3);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-\[20px\] {
    padding-top: 20px;
  }

  .pt-\[33px\] {
    padding-top: 33px;
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-6 {
    padding-right: calc(var(--spacing) * 6);
  }

  .pr-\[16px\] {
    padding-right: 16px;
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pb-\[20px\] {
    padding-bottom: 20px;
  }

  .pb-\[22px\] {
    padding-bottom: 22px;
  }

  .pb-\[30px\] {
    padding-bottom: 30px;
  }

  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }

  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .pl-14 {
    padding-left: calc(var(--spacing) * 14);
  }

  .pl-\[16px\] {
    padding-left: 16px;
  }

  .pl-\[17px\] {
    padding-left: 17px;
  }

  .text-center {
    text-align: center;
  }

  .text-end {
    text-align: end;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .align-middle {
    vertical-align: middle;
  }

  .align-top {
    vertical-align: top;
  }

  .font-\[poppins\] {
    font-family: poppins;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[5px\] {
    font-size: 5px;
  }

  .text-\[8px\] {
    font-size: 8px;
  }

  .text-\[10px\] {
    font-size: 10px;
  }

  .text-\[11px\] {
    font-size: 11px;
  }

  .text-\[12px\] {
    font-size: 12px;
  }

  .text-\[13px\] {
    font-size: 13px;
  }

  .text-\[14\.2px\] {
    font-size: 14.2px;
  }

  .text-\[14px\] {
    font-size: 14px;
  }

  .text-\[15px\] {
    font-size: 15px;
  }

  .text-\[16px\] {
    font-size: 16px;
  }

  .text-\[17px\] {
    font-size: 17px;
  }

  .text-\[18px\] {
    font-size: 18px;
  }

  .text-\[20px\] {
    font-size: 20px;
  }

  .text-\[21px\] {
    font-size: 21px;
  }

  .text-\[22px\] {
    font-size: 22px;
  }

  .text-\[24px\] {
    font-size: 24px;
  }

  .text-\[30px\] {
    font-size: 30px;
  }

  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }

  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }

  .leading-11 {
    --tw-leading: calc(var(--spacing) * 11);
    line-height: calc(var(--spacing) * 11);
  }

  .leading-\[15px\] {
    --tw-leading: 15px;
    line-height: 15px;
  }

  .leading-\[18px\] {
    --tw-leading: 18px;
    line-height: 18px;
  }

  .leading-\[22\.09px\] {
    --tw-leading: 22.09px;
    line-height: 22.09px;
  }

  .leading-\[23px\] {
    --tw-leading: 23px;
    line-height: 23px;
  }

  .leading-\[26px\] {
    --tw-leading: 26px;
    line-height: 26px;
  }

  .leading-\[29px\] {
    --tw-leading: 29px;
    line-height: 29px;
  }

  .leading-\[34px\] {
    --tw-leading: 34px;
    line-height: 34px;
  }

  .leading-\[42px\] {
    --tw-leading: 42px;
    line-height: 42px;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .\[line-height\:30px\] {
    line-height: 30px;
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-extralight {
    --tw-font-weight: var(--font-weight-extralight);
    font-weight: var(--font-weight-extralight);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .-tracking-\[0\.05px\] {
    --tw-tracking: calc(.05px * -1);
    letter-spacing: -.05px;
  }

  .-tracking-\[0\.5px\] {
    --tw-tracking: calc(.5px * -1);
    letter-spacing: -.5px;
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .break-all {
    word-break: break-all;
  }

  .text-ellipsis {
    text-overflow: ellipsis;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .text-\[\#0A090B\] {
    color: #0a090b;
  }

  .text-\[\#000\] {
    color: #000;
  }

  .text-\[\#1C1B1F\] {
    color: #1c1b1f;
  }

  .text-\[\#1a1a1ab3\] {
    color: #1a1a1ab3;
  }

  .text-\[\#2C2C2E\] {
    color: #2c2c2e;
  }

  .text-\[\#2D2B32\] {
    color: #2d2b32;
  }

  .text-\[\#4F4D55\] {
    color: #4f4d55;
  }

  .text-\[\#8C8C8C\] {
    color: #8c8c8c;
  }

  .text-\[\#9A9A9A\] {
    color: #9a9a9a;
  }

  .text-\[\#22c55e\] {
    color: #22c55e;
  }

  .text-\[\#027A48\] {
    color: #027a48;
  }

  .text-\[\#151D48\] {
    color: #151d48;
  }

  .text-\[\#0161AB\] {
    color: #0161ab;
  }

  .text-\[\#211F54\] {
    color: #211f54;
  }

  .text-\[\#232c6a\] {
    color: #232c6a;
  }

  .text-\[\#444\] {
    color: #444;
  }

  .text-\[\#313131\] {
    color: #313131;
  }

  .text-\[\#737375\] {
    color: #737375;
  }

  .text-\[\#787878\] {
    color: #787878;
  }

  .text-\[\#838282\] {
    color: #838282;
  }

  .text-\[\#E2E4E5\] {
    color: #e2e4e5;
  }

  .text-\[\#E1020C\] {
    color: #e1020c;
  }

  .text-\[\#E12121\] {
    color: #e12121;
  }

  .text-\[\#F15046\] {
    color: #f15046;
  }

  .text-\[\#FF8682\] {
    color: #ff8682;
  }

  .text-\[\#FFBB00\] {
    color: #fb0;
  }

  .text-\[\#FFFFFF\] {
    color: #fff;
  }

  .text-\[\#f43f5e\] {
    color: #f43f5e;
  }

  .text-\[black\] {
    color: #000;
  }

  .text-\[center\] {
    color: center;
  }

  .text-black {
    color: var(--color-black);
  }

  .text-blue-400 {
    color: var(--color-blue-400);
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-900 {
    color: var(--color-blue-900);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-green-900 {
    color: var(--color-green-900);
  }

  .text-indigo-800 {
    color: var(--color-indigo-800);
  }

  .text-orange-400 {
    color: var(--color-orange-400);
  }

  .text-purple-700 {
    color: var(--color-purple-700);
  }

  .text-purple-900 {
    color: var(--color-purple-900);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-slate-600 {
    color: var(--color-slate-600);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/90 {
    color: #ffffffe6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/90 {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .italic {
    font-style: italic;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-2 {
    text-underline-offset: 2px;
  }

  .accent-\[\#0161AB\] {
    accent-color: #0161ab;
  }

  .accent-\[\#E1020C\] {
    accent-color: #e1020c;
  }

  .accent-transparent {
    accent-color: #0000;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-25 {
    opacity: .25;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-75 {
    opacity: .75;
  }

  .opacity-80 {
    opacity: .8;
  }

  .opacity-90 {
    opacity: .9;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_1\.5px_4px_-1px_rgba\(10\,9\,11\,0\.07\)\] {
    --tw-shadow: 0 1.5px 4px -1px var(--tw-shadow-color, #0a090b12);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0px_3\.374px_16\.87px_0px_rgba\(238\,238\,238\,0\.50\)\] {
    --tw-shadow: 0px 3.374px 16.87px 0px var(--tw-shadow-color, #eeeeee80);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-blue-500 {
    --tw-ring-color: var(--color-blue-500);
  }

  .ring-offset-2 {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .drop-shadow {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, #0000001a)) drop-shadow(0 1px 1px var(--tw-drop-shadow-color, #0000000f));
    --tw-drop-shadow: drop-shadow(0 1px 2px #0000001a) drop-shadow(0 1px 1px #0000000f);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-lg {
    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, #00000026));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xs {
    --tw-backdrop-blur: blur(var(--blur-xs));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-150 {
    --tw-duration: .15s;
    transition-duration: .15s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-400 {
    --tw-duration: .4s;
    transition-duration: .4s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  :is(.\*\:border-r > *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  :is(.\*\:border-\[\#eee\] > *) {
    border-color: #eee;
  }

  :is(.\*\:px-2 > *) {
    padding-inline: calc(var(--spacing) * 2);
  }

  :is(.\*\:py-1 > *) {
    padding-block: calc(var(--spacing) * 1);
  }

  :is(.\*\:pl-2 > *) {
    padding-left: calc(var(--spacing) * 2);
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/subitem\:block:is(:where(.group\/subitem):hover *) {
      display: block;
    }
  }

  .peer-checked\:bg-\[\#1DC9A0\]:is(:where(.peer):checked ~ *) {
    background-color: #1dc9a0;
  }

  .peer-checked\:bg-blue-600:is(:where(.peer):checked ~ *) {
    background-color: var(--color-blue-600);
  }

  .peer-checked\:opacity-100:is(:where(.peer):checked ~ *) {
    opacity: 1;
  }

  .peer-focus\:outline-none:is(:where(.peer):focus ~ *) {
    --tw-outline-style: none;
    outline-style: none;
  }

  .placeholder\:border-gray-200::placeholder {
    border-color: var(--color-gray-200);
  }

  .placeholder\:text-sm::placeholder {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:-top-2:before {
    content: var(--tw-content);
    top: calc(var(--spacing) * -2);
  }

  .before\:left-4:before {
    content: var(--tw-content);
    left: calc(var(--spacing) * 4);
  }

  .before\:z-50:before {
    content: var(--tw-content);
    z-index: 50;
  }

  .before\:h-4:before {
    content: var(--tw-content);
    height: calc(var(--spacing) * 4);
  }

  .before\:w-4:before {
    content: var(--tw-content);
    width: calc(var(--spacing) * 4);
  }

  .before\:rotate-45:before {
    content: var(--tw-content);
    rotate: 45deg;
  }

  .before\:bg-white:before {
    content: var(--tw-content);
    background-color: var(--color-white);
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:start-\[2px\]:after {
    content: var(--tw-content);
    inset-inline-start: 2px;
  }

  .after\:top-\[1\.5px\]:after {
    content: var(--tw-content);
    top: 1.5px;
  }

  .after\:h-\[13\.5px\]:after {
    content: var(--tw-content);
    height: 13.5px;
  }

  .after\:h-\[14px\]:after {
    content: var(--tw-content);
    height: 14px;
  }

  .after\:w-\[14px\]:after {
    content: var(--tw-content);
    width: 14px;
  }

  .after\:rounded-full:after {
    content: var(--tw-content);
    border-radius: 3.40282e38px;
  }

  .after\:bg-\[\#E5E5EA\]:after {
    content: var(--tw-content);
    background-color: #e5e5ea;
  }

  .after\:bg-white:after {
    content: var(--tw-content);
    background-color: var(--color-white);
  }

  .after\:transition-all:after {
    content: var(--tw-content);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .after\:content-\[\'\'\]:after {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .peer-checked\:after\:translate-x-full:is(:where(.peer):checked ~ *):after {
    content: var(--tw-content);
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .peer-checked\:after\:border-white:is(:where(.peer):checked ~ *):after {
    content: var(--tw-content);
    border-color: var(--color-white);
  }

  .peer-checked\:after\:bg-white:is(:where(.peer):checked ~ *):after {
    content: var(--tw-content);
    background-color: var(--color-white);
  }

  .last\:border-r-0:last-child {
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .checked\:border-0:checked {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .checked\:border-transparent:checked {
    border-color: #0000;
  }

  .checked\:bg-transparent:checked {
    background-color: #0000;
  }

  .checked\:bg-\[linear-gradient\(268deg\,\#211F54_11\.09\%\,\#0161AB_98\.55\%\)\]:checked {
    background-image: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  }

  .checked\:text-transparent:checked {
    color: #0000;
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:cursor-pointer:hover {
      cursor: pointer;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#024e8c\]:hover {
      background-color: #024e8c;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#F3F8FC\]:hover {
      background-color: #f3f8fc;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#F5F6FA\]:hover {
      background-color: #f5f6fa;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#FFF0F1\]:hover {
      background-color: #fff0f1;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#b30009\]:hover {
      background-color: #b30009;
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-300:hover {
      background-color: var(--color-gray-300);
    }
  }

  @media (hover: hover) {
    .hover\:from-\[\#024e8c\]:hover {
      --tw-gradient-from: #024e8c;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:to-\[\#1a1b3a\]:hover {
      --tw-gradient-to: #1a1b3a;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-800:hover {
      color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-900:hover {
      color: var(--color-gray-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-500:hover {
      color: var(--color-red-500);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-600:hover {
      color: var(--color-red-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-800:hover {
      color: var(--color-red-800);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-90:hover {
      opacity: .9;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-300:focus {
    --tw-ring-color: var(--color-blue-300);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-green-300:focus {
    --tw-ring-color: var(--color-green-300);
  }

  .focus\:ring-purple-300:focus {
    --tw-ring-color: var(--color-purple-300);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:bg-transparent:active {
    background-color: #0000;
  }

  .disabled\:bg-gray-200:disabled {
    background-color: var(--color-gray-200);
  }

  .disabled\:text-gray-400:disabled {
    color: var(--color-gray-400);
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:order-2 {
      order: 2;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 48rem) {
    .md\:my-0 {
      margin-block: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:mt-20 {
      margin-top: calc(var(--spacing) * 20);
    }
  }

  @media (width >= 48rem) {
    .md\:ml-\[-10px\] {
      margin-left: -10px;
    }
  }

  @media (width >= 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:h-\[100px\] {
      height: 100px;
    }
  }

  @media (width >= 48rem) {
    .md\:h-full {
      height: 100%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-1\/3 {
      width: 33.3333%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-2\/3 {
      width: 66.6667%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-5\/12 {
      width: 41.6667%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-6\/12 {
      width: 50%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-10\/12 {
      width: 83.3333%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-12\/12 {
      width: 100%;
    }
  }

  @media (width >= 48rem) {
    .md\:w-auto {
      width: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:w-fit {
      width: fit-content;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-col {
      flex-direction: column;
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:items-start {
      align-items: flex-start;
    }
  }

  @media (width >= 48rem) {
    .md\:justify-center {
      justify-content: center;
    }
  }

  @media (width >= 48rem) {
    .md\:gap-2\.5 {
      gap: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 48rem) {
    .md\:p-\[24px\] {
      padding: 24px;
    }
  }

  @media (width >= 48rem) {
    .md\:px-2 {
      padding-inline: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:px-5 {
      padding-inline: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 48rem) {
    .md\:text-start {
      text-align: start;
    }
  }

  @media (width >= 48rem) {
    .md\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-\[14px\] {
      font-size: 14px;
    }
  }

  @media (width >= 48rem) {
    .md\:text-\[16px\] {
      font-size: 16px;
    }
  }

  @media (width >= 48rem) {
    .md\:text-\[17px\] {
      font-size: 17px;
    }
  }

  @media (width >= 48rem) {
    .md\:text-\[18px\] {
      font-size: 18px;
    }
  }

  @media (width >= 48rem) {
    .md\:text-\[40px\] {
      font-size: 40px;
    }
  }

  @media (width >= 48rem) {
    .md\:leading-\[22px\] {
      --tw-leading: 22px;
      line-height: 22px;
    }
  }

  @media (width >= 48rem) {
    .md\:font-semibold {
      --tw-font-weight: var(--font-weight-semibold);
      font-weight: var(--font-weight-semibold);
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[35\%\] {
      width: 35%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-\[65\%\] {
      width: 65%;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  .rtl\:peer-checked\:after\:-translate-x-full:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *):is(:where(.peer):checked ~ *):after {
    content: var(--tw-content);
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-600 {
      border-color: var(--color-gray-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-700 {
      background-color: var(--color-gray-700);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:peer-checked\:bg-blue-600:is(:where(.peer):checked ~ *) {
      background-color: var(--color-blue-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-200:hover {
        background-color: var(--color-gray-200);
      }
    }
  }
}

:root {
  --background: #fafbfc;
  --foreground: #171717;
}

body {
  background: var(--background);
  color: #212a30;
  font-family: Poppins, sans-serif;
}

.custom_slider_dots .slick-dots li div {
  background-color: #9ca3af;
  border-radius: 9999px;
  width: 40px;
  height: 5px;
  margin-top: 10px;
  transition: all .3s;
}

.custom_slider_dots li.slick-active div {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  width: 40px;
  height: 5px;
}

.custom_inside_dots {
  justify-content: center;
  gap: 2px;
  display: flex !important;
}

.custom_inside_dots li {
  width: 10px !important;
  height: 10px !important;
  margin: 0 !important;
}

.custom_inside_dots li button {
  padding: 0 !important;
}

.custom_inside_dots li button:before {
  opacity: .5;
  color: #fff !important;
}

.custom_inside_dots li.slick-active button:before {
  opacity: 1;
}

input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active {
  -webkit-box-shadow: inset 0 0 0 30px #fff !important;
}

.css-13cymwt-control {
  border: none;
  outline: none;
  width: 100% !important;
}

.css-1im77uy-control {
  outline: none;
  width: 100%;
  border: none !important;
}

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

button {
  cursor: pointer;
}

.global_linear_gradient {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
}

.global_text_linear_gradient {
  -webkit-text-fill-color: transparent;
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  -webkit-background-clip: text;
  background-clip: text;
}

.no_scrollbar::-webkit-scrollbar {
  display: none;
}

.shadow_medium, .shadow_medium:before {
  box-shadow: 0 1px 4px #00000029;
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

/*# sourceMappingURL=app_globals_css_f9ee138c._.single.css.map*/