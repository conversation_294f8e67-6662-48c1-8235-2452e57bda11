{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/categories/categories.module.scss.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"gradientAllRoundBorder\": \"categories-module-scss-module__Pg8QXG__gradientAllRoundBorder\",\n  \"membershipContainer\": \"categories-module-scss-module__Pg8QXG__membershipContainer\",\n  \"shimmer\": \"categories-module-scss-module__Pg8QXG__shimmer\",\n  \"skeleton-shimmer\": \"categories-module-scss-module__Pg8QXG__skeleton-shimmer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/utils/axiosError.handler.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\r\n// import history from \"./history\";\r\nimport { removeToken } from \"../utils/utils\";\r\n\r\n\r\nexport const axiosErrorHandler = (error, action, checkUnauthorized = true) => {\r\n\r\n    console.log(\"error\",error)\r\n    const requestStatus = error?.request?.status;\r\n    const responseStatus = error?.response?.status;\r\n    const dataStatus = error?.data?.statusCode;\r\n\r\n    // Only log out on true 401 Unauthorized from response\r\n    if (responseStatus === 401) {\r\n        removeToken();\r\n        if (typeof window !== 'undefined' && window.location) {\r\n            window.location.href = \"/login\";\r\n        }\r\n        return;\r\n    }\r\n    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {\r\n        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map(er => toast.error(er.messages)) || error?.data?.error?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n            );\r\n    }\r\n    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {\r\n        console.log(\"error log is\", error)\r\n        if (Array.isArray(error?.response?.data?.message) || Array?.isArray(error?.data?.message)) error?.response?.data?.message?.map(er => toast.error(er)) || error?.data?.message?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.message || error?.response?.data?.data || error?.data?.message,\r\n            );\r\n    }\r\n    if (\r\n        checkUnauthorized &&\r\n        (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)\r\n    ) {\r\n        if (localStorage.getItem(\"token\")) {\r\n            toast.error(error?.response?.data?.message);\r\n        }\r\n    }\r\n\r\n    if (action === \"uploadImage\") {\r\n        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {\r\n            if (localStorage.getItem(\"token\")) {\r\n                const message = error?.response?.data?.message;\r\n                message && toast.error(message);\r\n            } else history.push(\"/\");\r\n        }\r\n    }\r\n\r\n    if (error?.response) return error.response;\r\n    else if (error?.request) return error.request;\r\n    else return error?.message;\r\n};"], "names": [], "mappings": ";;;AAAA;AACA,mCAAmC;AACnC;;;AAGO,MAAM,oBAAoB,CAAC,OAAO,QAAQ,oBAAoB,IAAI;IAErE,QAAQ,GAAG,CAAC,SAAQ;IACpB,MAAM,gBAAgB,OAAO,SAAS;IACtC,MAAM,iBAAiB,OAAO,UAAU;IACxC,MAAM,aAAa,OAAO,MAAM;IAEhC,sDAAsD;IACtD,IAAI,mBAAmB,KAAK;QACxB,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;QACV,uCAAsD;;QAEtD;QACA;IACJ;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QACvE,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,UAAU,MAAM,OAAO,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,MAAM,OAAO,MAAM,OAAO,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAElM,mJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;IAEzF;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,kBAAkB,KAAK;QAChG,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,YAAY,OAAO,QAAQ,OAAO,MAAM,UAAU,OAAO,UAAU,MAAM,SAAS,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,OAAO,MAAM,SAAS,IAAI,CAAA,KAAM,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEjM,mJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,WAAW,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM;IAE1F;IACA,IACI,qBACA,CAAC,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,GAAG,GACxE;QACE,IAAI,aAAa,OAAO,CAAC,UAAU;YAC/B,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM;QACvC;IACJ;IAEA,IAAI,WAAW,eAAe;QAC1B,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;YACvE,IAAI,aAAa,OAAO,CAAC,UAAU;gBAC/B,MAAM,UAAU,OAAO,UAAU,MAAM;gBACvC,WAAW,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAC3B,OAAO,QAAQ,IAAI,CAAC;QACxB;IACJ;IAEA,IAAI,OAAO,UAAU,OAAO,MAAM,QAAQ;SACrC,IAAI,OAAO,SAAS,OAAO,MAAM,OAAO;SACxC,OAAO,OAAO;AACvB", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/service/axios.js"], "sourcesContent": ["const { default: axios } = require(\"axios\");\r\nconst { getToken } = require(\"../utils/utils\");\r\n\r\nconst BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst instance = axios.create({\r\n  baseURL: BASE_URL+\"/api\" ,\r\n\r\n  // Lets keep a check as default is 0 millisecond i.e. never\r\n  // Note: timeout is only for server response not network i.e. server reachability\r\n  timeout: 100000,\r\n\r\n  // Lets keep a check as default bytes- 2k\r\n  maxContentLength: 1000,\r\n\r\n  // Lets keep a check as default 5 seems high\r\n  maxRedirects: 2,\r\n});\r\n\r\ninstance.interceptors.request.use(\r\n  (config) => {\r\n    // const token = localStorage.getItem(\"auth\");\r\n    const token = getToken();\r\n    console.log(\"token\", token)\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // Rate limiting: only fire a request every 2 sec from lodash.debounce\r\n    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });\r\n    return Promise.resolve(config);\r\n  },\r\n  function (error) {\r\n    const response = handleLogError(error); // log them\r\n\r\n    return Promise.reject(error);\r\n  }\r\n  // multiple options as to when and how to apply these interceptors\r\n  // , { synchronous: true, runWhen: onGetCall }\r\n);\r\n\r\n\r\nmodule.exports = instance;"], "names": [], "mappings": "AAAA,MAAM,EAAE,SAAS,KAAK,EAAE;AACxB,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM;AAEN,MAAM,WAAW,MAAM,MAAM,CAAC;IAC5B,SAAS,WAAS;IAElB,2DAA2D;IAC3D,iFAAiF;IACjF,SAAS;IAET,yCAAyC;IACzC,kBAAkB;IAElB,4CAA4C;IAC5C,cAAc;AAChB;AAEA,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;IACC,8CAA8C;IAC9C,MAAM,QAAQ;IACd,QAAQ,GAAG,CAAC,SAAS;IACrB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,sEAAsE;IACtE,8EAA8E;IAC9E,OAAO,QAAQ,OAAO,CAAC;AACzB,GACA,SAAU,KAAK;IACb,MAAM,WAAW,eAAe,QAAQ,WAAW;IAEnD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAMF,OAAO,OAAO,GAAG", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/service/category.js"], "sourcesContent": ["const {axiosErrorHandler} = require(\"../utils/axiosError.handler\");\r\nconst instance = require(\"./axios\");\r\n\r\nlet uri = {\r\n  //category\r\n  addCategory: \"/master/category\",\r\n  //sub category\r\n  addSubCategory: \"/master/subCategory\",\r\n  getSubCategories: \"/master/sub-category\",\r\n  editSubCategory: \"/master/sub-category\",\r\n  deleteSubCategory: \"/master/sub-category\",\r\n  //sub sub category\r\n  addSubSubCategory: \"/master/subSubCategory\",\r\n  getSubSubCategory: \"/master/sub-sub-category\",\r\n  editSubSubCategory: \"/master/sub-sub-category\",\r\n  deleteSubSubCategory: \"/master/sub-sub-category\",\r\n  //sub sub sub category\r\n  addSubSubSubCategory: \"/master/subsubsubCategory\",\r\n  getSubSubSubCategory: \"/master/sub-sub-sub-category\",\r\n  editSubSubSubCategory: \"/master/sub-sub-sub-category\",\r\n  deleteSubSubSubCategory: \"/master/sub-sub-sub-category\",\r\n  //file upload\r\n  upload: `/admin/single-upload`,\r\n};\r\n\r\n// Category\r\nexport const addCategory = async (data) => {\r\n  let response = await instance\r\n    .post(uri.addCategory, data)\r\n    .catch(axiosErrorHandler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\nexport const getCategories = async () => {\r\n  let response = await instance.get(uri.addCategory).catch(axiosErrorHandler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\n\r\n//Sub-Category\r\nexport const addSubCategory = async (data) => {\r\n  let response = await instance\r\n    .post(uri.addSubCategory, data)\r\n    .catch(axiosErrorHandler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\n\r\nexport const getSubCategories = async (id) => {\r\n  let response = await instance\r\n    .get(uri.getSubCategories + `/${id}`)\r\n    .catch(axiosErrorHandler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\n\r\n//Sub-Sub-category\r\nexport const addSubSubCategory = async (data) => {\r\n  \r\n  let response = await instance\r\n    .post(uri.addSubSubCategory, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const getSubSubCategories = async (id) => {\r\n  let response = await instance\r\n    .get(uri.getSubSubCategory + `/${id}`)\r\n    .catch(axiosErrorHandler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\n\r\n//Sub-Sub-Sub-category\r\nexport const addSubSubSubCategory = async (data) => {\r\n  let response = await instance\r\n    .post(uri.addSubSubSubCategory, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const getSubSubSubCategories = async (id) => {\r\n  let response = await instance\r\n    .get(uri.getSubSubSubCategory + `/${id}`)\r\n    .catch(axiosErrorHandler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\n\r\nexport const uploadFile = async (data) => {\r\n  let response = await instance.post(uri.upload, data).catch(axiosErrorHandler);\r\n  // console.log(\"login test response\", response)\r\n  return response;\r\n};\r\n\r\n\r\n\r\n\r\n// Add these functions to app/service/category.js\r\n\r\nexport const editSubCategory = async (id, data) => {\r\n  let response = await instance\r\n    .put(`${uri.getSubCategories}/${id}`, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const deleteSubCategory = async (id) => {\r\n  let response = await instance\r\n    .delete(`${uri.getSubCategories}/${id}`)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const editSubSubCategory = async (id, data) => {\r\n  let response = await instance\r\n    .put(`${uri.getSubSubCategory}/${id}`, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const deleteSubSubCategory = async (id) => {\r\n  let response = await instance\r\n    .delete(`${uri.getSubSubCategory}/${id}`)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const editSubSubSubCategory = async (id, data) => {\r\n  let response = await instance\r\n    .put(`${uri.getSubSubSubCategory}/${id}`, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const deleteSubSubSubCategory = async (id) => {\r\n  let response = await instance\r\n    .delete(`${uri.getSubSubSubCategory}/${id}`)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,MAAM,EAAC,iBAAiB,EAAC;AACzB,MAAM;AAEN,IAAI,MAAM;IACR,UAAU;IACV,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,kBAAkB;IAClB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,mBAAmB;IACnB,mBAAmB;IACnB,oBAAoB;IACpB,sBAAsB;IACtB,sBAAsB;IACtB,sBAAsB;IACtB,sBAAsB;IACtB,uBAAuB;IACvB,yBAAyB;IACzB,aAAa;IACb,QAAQ,CAAC,oBAAoB,CAAC;AAChC;AAGO,MAAM,cAAc,OAAO;IAChC,IAAI,WAAW,MAAM,SAClB,IAAI,CAAC,IAAI,WAAW,EAAE,MACtB,KAAK,CAAC;IACT,+CAA+C;IAC/C,OAAO;AACT;AACO,MAAM,gBAAgB;IAC3B,IAAI,WAAW,MAAM,SAAS,GAAG,CAAC,IAAI,WAAW,EAAE,KAAK,CAAC;IACzD,+CAA+C;IAC/C,OAAO;AACT;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI,WAAW,MAAM,SAClB,IAAI,CAAC,IAAI,cAAc,EAAE,MACzB,KAAK,CAAC;IACT,+CAA+C;IAC/C,OAAO;AACT;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,CAAC,EAAE,IAAI,EACnC,KAAK,CAAC;IACT,+CAA+C;IAC/C,OAAO;AACT;AAGO,MAAM,oBAAoB,OAAO;IAEtC,IAAI,WAAW,MAAM,SAClB,IAAI,CAAC,IAAI,iBAAiB,EAAE,MAC5B,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,sBAAsB,OAAO;IACxC,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,IAAI,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,EACpC,KAAK,CAAC;IACT,+CAA+C;IAC/C,OAAO;AACT;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI,WAAW,MAAM,SAClB,IAAI,CAAC,IAAI,oBAAoB,EAAE,MAC/B,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,yBAAyB,OAAO;IAC3C,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,IAAI,oBAAoB,GAAG,CAAC,CAAC,EAAE,IAAI,EACvC,KAAK,CAAC;IACT,+CAA+C;IAC/C,OAAO;AACT;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI,WAAW,MAAM,SAAS,IAAI,CAAC,IAAI,MAAM,EAAE,MAAM,KAAK,CAAC;IAC3D,+CAA+C;IAC/C,OAAO;AACT;AAOO,MAAM,kBAAkB,OAAO,IAAI;IACxC,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,GAAG,IAAI,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAE,MACrC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI,WAAW,MAAM,SAClB,MAAM,CAAC,GAAG,IAAI,gBAAgB,CAAC,CAAC,EAAE,IAAI,EACtC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,qBAAqB,OAAO,IAAI;IAC3C,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,GAAG,IAAI,iBAAiB,CAAC,CAAC,EAAE,IAAI,EAAE,MACtC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,uBAAuB,OAAO;IACzC,IAAI,WAAW,MAAM,SAClB,MAAM,CAAC,GAAG,IAAI,iBAAiB,CAAC,CAAC,EAAE,IAAI,EACvC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,wBAAwB,OAAO,IAAI;IAC9C,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,GAAG,IAAI,oBAAoB,CAAC,CAAC,EAAE,IAAI,EAAE,MACzC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI,WAAW,MAAM,SAClB,MAAM,CAAC,GAAG,IAAI,oBAAoB,CAAC,CAAC,EAAE,IAAI,EAC1C,KAAK,CAAC;IACT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/public/landing/bookCategory.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 167, height: 173, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AFgNDW3JLRv2yy4b+MolHfjJGx34yiEe+MUeHfFFDAxUAF0PDXLPKhz9zzcj/8lDOP/HPj7/2nIs/9NbJflLDw1aAJtBGK3KHh3/xjAt/8c6M//MMCn/1Ecj/9JBIf6UShmgAL+RNvKQXDj/mGU1/6R6QP+7iEf/wZFZ/8WYWv/brDvqAKSgTvlmhGj/bnpK/0R5d/9pk3//RI+W/z+Ol/+sslzxAHuCTMaBiFX/jXo2/01/ev92lmv/PmZi/0xsbv9yilW8ABQvLkhzekzdlHo6/0GIhv9WjXj/MmFg/z5NUNkULClAAAECAgM0KQ9FkWgbwLqLKfWei0P0RmpquxYXGEABBAUDTaR9MTUgkHMAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0HAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAsd,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/categories/Tabs.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport MembershipScss from \"./categories.module.scss\";\r\nimport {\r\n  changeCategoryState,\r\n  changeCategoryTab,\r\n  changeMemberShipTab,\r\n} from \"../redux/slices/storeSlice\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\nimport { getCategories } from \"../service/category\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport bookIcon from \"@/public/landing/bookCategory.png\";\r\n\r\nexport default function Tabs() {\r\n  const searchParams = useSearchParams();\r\n  const [isLoading, setisLoading] = useState(false)\r\n  const dispatch = useDispatch();\r\n  const router = useRouter();\r\n  const [categories, setcategories] = useState([]);\r\n  // const [isLoading, setisLoading] = useState(false);\r\n\r\n  const storeData = useSelector((state) => state.storeData);\r\n\r\n  // Add this to preserve category ID in URL\r\n  const getTabUrl = (key) => {\r\n    const categoryId = searchParams.get(\"categoryId\");\r\n    return categoryId\r\n      ? `/categories/${key}?categoryId=${categoryId}`\r\n      : `/categories/${key}`;\r\n  };\r\n\r\n  const routeMap = {\r\n    // category: \"Category\",\r\n    subcategory: \"SubCategory\",\r\n    sub_subcategory: \"SubSubCategory\",\r\n    sub_sub_subcategory: \"SubSubSubCategory\",\r\n  };\r\n  console.log(\"storeData\", storeData);\r\n\r\n  console.log(\"location\", window.location.pathname.split(\"/\"));\r\n  let allnestPast = window.location.pathname.split(\"/\");\r\n  const tabs = Object.entries(routeMap);\r\n  console.log(\"tabs\", tabs);\r\n  const activeIndex = storeData.categoryTab;\r\n  console.log(\"activeIndex\", activeIndex);\r\n  let pathName = allnestPast[allnestPast.length];\r\n\r\n  const getCategoriesFunc = useCallback(async () => {\r\n    setisLoading(true)\r\n    let response = await getCategories();\r\n    console.log(\"response getCategories\", response);\r\n    if (response.status == 200) {\r\n      setcategories(response.data.categories);\r\n    }\r\n    setisLoading(false)\r\n  }, [])\r\n  useEffect(() => {\r\n    getCategoriesFunc();\r\n  }, [activeIndex]);\r\n\r\n  useEffect(() => {\r\n    const defaultPath = `/categories/${tabs[0][0]}`;\r\n    // router.asPath is not available in next/navigation; use window.location.pathname\r\n    const currentPath = window.location.pathname;\r\n    if (activeIndex === 0 && currentPath !== defaultPath) {\r\n      dispatch(changeCategoryTab(0));\r\n      router.push(`/categories/${tabs[0][0]}`);\r\n    }\r\n  }, [activeIndex, dispatch, router, tabs]);\r\n\r\n\r\n  return (\r\n    <>\r\n      <div>\r\n        {/* <div className={`w-[200px] h-[250px] rounded-md ${MembershipScss['skeleton-shimmer']}`}></div> */}\r\n        <h1 className=\"text-xl font-semibold mb-5\">Select Main Category</h1>\r\n        <div className=\"flex mb-6  gap-5 min-h-[200px]\">\r\n          {!isLoading ? categories?.map((item, index) => {\r\n            return (\r\n              <div\r\n                className={`cursor-pointer flex flex-col px-[35px] py-[15px] rounded-lg gap-4 w-[180px] min-h-[140px]  border ${storeData.categoriesState?.categoryId == item._id ? \"border-gray-500\" : \"border-gray-200\"}    items-center justify-start`}\r\n                key={index}\r\n                onClick={() => {\r\n                  // router.push(`/categories/subcategory?categoryId=${item._id}`)\r\n                  dispatch(changeCategoryState(item._id))\r\n                }\r\n                }\r\n              >\r\n                <div className=\"bg-gray-200 rounded-full w-[100px] p-5 h-[100px]\">\r\n                  <img\r\n                    // src={item.image}\r\n                    src={bookIcon.src}\r\n                    alt=\"categories_image\"\r\n                    className=\"rounded-full w-full h-full object-cover\"\r\n                  />\r\n                </div>\r\n                <p className=\"w-[90%] text-center break-after-all\">\r\n                  {item.name}\r\n                </p>\r\n              </div>\r\n            );\r\n          }) :\r\n            <div className=\"flex gap-3\">\r\n              <div className={`w-[200px] h-[200px] rounded-md ${MembershipScss['skeleton-shimmer']}`}></div>\r\n              <div className={`w-[200px] h-[200px] rounded-md ${MembershipScss['skeleton-shimmer']}`}></div>\r\n\r\n            </div>\r\n          }\r\n        </div>\r\n      </div>\r\n      <div\r\n        className={`bg-white min-h-full h-full rounded-2xl p-5 ${MembershipScss.membershipContainer}`}\r\n      >\r\n        <div className=\"relative flex justify-around items-center py-2 px-3 rounded-[5px] border border-[#F3F3F3] gap-2 h-[64px] overflow-hidden\">\r\n          {/* Animated background for active tab */}\r\n          <div\r\n            className=\"absolute h-[36px] rounded-[8px] bg-gradient-to-r from-[#0161AB] to-[#211F54] transition-all duration-400 ease-in-out\"\r\n            style={{\r\n              width: `calc(100% / ${tabs.length})`,\r\n              left: `calc(${activeIndex} * 100% / ${tabs.length})`,\r\n              top: \"50%\",\r\n              transform: \"translateY(-50%)\",\r\n              zIndex: 10,\r\n            }}\r\n          />\r\n\r\n          {/* Tabs */}\r\n          {tabs.map(([key, label], index) => (\r\n            <span\r\n              key={key}\r\n              className={`py-1  px-1.5 text-sm leading-[29px] text-center w-1/1 cursor-pointer z-20 transition-colors duration-300 ${activeIndex === index\r\n                ? \"text-white font-semibold\"\r\n                : \"text-[#444]\"\r\n                }`}\r\n              onClick={() => {\r\n                dispatch(changeCategoryTab(index));\r\n                router.push(`/categories/${key}`);\r\n              }}\r\n            >\r\n              {label}\r\n            </span>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAKA;AACA;AACA;AAEA;AAbA;;;;;;;;;;AAee,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,qDAAqD;IAErD,MAAM,YAAY,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;IAExD,0CAA0C;IAC1C,MAAM,YAAY,CAAC;QACjB,MAAM,aAAa,aAAa,GAAG,CAAC;QACpC,OAAO,aACH,CAAC,YAAY,EAAE,IAAI,YAAY,EAAE,YAAY,GAC7C,CAAC,YAAY,EAAE,KAAK;IAC1B;IAEA,MAAM,WAAW;QACf,wBAAwB;QACxB,aAAa;QACb,iBAAiB;QACjB,qBAAqB;IACvB;IACA,QAAQ,GAAG,CAAC,aAAa;IAEzB,QAAQ,GAAG,CAAC,YAAY,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;IACvD,IAAI,cAAc,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;IACjD,MAAM,OAAO,OAAO,OAAO,CAAC;IAC5B,QAAQ,GAAG,CAAC,QAAQ;IACpB,MAAM,cAAc,UAAU,WAAW;IACzC,QAAQ,GAAG,CAAC,eAAe;IAC3B,IAAI,WAAW,WAAW,CAAC,YAAY,MAAM,CAAC;IAE9C,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,aAAa;QACb,IAAI,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;QACjC,QAAQ,GAAG,CAAC,0BAA0B;QACtC,IAAI,SAAS,MAAM,IAAI,KAAK;YAC1B,cAAc,SAAS,IAAI,CAAC,UAAU;QACxC;QACA,aAAa;IACf,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAC/C,kFAAkF;QAClF,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;QAC5C,IAAI,gBAAgB,KAAK,gBAAgB,aAAa;YACpD,SAAS,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;YAC3B,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QACzC;IACF,GAAG;QAAC;QAAa;QAAU;QAAQ;KAAK;IAGxC,qBACE;;0BACE,8OAAC;;kCAEC,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;kCACZ,CAAC,YAAY,YAAY,IAAI,CAAC,MAAM;4BACnC,qBACE,8OAAC;gCACC,WAAW,CAAC,kGAAkG,EAAE,UAAU,eAAe,EAAE,cAAc,KAAK,GAAG,GAAG,oBAAoB,kBAAkB,8BAA8B,CAAC;gCAEzO,SAAS;oCACP,gEAAgE;oCAChE,SAAS,CAAA,GAAA,oIAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,GAAG;gCACvC;;kDAGA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,mBAAmB;4CACnB,KAAK,kSAAA,CAAA,UAAQ,CAAC,GAAG;4CACjB,KAAI;4CACJ,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAE,WAAU;kDACV,KAAK,IAAI;;;;;;;+BAhBP;;;;;wBAoBX,mBACE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,+BAA+B,EAAE,6JAAA,CAAA,UAAc,CAAC,mBAAmB,EAAE;;;;;;8CACtF,8OAAC;oCAAI,WAAW,CAAC,+BAA+B,EAAE,6JAAA,CAAA,UAAc,CAAC,mBAAmB,EAAE;;;;;;;;;;;;;;;;;;;;;;;0BAM9F,8OAAC;gBACC,WAAW,CAAC,2CAA2C,EAAE,6JAAA,CAAA,UAAc,CAAC,mBAAmB,EAAE;0BAE7F,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO,CAAC,YAAY,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC;gCACpC,MAAM,CAAC,KAAK,EAAE,YAAY,UAAU,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC;gCACpD,KAAK;gCACL,WAAW;gCACX,QAAQ;4BACV;;;;;;wBAID,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,sBACvB,8OAAC;gCAEC,WAAW,CAAC,yGAAyG,EAAE,gBAAgB,QACnI,6BACA,eACA;gCACJ,SAAS;oCACP,SAAS,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;oCAC3B,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK;gCAClC;0CAEC;+BAVI;;;;;;;;;;;;;;;;;;AAiBnB", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/components/common/SubmitButton.js"], "sourcesContent": ["import React from 'react'\r\n\r\nexport default function SubmitButton({isLoading,InnerDiv,type,btnAction}) {\r\n  return (\r\n     <button type={type} onClick={btnAction} className='mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5' >\r\n            {isLoading ? <>\r\n                <svg\r\n                    className=\"w-4 h-4 animate-spin text-white\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                >\r\n                    <circle\r\n                        className=\"opacity-25\"\r\n                        cx=\"12\"\r\n                        cy=\"12\"\r\n                        r=\"10\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"4\"\r\n                    ></circle>\r\n                    <path\r\n                        className=\"opacity-75\"\r\n                        fill=\"currentColor\"\r\n                        d=\"M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z\"\r\n                    ></path>\r\n                </svg>\r\n                Loading...\r\n            </> : <InnerDiv />}\r\n\r\n        </button>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,aAAa,EAAC,SAAS,EAAC,QAAQ,EAAC,IAAI,EAAC,SAAS,EAAC;IACtE,qBACG,8OAAC;QAAO,MAAM;QAAM,SAAS;QAAW,WAAU;kBAC1C,0BAAY;;8BACT,8OAAC;oBACG,WAAU;oBACV,MAAK;oBACL,SAAQ;;sCAER,8OAAC;4BACG,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEhB,8OAAC;4BACG,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAEJ;;yCAEJ,8OAAC;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/categories/sub_sub_subcategory/page.js"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useRef, useState } from \"react\";\r\nimport Tabs from \"../Tabs\";\r\nimport {\r\n  addSubSubSubCategory,\r\n  deleteSubSubSubCategory,\r\n  editSubSubSubCategory,\r\n  getCategories,\r\n  getSubCategories,\r\n  getSubSubCategories,\r\n  getSubSubSubCategories,\r\n  uploadFile,\r\n} from \"@/app/service/category\";\r\nimport { RiFileUploadLine } from \"react-icons/ri\";\r\nimport SubmitButton from \"@/app/components/common/SubmitButton\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { changeCategoryState } from \"@/app/redux/slices/storeSlice\";\r\nimport { toast } from \"react-toastify\";\r\nimport moment from \"moment\";\r\n\r\nconst SubSubSubCategoryPage = () => {\r\n  const storeData = useSelector((state) => state.storeData);\r\n  const [subSubSubCategories, setSubSubSubCategories] = useState([]);\r\n  const [subSubCategories, setSubSubCategories] = useState([]);\r\n  const [selectedCategory, setselectedCategory] = useState(storeData.categoriesState.categoryId || \"\");\r\n  const [subCategories, setsubCategories] = useState([]);\r\n  const [categories, setcategories] = useState([]);\r\n  const [selectedSubCategory, setselectedSubCategory] = useState([]);\r\n  const [selectedSubSubCategory, setSelectedSubSubCategory] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [submitIsLoading, setSubmitIsLoading] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n  const [selectedImage, setSelectedImage] = useState(null);\r\n  const [enteredName, setEnteredName] = useState(\"\");\r\n  const [selectedImageOnUpload, setSelectedImageOnUpload] = useState(null);\r\n\r\n  const [showModal, setShowModal] = useState(false);\r\n  const [subCategoryValue, setSubCategoryValue] = useState(\"\");\r\n  const [editingId, setEditingId] = useState(null);\r\n  const [confirmDeleteId, setConfirmDeleteId] = useState(null);\r\n\r\n  // const fetchSubSubCategories = async () => {\r\n  //   try {\r\n  //     const response = await getSubSubCategories();\r\n  //     if (response.status === 200) {\r\n  //       setSubSubCategories(response.data.subSubCategories || []);\r\n\r\n  //       if (\r\n  //         response.data.subSubCategories.length > 0 &&\r\n  //         !selectedSubSubCategory\r\n  //       ) {\r\n  //         setSelectedSubSubCategory(response.data.subSubCategories[0]._id);\r\n  //       }\r\n  //     }\r\n  //   } catch (error) {\r\n  //     console.error(\"Error fetching sub-sub-categories:\", error);\r\n  //     toast.error(\"Failed to load sub-sub-categories\");\r\n  //   }\r\n  // };\r\n\r\n  const fetchSubCategories = async (id) => {\r\n    let response = await getSubCategories(id);\r\n    if (response.status == 200) {\r\n      response.data.subCategories;\r\n      setsubCategories(response.data.subCategories);\r\n    }\r\n  };\r\n\r\n  const fetchSubSubCategories = async (id) => {\r\n    try {\r\n      let response = await getSubSubCategories(id);\r\n      if (response.status == 200) {\r\n        setSubSubCategories(response.data.subSubCategories);\r\n      }\r\n    } catch (err) {\r\n      console.log(\"err\", err);\r\n    }\r\n  };\r\n  useEffect(() => {\r\n    if (selectedCategory.length) {\r\n      fetchSubCategories(selectedCategory);\r\n    }\r\n  }, [selectedCategory]);\r\n\r\n  useEffect(() => {\r\n    if (selectedSubCategory.length) {\r\n      fetchSubSubCategories(selectedSubCategory);\r\n    }\r\n  }, [selectedSubCategory]);\r\n  console.log(\"category\", categories);\r\n  console.log(\"subCategory\", subCategories);\r\n  console.log(\"subSubCategory\", subSubCategories);\r\n  console.log(\"subSubSubCategory\", subSubSubCategories);\r\n  console.log(\"selectedCatrgory\", selectedCategory);\r\n  console.log(\"selectedSubCategory\", selectedSubCategory);\r\n  console.log(\"selectedSubCategory\", selectedSubCategory);\r\n\r\n  const fetchAllData = async (id) => {\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Fetch all categories\r\n      const categoriesRes = await getSubSubSubCategories(id);\r\n      if (categoriesRes.status !== 200) return;\r\n      const categories = categoriesRes.data?.subSubSubCategories;\r\n      setSubSubSubCategories(categories);\r\n      // Process all levels\r\n      // setSubSubSubCategories(allData.flat());\r\n    } catch (error) {\r\n      console.error(\"Error fetching data:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  console.log(\"subSubSubCategories\", subSubSubCategories);\r\n\r\n  const submitSubSubSubCategory = async () => {\r\n    setSubmitIsLoading(true);\r\n    try {\r\n      // Upload image first if exists\r\n      let imageUrl = \"\";\r\n      if (selectedImageOnUpload) {\r\n        const formData = new FormData();\r\n        formData.append(\"file\", selectedImageOnUpload);\r\n        const fileResponse = await uploadFile(formData);\r\n        imageUrl = fileResponse.data.url;\r\n      }\r\n\r\n      // Create payload\r\n      const payload = {\r\n        name: enteredName,\r\n        image: imageUrl,\r\n        subSubCategoryId: selectedSubSubCategory,\r\n      };\r\n\r\n      // Add sub-sub-sub-category\r\n      const response = await addSubSubSubCategory(payload);\r\n\r\n      if (response.status === 200) {\r\n        toast.success(\"Sub-Sub-Sub-Category Added Successfully\");\r\n        // fetchAllData(); // Refresh data\r\n\r\n        // Close modal and reset form\r\n        document.getElementById(\"subSubSubModal\").classList.add(\"hidden\");\r\n        setEnteredName(\"\");\r\n        setSelectedImage(null);\r\n        setSelectedImageOnUpload(null);\r\n        fetchAllData(response.data.subSubCategoryId);\r\n      } else {\r\n        toast.error(\"Failed to add sub-sub-sub-category\");\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Error adding sub-sub-sub-category\");\r\n      console.error(\"Error adding sub-sub-sub-category:\", error);\r\n    } finally {\r\n      setSubmitIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const InnerDiv = () => {\r\n    return <div className=\"px-4\">Submit</div>;\r\n  };\r\n\r\n  const handleButtonClick = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setSelectedImageOnUpload(file);\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setSelectedImage(reader.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // fetchSubSubCategories();\r\n    // fetchAllData();\r\n  }, []);\r\n\r\n  const getCategoriesFunc = async () => {\r\n    setIsLoading(true);\r\n    let response = await getCategories();\r\n    console.log(\"response getCategories\", response);\r\n\r\n    if (response.status == 200) {\r\n      // setcategories(response.data.categories)\r\n      let list = response.data?.categories;\r\n      setcategories(list);\r\n      // fetchSubCategory(list[0]._id)\r\n      // setselectedSubCategory(list[0]._id)\r\n\r\n      if (list.length > 0) {\r\n        // dispatch(changeCategoryState(list[0]._id))\r\n        // getSubCategory(list[0]._id);\r\n      }\r\n    }\r\n    // setIsLoading(false);\r\n  };\r\n\r\n  const fetchSubSubCategory = async (id) => {\r\n    try {\r\n      // setIsLoading(true)\r\n\r\n      let response = await getSubSubCategories(id);\r\n      if (response.status == 200) {\r\n        let list = response.data.subSubCategories || [];\r\n        setSubSubCategories(list);\r\n        if (list.length) {\r\n          fetchAllData(list[0]._id);\r\n          setSelectedSubSubCategory(list[0]._id);\r\n        }\r\n        // setSubSubSubCategories(response.data.subSubSubCategories)\r\n      }\r\n      setIsLoading(false);\r\n    } catch (err) {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n  console.log(subSubCategories, \"subSubCategories\");\r\n  const fetchSubCategory = async (id) => {\r\n    try {\r\n      setIsLoading(true);\r\n      let response = await getSubCategories(id);\r\n      if (response.status == 200) {\r\n        // response.data.categories\r\n        let list = response.data?.subCategories || [];\r\n        setsubCategories(list);\r\n        if (list.length > 0) {\r\n          setselectedSubCategory(list[0]._id);\r\n          // fetchSubSubCategory(list[0]._id)\r\n        }\r\n        setIsLoading(true);\r\n      }\r\n    } catch (err) {\r\n      console.log(\"Error in fetchin\");\r\n    }\r\n  };\r\n  useEffect(() => {\r\n    getCategoriesFunc();\r\n  }, [storeData.categoriesState.categoryId]);\r\n\r\n  useEffect(() => {\r\n    if (storeData.categoriesState.categoryId) {\r\n      fetchSubCategory(storeData.categoriesState.categoryId);\r\n    }\r\n  }, [storeData.categoriesState.categoryId]);\r\n\r\n  useEffect(() => {\r\n    if (selectedSubCategory.length) {\r\n      fetchSubSubCategory(selectedSubCategory);\r\n    }\r\n  }, [selectedSubCategory]);\r\n\r\n  const handleEdit = async () => {\r\n    const payload = {\r\n      name: subCategoryValue,\r\n      // Include other fields if necessary\r\n    };\r\n    const data = await editSubSubSubCategory(editingId, payload);\r\n    if (data.status == 200) {\r\n      toast.success(\"Edited Successfully\");\r\n      // getCategories();\r\n      await fetchSubSubCategory(selectedSubCategory);\r\n      setShowModal(false);\r\n      setEditingId(null);\r\n      setSubCategoryValue(\"\");\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    const res = await deleteSubSubSubCategory(id);\r\n    if (res.status == 200) {\r\n      toast.success(\"Deleted Successfully\");\r\n      // getCategories();\r\n      await fetchSubSubCategory(selectedSubCategory);\r\n      setConfirmDeleteId(null);\r\n    }\r\n  };\r\n\r\n  const confirmDelete = (id) => {\r\n    console.log(\"id to delete\", id);\r\n    setConfirmDeleteId(id); // Set the ID to confirm deletion\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setConfirmDeleteId(null); // Reset confirmation\r\n  };\r\n\r\n  const openModalForEdit = (item) => {\r\n    setEditingId(item._id);\r\n    setSubCategoryValue(item.name);\r\n    setShowModal(true);\r\n  };\r\n\r\n  const closeModal = () => {\r\n    setShowModal(false);\r\n    setEditingId(null);\r\n    setSubCategoryValue(\"\");\r\n  };\r\n\r\n  console.log(\"selectedCategory\", selectedCategory);\r\n  console.log(\"selectedSubSubCategory\", selectedSubSubCategory);\r\n  return (\r\n    <div>\r\n      <Tabs />\r\n      <div className=\"flex justify-between my-4\">\r\n        <div className=\"\">\r\n          {!isLoading ? (\r\n            <div className=\"min-h-[50px]\">\r\n              <select\r\n                defaultValue={storeData.categoriesState.categoryId}\r\n                className=\"ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1\"\r\n                // onChange={(e) => {\r\n                //   dispatch(changeCategoryState(e.target.value));\r\n                //   // setselectedCategory(e.target.value)\r\n                // }}\r\n              >\r\n                {categories?.map((item) => {\r\n                  return <option key={item._id} value={item._id}>{item.name}</option>;\r\n                })}\r\n              </select>\r\n              <select\r\n                className=\"ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1\"\r\n                onChange={(e) => setselectedSubCategory(e.target.value)}\r\n              >\r\n                {subCategories?.map((item) => {\r\n                  return <option key={item._id} value={item._id}>{item.name}</option>;\r\n                })}\r\n              </select>\r\n              {subSubCategories.length > 0 && (\r\n                <select\r\n                  className=\"ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1\"\r\n                  onChange={(e) => setSelectedSubSubCategory(e.target.value)}\r\n                >\r\n                  {subSubCategories?.map((item) => {\r\n                    return <option key={item._id} value={item._id}>{item.name}</option>;\r\n                  })}\r\n                </select>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div>...Loading</div>\r\n          )}\r\n        </div>\r\n        <button\r\n          onClick={() => document.getElementById(\"subSubSubModal\").classList.remove(\"hidden\")}\r\n          className=\"bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white px-5 py-2 rounded-full\"\r\n        >\r\n          Create New Sub-Sub-Sub-Category\r\n        </button>\r\n      </div>\r\n\r\n      <table className=\"w-full border border-[#EFF1F4] rounded-lg border-separate\">\r\n        <thead className=\"border-b border-[#EFF1F4]\">\r\n          <tr>\r\n            <th className=\"text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]\">Name</th>\r\n\r\n            <th className=\"text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]\">Created On</th>\r\n            {/* <th className=\"text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]\">\r\n              Action\r\n            </th> */}\r\n            <th className=\"text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]\"> Action</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody className=\"w-full\">\r\n          {isLoading ? (\r\n            <tr>\r\n              <td colSpan={6} className=\"text-center py-4\">\r\n                Loading...\r\n              </td>\r\n            </tr>\r\n          ) : subSubSubCategories?.length === 0 ? (\r\n            <tr>\r\n              <td colSpan={6} className=\"text-center py-4\">\r\n                No data found\r\n              </td>\r\n            </tr>\r\n          ) : (\r\n            subSubSubCategories.map((item) => (\r\n              <tr key={item._id} className=\"py-[10px] bg-white px-2\">\r\n                <td className=\"border-b border-[#EFF1F4] px-[10px] py-[10px] bg-white flex items-center\">\r\n                  <div>\r\n                    <img\r\n                      className=\"ml-2 w-[40px] h-[40px] rounded-full\"\r\n                      src={item.image || \"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/952747fd-94f3-4f7f-ba93-eaf188f302aa.png\"}\r\n                    />{\" \"}\r\n                  </div>\r\n                  <div className=\"ml-2\">{item?.name}</div>\r\n                </td>\r\n                {/* <td className=\"bg-white border-b border-[#EFF1F4]\">\r\n                  {item.categoryName}\r\n                </td>\r\n                <td className=\"bg-white border-b border-[#EFF1F4]\">\r\n                  {item.subCategoryName}\r\n                </td>\r\n                <td className=\"bg-white border-b border-[#EFF1F4]\">\r\n                  {item.subSubCategoryName}\r\n                </td> */}\r\n                <td className=\"bg-white border-b border-[#EFF1F4]\">{moment(item.createdAt).format(\"DD-MM-YYYY\")}</td>\r\n                {/* <td className=\"bg-white border-b border-[#EFF1F4]\"> */}\r\n                {/* Action buttons or icons here */}\r\n                {/* </td> */}\r\n                <td className=\"bg-white border-b border-[#EFF1F4] w-2/12\">\r\n                  <button className=\"px-3 py-2 rounded-lg border-blue-400 border-2 mr-2\" onClick={() => openModalForEdit(item)}>\r\n                    edit\r\n                  </button>\r\n                  <button className=\"px-2 py-2 rounded-lg bg-red-200 border-red-400 border-2 ml-2\" onClick={() => confirmDelete(item._id)}>\r\n                    delete\r\n                  </button>\r\n                </td>\r\n              </tr>\r\n            ))\r\n          )}\r\n        </tbody>\r\n      </table>\r\n\r\n      {/* Modal for creating new sub-sub-sub-category */}\r\n      <div id=\"subSubSubModal\" className=\"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden\">\r\n        <div className=\"bg-white rounded-lg w-full max-w-lg shadow-lg relative p-6\">\r\n          <div\r\n            className=\"absolute top-4 right-4 cursor-pointer\"\r\n            onClick={() => {\r\n              document.getElementById(\"subSubSubModal\").classList.add(\"hidden\");\r\n              setEnteredName(\"\");\r\n              setSelectedImage(null);\r\n              setSelectedImageOnUpload(null);\r\n            }}\r\n          >\r\n            <button className=\"text-gray-500 hover:text-red-600 text-2xl\">&times;</button>\r\n          </div>\r\n\r\n          <h2 className=\"text-xl font-semibold mb-4 text-center border-b pb-2\">Create Sub-Sub-Sub-Category</h2>\r\n\r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <label className=\"block mb-1\">Name</label>\r\n              <input\r\n                placeholder=\"Enter name\"\r\n                type=\"text\"\r\n                value={enteredName}\r\n                onChange={(e) => setEnteredName(e.target.value)}\r\n                className=\"w-full px-3 py-2 border rounded-md\"\r\n              />\r\n            </div>\r\n\r\n            <div>\r\n              <label className=\"block mb-1\">Category</label>\r\n              <select\r\n                value={storeData.categoriesState.categoryId || selectedCategory}\r\n                onChange={(e) => setselectedCategory(e.target.value)}\r\n                className=\"w-full px-3 py-2 border rounded-md\"\r\n              >\r\n                {categories.map((item) => (\r\n                  <option key={item._id} value={item._id}>\r\n                    {item.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n            <div>\r\n              <label className=\"block mb-1\">Sub-Category</label>\r\n              <select\r\n                value={selectedSubSubCategory || \"\"}\r\n                onChange={(e) => setselectedSubCategory(e.target.value)}\r\n                className=\"w-full px-3 py-2 border rounded-md\"\r\n              >\r\n                {subCategories.map((item) => (\r\n                  <option key={item._id} value={item._id}>\r\n                    {item.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n            <div>\r\n              <label className=\"block mb-1\">Sub-Sub-Category</label>\r\n              <select\r\n                value={selectedSubSubCategory || \"\"}\r\n                onChange={(e) => setSelectedSubSubCategory(e.target.value)}\r\n                className=\"w-full px-3 py-2 border rounded-md\"\r\n              >\r\n                {subSubCategories.map((item) => (\r\n                  <option key={item._id} value={item._id}>\r\n                    {item.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <div>\r\n              <input ref={fileInputRef} type=\"file\" className=\"hidden\" onChange={handleFileChange} />\r\n              <button\r\n                onClick={handleButtonClick}\r\n                className=\"w-full py-2 px-3 flex items-center justify-center bg-gray-100 text-gray-700 rounded-md\"\r\n              >\r\n                <RiFileUploadLine className=\"mr-2\" />\r\n                Upload Image\r\n              </button>\r\n            </div>\r\n\r\n            {selectedImage && (\r\n              <div className=\"relative mt-2\">\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedImage(null);\r\n                    setSelectedImageOnUpload(null);\r\n                  }}\r\n                  className=\"absolute -top-3 -right-3 bg-white rounded-full w-6 h-6 flex items-center justify-center shadow-md\"\r\n                >\r\n                  &times;\r\n                </button>\r\n                <img src={selectedImage} className=\"max-h-40 mx-auto rounded-md\" alt=\"Preview\" />\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"flex justify-center pt-4\">\r\n              <SubmitButton\r\n                isLoading={submitIsLoading}\r\n                InnerDiv={InnerDiv}\r\n                type=\"button\"\r\n                btnAction={submitSubSubSubCategory}\r\n                className=\"w-40\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Confirmation Dialog for Deletion */}\r\n      {confirmDeleteId && (\r\n        <div className=\"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50\">\r\n          <div className=\"bg-white rounded-lg w-full max-w-lg shadow-lg relative p-4\">\r\n            <h2 className=\"text-xl font-semibold\">Confirm Deletion</h2>\r\n            <p>Are you sure you want to delete this sub-category?</p>\r\n            <div className=\"flex justify-end mt-4\">\r\n              <button onClick={cancelDelete} className=\"mr-2 px-4 py-2 bg-gray-300 rounded\">\r\n                Cancel\r\n              </button>\r\n              <button onClick={() => handleDelete(confirmDeleteId)} className=\"px-4 py-2 bg-red-600 text-white rounded\">\r\n                Delete\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* EDIT/DELETE POPUP */}\r\n      {/* Modal for Create/Edit Sub-Category */}\r\n      {showModal && (\r\n        <div className=\"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50\">\r\n          <div className=\"bg-white rounded-lg w-full max-w-lg shadow-lg relative\">\r\n            <div className=\"flex justify-between p-4 border-b\">\r\n              <h2 className=\"text-xl font-semibold\">{editingId ? \"Edit Sub-Category\" : \"Create Sub-Category\"}</h2>\r\n              <button onClick={closeModal} className=\"text-gray-500 hover:text-red-600 text-xl font-bold\">\r\n                &times;\r\n              </button>\r\n            </div>\r\n            <div className=\"p-4\">\r\n              <label>Name</label>\r\n              <input\r\n                placeholder=\"Add Name\"\r\n                type=\"text\"\r\n                value={subCategoryValue}\r\n                onChange={(e) => setSubCategoryValue(e.target.value)}\r\n                className=\"px-2 py-2 rounded-md w-full border-[1px] border-[#dddddd] outline-none\"\r\n              />\r\n              <div className=\"my-2\">\r\n                <select\r\n                  className=\"mt-3 px-2 py-2 rounded-md w-full border-[1px] border-[#dddddd] outline-none\"\r\n                  onChange={(e) => setselectedCategory(e.target.value)}\r\n                >\r\n                  {categories.map((item) => (\r\n                    <option key={item._id} value={item._id}>\r\n                      {item.name}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n              <input ref={fileInputRef} type=\"file\" className=\"hidden\" onChange={handleFileChange} />\r\n              <div className=\"mt-2\">\r\n                <button onClick={handleButtonClick} className=\"py-3 px-2 flex items-center bg-[#f4f4f4] text-[#8C8C8C] w-full\">\r\n                  <RiFileUploadLine /> Upload Image\r\n                </button>\r\n              </div>\r\n              {selectedImage && (\r\n                <div className=\"my-3 relative w-fit\">\r\n                  <button\r\n                    onClick={() => setselectedImage(null)}\r\n                    className=\"absolute bg-[white] top-[-10px] rounded-full w-[20px] h-[20px] right-0 z-[100] text-gray-500 hover:text-red-600 text-xl font-bold\"\r\n                  >\r\n                    &times;\r\n                  </button>\r\n                  <img src={selectedImage} className=\"h-[100px]\" alt=\"image\" />\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"my-2 flex justify-end mx-4\">\r\n              <SubmitButton\r\n                isLoading={submitIsLoading}\r\n                InnerDiv={() => <div className=\"px-4\">Submit</div>}\r\n                type={\"button\"}\r\n                btnAction={editingId ? handleEdit : submitQuestion}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SubSubSubCategoryPage;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;AAoBA,MAAM,wBAAwB;IAC5B,MAAM,YAAY,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU,MAAM,SAAS;IACxD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,eAAe,CAAC,UAAU,IAAI;IACjG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,8CAA8C;IAC9C,UAAU;IACV,oDAAoD;IACpD,qCAAqC;IACrC,mEAAmE;IAEnE,aAAa;IACb,uDAAuD;IACvD,kCAAkC;IAClC,YAAY;IACZ,4EAA4E;IAC5E,UAAU;IACV,QAAQ;IACR,sBAAsB;IACtB,kEAAkE;IAClE,wDAAwD;IACxD,MAAM;IACN,KAAK;IAEL,MAAM,qBAAqB,OAAO;QAChC,IAAI,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE;QACtC,IAAI,SAAS,MAAM,IAAI,KAAK;YAC1B,SAAS,IAAI,CAAC,aAAa;YAC3B,iBAAiB,SAAS,IAAI,CAAC,aAAa;QAC9C;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,IAAI,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;YACzC,IAAI,SAAS,MAAM,IAAI,KAAK;gBAC1B,oBAAoB,SAAS,IAAI,CAAC,gBAAgB;YACpD;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC,OAAO;QACrB;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,MAAM,EAAE;YAC3B,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAiB;IAErB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,oBAAoB,MAAM,EAAE;YAC9B,sBAAsB;QACxB;IACF,GAAG;QAAC;KAAoB;IACxB,QAAQ,GAAG,CAAC,YAAY;IACxB,QAAQ,GAAG,CAAC,eAAe;IAC3B,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,qBAAqB;IACjC,QAAQ,GAAG,CAAC,oBAAoB;IAChC,QAAQ,GAAG,CAAC,uBAAuB;IACnC,QAAQ,GAAG,CAAC,uBAAuB;IAEnC,MAAM,eAAe,OAAO;QAC1B,aAAa;QAEb,IAAI;YACF,uBAAuB;YACvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD,EAAE;YACnD,IAAI,cAAc,MAAM,KAAK,KAAK;YAClC,MAAM,aAAa,cAAc,IAAI,EAAE;YACvC,uBAAuB;QACvB,qBAAqB;QACrB,0CAA0C;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,QAAQ,GAAG,CAAC,uBAAuB;IAEnC,MAAM,0BAA0B;QAC9B,mBAAmB;QACnB,IAAI;YACF,+BAA+B;YAC/B,IAAI,WAAW;YACf,IAAI,uBAAuB;gBACzB,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBACxB,MAAM,eAAe,MAAM,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE;gBACtC,WAAW,aAAa,IAAI,CAAC,GAAG;YAClC;YAEA,iBAAiB;YACjB,MAAM,UAAU;gBACd,MAAM;gBACN,OAAO;gBACP,kBAAkB;YACpB;YAEA,2BAA2B;YAC3B,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,uBAAoB,AAAD,EAAE;YAE5C,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,kCAAkC;gBAElC,6BAA6B;gBAC7B,SAAS,cAAc,CAAC,kBAAkB,SAAS,CAAC,GAAG,CAAC;gBACxD,eAAe;gBACf,iBAAiB;gBACjB,yBAAyB;gBACzB,aAAa,SAAS,IAAI,CAAC,gBAAgB;YAC7C,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,sCAAsC;QACtD,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,WAAW;QACf,qBAAO,8OAAC;YAAI,WAAU;sBAAO;;;;;;IAC/B;IAEA,MAAM,oBAAoB;QACxB,aAAa,OAAO,CAAC,KAAK;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,MAAM;YACR,yBAAyB;YACzB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,iBAAiB,OAAO,MAAM;YAChC;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;IACR,2BAA2B;IAC3B,kBAAkB;IACpB,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;QACjC,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,IAAI,SAAS,MAAM,IAAI,KAAK;YAC1B,0CAA0C;YAC1C,IAAI,OAAO,SAAS,IAAI,EAAE;YAC1B,cAAc;YACd,gCAAgC;YAChC,sCAAsC;YAEtC,IAAI,KAAK,MAAM,GAAG,GAAG;YACnB,6CAA6C;YAC7C,+BAA+B;YACjC;QACF;IACA,uBAAuB;IACzB;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,qBAAqB;YAErB,IAAI,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;YACzC,IAAI,SAAS,MAAM,IAAI,KAAK;gBAC1B,IAAI,OAAO,SAAS,IAAI,CAAC,gBAAgB,IAAI,EAAE;gBAC/C,oBAAoB;gBACpB,IAAI,KAAK,MAAM,EAAE;oBACf,aAAa,IAAI,CAAC,EAAE,CAAC,GAAG;oBACxB,0BAA0B,IAAI,CAAC,EAAE,CAAC,GAAG;gBACvC;YACA,4DAA4D;YAC9D;YACA,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,aAAa;QACf;IACF;IACA,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YACb,IAAI,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE;YACtC,IAAI,SAAS,MAAM,IAAI,KAAK;gBAC1B,2BAA2B;gBAC3B,IAAI,OAAO,SAAS,IAAI,EAAE,iBAAiB,EAAE;gBAC7C,iBAAiB;gBACjB,IAAI,KAAK,MAAM,GAAG,GAAG;oBACnB,uBAAuB,IAAI,CAAC,EAAE,CAAC,GAAG;gBAClC,mCAAmC;gBACrC;gBACA,aAAa;YACf;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,GAAG,CAAC;QACd;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,UAAU,eAAe,CAAC,UAAU;KAAC;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,eAAe,CAAC,UAAU,EAAE;YACxC,iBAAiB,UAAU,eAAe,CAAC,UAAU;QACvD;IACF,GAAG;QAAC,UAAU,eAAe,CAAC,UAAU;KAAC;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,oBAAoB,MAAM,EAAE;YAC9B,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAoB;IAExB,MAAM,aAAa;QACjB,MAAM,UAAU;YACd,MAAM;QAER;QACA,MAAM,OAAO,MAAM,CAAA,GAAA,0HAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW;QACpD,IAAI,KAAK,MAAM,IAAI,KAAK;YACtB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,mBAAmB;YACnB,MAAM,oBAAoB;YAC1B,aAAa;YACb,aAAa;YACb,oBAAoB;QACtB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,MAAM,MAAM,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD,EAAE;QAC1C,IAAI,IAAI,MAAM,IAAI,KAAK;YACrB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,mBAAmB;YACnB,MAAM,oBAAoB;YAC1B,mBAAmB;QACrB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,mBAAmB,KAAK,iCAAiC;IAC3D;IAEA,MAAM,eAAe;QACnB,mBAAmB,OAAO,qBAAqB;IACjD;IAEA,MAAM,mBAAmB,CAAC;QACxB,aAAa,KAAK,GAAG;QACrB,oBAAoB,KAAK,IAAI;QAC7B,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,aAAa;QACb,aAAa;QACb,oBAAoB;IACtB;IAEA,QAAQ,GAAG,CAAC,oBAAoB;IAChC,QAAQ,GAAG,CAAC,0BAA0B;IACtC,qBACE,8OAAC;;0BACC,8OAAC,yHAAA,CAAA,UAAI;;;;;0BACL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,CAAC,0BACA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,cAAc,UAAU,eAAe,CAAC,UAAU;oCAClD,WAAU;8CAMT,YAAY,IAAI,CAAC;wCAChB,qBAAO,8OAAC;4CAAsB,OAAO,KAAK,GAAG;sDAAG,KAAK,IAAI;2CAArC,KAAK,GAAG;;;;;oCAC9B;;;;;;8CAEF,8OAAC;oCACC,WAAU;oCACV,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;8CAErD,eAAe,IAAI,CAAC;wCACnB,qBAAO,8OAAC;4CAAsB,OAAO,KAAK,GAAG;sDAAG,KAAK,IAAI;2CAArC,KAAK,GAAG;;;;;oCAC9B;;;;;;gCAED,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;oCACC,WAAU;oCACV,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;8CAExD,kBAAkB,IAAI,CAAC;wCACtB,qBAAO,8OAAC;4CAAsB,OAAO,KAAK,GAAG;sDAAG,KAAK,IAAI;2CAArC,KAAK,GAAG;;;;;oCAC9B;;;;;;;;;;;iDAKN,8OAAC;sCAAI;;;;;;;;;;;kCAGT,8OAAC;wBACC,SAAS,IAAM,SAAS,cAAc,CAAC,kBAAkB,SAAS,CAAC,MAAM,CAAC;wBAC1E,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAM,WAAU;;kCACf,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAEzE,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAIzE,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;;;;;;;;;;;;kCAG7E,8OAAC;wBAAM,WAAU;kCACd,0BACC,8OAAC;sCACC,cAAA,8OAAC;gCAAG,SAAS;gCAAG,WAAU;0CAAmB;;;;;;;;;;mCAI7C,qBAAqB,WAAW,kBAClC,8OAAC;sCACC,cAAA,8OAAC;gCAAG,SAAS;gCAAG,WAAU;0CAAmB;;;;;;;;;;mCAK/C,oBAAoB,GAAG,CAAC,CAAC,qBACvB,8OAAC;gCAAkB,WAAU;;kDAC3B,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;;kEACC,8OAAC;wDACC,WAAU;wDACV,KAAK,KAAK,KAAK,IAAI;;;;;;oDAClB;;;;;;;0DAEL,8OAAC;gDAAI,WAAU;0DAAQ,MAAM;;;;;;;;;;;;kDAW/B,8OAAC;wCAAG,WAAU;kDAAsC,CAAA,GAAA,gIAAA,CAAA,UAAM,AAAD,EAAE,KAAK,SAAS,EAAE,MAAM,CAAC;;;;;;kDAIlF,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAO,WAAU;gDAAqD,SAAS,IAAM,iBAAiB;0DAAO;;;;;;0DAG9G,8OAAC;gDAAO,WAAU;gDAA+D,SAAS,IAAM,cAAc,KAAK,GAAG;0DAAG;;;;;;;;;;;;;+BA3BpH,KAAK,GAAG;;;;;;;;;;;;;;;;0BAsCzB,8OAAC;gBAAI,IAAG;gBAAiB,WAAU;0BACjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,SAAS;gCACP,SAAS,cAAc,CAAC,kBAAkB,SAAS,CAAC,GAAG,CAAC;gCACxD,eAAe;gCACf,iBAAiB;gCACjB,yBAAyB;4BAC3B;sCAEA,cAAA,8OAAC;gCAAO,WAAU;0CAA4C;;;;;;;;;;;sCAGhE,8OAAC;4BAAG,WAAU;sCAAuD;;;;;;sCAErE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAa;;;;;;sDAC9B,8OAAC;4CACC,aAAY;4CACZ,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAa;;;;;;sDAC9B,8OAAC;4CACC,OAAO,UAAU,eAAe,CAAC,UAAU,IAAI;4CAC/C,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,WAAU;sDAET,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oDAAsB,OAAO,KAAK,GAAG;8DACnC,KAAK,IAAI;mDADC,KAAK,GAAG;;;;;;;;;;;;;;;;8CAM3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAa;;;;;;sDAC9B,8OAAC;4CACC,OAAO,0BAA0B;4CACjC,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;4CACtD,WAAU;sDAET,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oDAAsB,OAAO,KAAK,GAAG;8DACnC,KAAK,IAAI;mDADC,KAAK,GAAG;;;;;;;;;;;;;;;;8CAM3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAa;;;;;;sDAC9B,8OAAC;4CACC,OAAO,0BAA0B;4CACjC,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;4CACzD,WAAU;sDAET,iBAAiB,GAAG,CAAC,CAAC,qBACrB,8OAAC;oDAAsB,OAAO,KAAK,GAAG;8DACnC,KAAK,IAAI;mDADC,KAAK,GAAG;;;;;;;;;;;;;;;;8CAO3B,8OAAC;;sDACC,8OAAC;4CAAM,KAAK;4CAAc,MAAK;4CAAO,WAAU;4CAAS,UAAU;;;;;;sDACnE,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,8IAAA,CAAA,mBAAgB;oDAAC,WAAU;;;;;;gDAAS;;;;;;;;;;;;;gCAKxC,+BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;gDACP,iBAAiB;gDACjB,yBAAyB;4CAC3B;4CACA,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CAAI,KAAK;4CAAe,WAAU;4CAA8B,KAAI;;;;;;;;;;;;8CAIzE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2IAAA,CAAA,UAAY;wCACX,WAAW;wCACX,UAAU;wCACV,MAAK;wCACL,WAAW;wCACX,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnB,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;sCACtC,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,SAAS;oCAAc,WAAU;8CAAqC;;;;;;8CAG9E,8OAAC;oCAAO,SAAS,IAAM,aAAa;oCAAkB,WAAU;8CAA0C;;;;;;;;;;;;;;;;;;;;;;;YAUjH,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyB,YAAY,sBAAsB;;;;;;8CACzE,8OAAC;oCAAO,SAAS;oCAAY,WAAU;8CAAqD;;;;;;;;;;;;sCAI9F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAM;;;;;;8CACP,8OAAC;oCACC,aAAY;oCACZ,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oCACnD,WAAU;;;;;;8CAEZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;kDAElD,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;gDAAsB,OAAO,KAAK,GAAG;0DACnC,KAAK,IAAI;+CADC,KAAK,GAAG;;;;;;;;;;;;;;;8CAM3B,8OAAC;oCAAM,KAAK;oCAAc,MAAK;oCAAO,WAAU;oCAAS,UAAU;;;;;;8CACnE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAO,SAAS;wCAAmB,WAAU;;0DAC5C,8OAAC,8IAAA,CAAA,mBAAgB;;;;;4CAAG;;;;;;;;;;;;gCAGvB,+BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CAAI,KAAK;4CAAe,WAAU;4CAAY,KAAI;;;;;;;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2IAAA,CAAA,UAAY;gCACX,WAAW;gCACX,UAAU,kBAAM,8OAAC;wCAAI,WAAU;kDAAO;;;;;;gCACtC,MAAM;gCACN,WAAW,YAAY,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;uCAEe", "debugId": null}}]}