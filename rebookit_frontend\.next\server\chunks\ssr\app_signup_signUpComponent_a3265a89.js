module.exports = {

"[project]/app/signup/signUpComponent.js [app-rsc] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/app_signup_signUpComponent_3de9c359.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/app/signup/signUpComponent.js [app-rsc] (ecmascript, next/dynamic entry)");
    });
});
}}),

};