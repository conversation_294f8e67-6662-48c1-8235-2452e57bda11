"use client";

import { useEffect, useState } from "react";
// import Tabs from "../Tabs";
import { BsThreeDotsVertical } from "react-icons/bs";
import { getMembers } from "@/app/service/membership";
import { toast } from "react-toastify";
import moment from "moment";
import { debouncFunc } from "@/app/utils/utils";
import Pagination from "@/app/components/common/Pagination";
import MagnifierIcon from "@/public/icons/magnifier_icon.svg";
import Image from "next/image";
import { getSupportRequest, updateSupportRequest } from "../service/support";
import Action from "./Action";

function Member({ setSelectedTab }) {
    const [membersList, setmembersList] = useState([]);
    const [supportRequestList, setsupportRequestList] = useState([])
    const [isLoading, setisLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [page, setPage] = useState(1)
    const [pageSize, setPageSize] = useState(10);
    const [totalItems, setTotalItems] = useState(0);
    const [totalPages, setTotalPages] = useState(1);
    useEffect(() => {
        // setSelectedTab(0); // <- maybe causing loop
        // getMembersFunc();
        // getMembersFunc()
        getSupportRequstFunc()
    }, [pageSize, currentPage]);



    // let activeInactive={
    //     active:return <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div><div className="text-[#027A48]"> {item.status}</div></div>

    //     ,inActive:<div className="rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"></div><div className="text-[#F15046]"> {item.status}</div></div>
    // }

    const ActiveInactive = (name, item) => {
        console.log("name status", name);
        if (name == "active") {
            return (
                <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center ">
                    {" "}
                    <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div>
                    <div className="text-[#027A48]"> {item.status}</div>
                </div>
            );
        } else if (name == "pending") {
            return (
                <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center ">
                    {" "}
                    <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div>
                    <div className="text-[#027A48]"> {item.status}</div>
                </div>
            );
        } else {
            return (
                <div className="rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center ">
                    {" "}
                    <div className="w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"></div>
                    <div className="text-[#F15046]"> {item.status}</div>
                </div>
            );
        }
    };

    const getSupportRequstFunc = async (value) => {
        //   debugger
        setisLoading(true);
        let payload = {};
        let query = "?";
        if (currentPage) {
            query = query + `&page=${currentPage}`;
        }
        if (pageSize) {
            query = query + `&pageSize=${pageSize}`;
        }
        if (value) {
            payload.subject = value;
        }
        console.log("query", query)
        let response = await getSupportRequest(payload, query)
        if (response.status == 200) {
            console.log("response", response)
            setsupportRequestList(response.data.data)
            setPageSize(response.data.pageSize);
            setTotalItems(response.data.totalCount);
            setTotalPages(response.data.totalPages);
        }
        setisLoading(false)
        // let membersData = await getMembers(payload, query);
        // if (membersData.status == 200) {
        //   setmembersList(membersData.data?.data);
        //   // setCurrentPage()

        // }
        // setisLoading(false);
    };

    const updateSupportRequestFunc = async (id) => {
        let query = `?id=${id}`
        let payload = {
            status: true
        }
        let response = await updateSupportRequest(payload, query)
        if (response.status == 200) {
            toast.success(response.data.message || "Updated")
            getSupportRequstFunc()
        }
    }

    // const getSupportRequstFunc = async () => {
    //     let payload = {
    //         name: "",
    //         subject: ""
    //     }
    //     let query = `?page=${page}&pageSize=${pageSize}`
    //     let response = await getSupportRequest(query, payload)
    //     if (response) {

    //     }
    // }
    console.log("membersList", membersList);
    let debounceHandle = debouncFunc((e) => getSupportRequstFunc(e.target.value), 1000);
    console.log(supportRequestList, "supportRequestList")

    return (
        <div className="bg-white rounded-lg p-3 ">
            {/* <Tabs /> */}
            {/* <div className="flex justify-end mb-2">
        <input
          placeholder="Search Items..."
          className="rounded-full border border-gray-400 ps-3 p-2"
          onChange={debounceHandle}
        />
      </div> */}
            <div className="flex items-center  justify-end mb-2">
                <div className="w-70 bg-gray-50 flex rounded-lg items-center">
                    <div className="w-6 h-6 ml-2 relative overflow-hidden rounded-lg">
                        <Image
                            src={MagnifierIcon}
                            alt="Search Icon"
                            fill
                            className="object-cover"
                            sizes="24px"
                        />
                    </div>
                    <input
                        placeholder="Search Items..."
                        className="rounded-lg outline-none bg-gray-50 ps-2 p-2"
                        onChange={debounceHandle}
                    />
                </div>
            </div>

            <table className="w-full border border-[#EFF1F4] rounded-lg overflow-hidden table-fixed">
                <thead className="bg-[#FAFBFB]">
                    <tr>
                        <th className="px-4 py-3 text-left font-medium text-[14px] ">
                            Name
                        </th>
                        <th className="px-3 py-3 text-left font-medium text-[14px] ">
                            Email
                        </th>
                        <th className="px-3 py-3 text-left font-medium text-[14px] ">
                            Subject
                        </th>
                        <th className="px-3 py-3 text-left font-medium text-[14px] ">
                            Concern
                        </th>
                        <th className="px-3 py-3 text-left font-medium text-[14px] ">
                            Status
                        </th>
                        <th className="px-3 py-3 text-left font-medium text-[14px] ">
                            Action
                        </th>
                    </tr>
                </thead>
                <tbody className="divide-y divide-[#EFF1F4]">
                    {!isLoading ? (
                        supportRequestList.length > 0 ? (
                            supportRequestList?.map((item, index) => (
                                <tr key={index} className="hover:bg-gray-50 transition-colors">
                                    <td className="px-4 text-[14px] max-w-[100px]" title={item.name}>
                                        {item.name}
                                    </td>
                                    <td className=" px-3 py-3  text-[14px] text-gray-700 overflow-none" title={item.email}>
                                        <span className="block max-w-[200px] overflow-hidden whitespace-nowrap text-ellipsis"> {item?.email || "NA"}</span>
                                    </td>
                                    <td className="px-3 py-3 text-[14px] w-fit relative group " >
                                        <span className="cursor-pointer">{item.subject}</span>
                                         <div className="absolute left-0 top-[-20px] mt-1 w-max max-w-xs bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition duration-200 z-10">
                                            {item.subject}
                                        </div>
                                    </td>
                                    <td className="px-3 py-3 text-[14px] text-sm relative group " >
                                        <span className="cursor-pointer"> {item.content || "NA"}</span>
                                        <div className="absolute left-0 top-[-20px] mt-1 w-max max-w-xs bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition duration-200 z-10">
                                            {item.content}
                                        </div>
                                    </td>
                                    <td className="px-3 text-[14px] py-3">
                                        {item.status ? "Close" : "Open"}
                                    </td>
                                    <td className="px-3 text-[14px] py-3 relative">
                                        <Action item={item} updateSupportRequestFunc={updateSupportRequestFunc} />
                                    </td>
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan={6} className="text-center py-8 text-gray-500">
                                    No Data Found
                                </td>
                            </tr>
                        )
                    ) : (
                        <tr>
                            <td colSpan={6} className="text-center py-8">
                                <div className="flex justify-center">
                                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                                </div>
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>

            <Pagination
                setPageSize={setPageSize}
                setCurrentPage={setCurrentPage}
                getListing={getSupportRequstFunc}
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                pageSize={pageSize}
            />
        </div>
    );
}

export default Member;
