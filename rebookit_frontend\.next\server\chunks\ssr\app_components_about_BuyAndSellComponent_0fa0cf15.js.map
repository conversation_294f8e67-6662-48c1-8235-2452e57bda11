{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/components/about/BuyAndSellComponent.js"], "sourcesContent": ["import Link from \"next/link\";\r\nimport React from \"react\";\r\n\r\nfunction BuyAndSellComponent() {\r\n  return (\r\n    <section\r\n      className=\"rounded-[20px] px-5 pt-[31px] pb-7 md:py-3 md:px-12 lg:px-16 flex flex-col md:flex-row md:items-center justify-between\"\r\n      style={{\r\n        background: \"linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%)\",\r\n      }}\r\n    >\r\n      <div>\r\n        <h3 className=\"mb-2.5 text-white text-[22px] md:text-[34px] font-semibold  leading-normal lg:leading-[33px]\">\r\n          Buy and sell your books in your community\r\n        </h3>\r\n        <p className=\"mb-5 font-light text-white text-[14px]\">\r\n          Connect with readers and sellers around you.\r\n        </p>\r\n        <Link href={\"/become-seller\"}>\r\n          <button\r\n            type=\"button\"\r\n            aria-label=\"Sell your book through ReBookIt community platform\"\r\n            className=\"cursor-pointer w-[150px] text-[12.5px] py-2.5 px-8 text-center font-semibold bg-white text-black rounded-full mb-7 md:mb-0\"\r\n          >\r\n            Sell book\r\n          </button>\r\n        </Link>\r\n      </div>\r\n\r\n      <figure className=\"flex justify-center items-center h-[250px]\">\r\n        <img\r\n          src={\"/images/book_img.png\"}\r\n          alt=\"Illustration of an open book for community book trading\"\r\n          className=\"h-full\"\r\n        />\r\n      </figure>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default BuyAndSellComponent;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,SAAS;IACP,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,YAAY;QACd;;0BAEA,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA+F;;;;;;kCAG7G,8OAAC;wBAAE,WAAU;kCAAyC;;;;;;kCAGtD,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM;kCACV,cAAA,8OAAC;4BACC,MAAK;4BACL,cAAW;4BACX,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAML,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBACC,KAAK;oBACL,KAAI;oBACJ,WAAU;;;;;;;;;;;;;;;;;AAKpB;uCAEe", "debugId": null}}]}