{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend/app/components/book-listing/Mapview.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { AdvancedMarker, APIProvider, Map } from \"@vis.gl/react-google-maps\";\r\nimport \"./mapview.scss\";\r\n\r\nimport Skeleton from \"react-loading-skeleton\";\r\nimport \"react-loading-skeleton/dist/skeleton.css\";\r\nimport { ItemKindEnum } from \"@/app/config/constant\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { updateUserLocationData } from \"@/app/redux/slices/storeSlice\";\r\nimport {\r\n  formatWithCommas,\r\n  getCurrentLocationAndAddress,\r\n  parseGeocodeResponse,\r\n} from \"../../utils/utils\";\r\n// const JAMAICA_CENTER = { lat: 17.9714, lng: -76.7931 };\r\nconst JAMAICA_ZOOM = 12;\r\n\r\nconst googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY;\r\n\r\nconst Mapview = ({\r\n  width,\r\n  height,\r\n  data,\r\n  fullScreen = false,\r\n  center,\r\n  isSingleBookDetails,\r\n  onExitFullScreen = () => {},\r\n}) => {\r\n  const userLocationData = useSelector(\r\n    (state) => state.storeData.userLocationData\r\n  );\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [zoom, setZoom] = useState(JAMAICA_ZOOM);\r\n  const [locationAllowed, setLocationAllowed] = useState(false);\r\n  const [mapCenter, setMapCenter] = useState(\r\n    userLocationData &&\r\n      typeof userLocationData.latitude === \"number\" &&\r\n      typeof userLocationData.longitude === \"number\"\r\n      ? {\r\n          lat: userLocationData.latitude,\r\n          lng: userLocationData.longitude,\r\n        }\r\n      : null\r\n  );\r\n  const [mapKey, setMapKey] = useState(0);\r\n  const router = useRouter();\r\n  const mapRef = useRef(null);\r\n  const dispatch = useDispatch();\r\n  const locations = Array.isArray(data) ? data : [data];\r\n\r\n  const toShoInlocation = (text) => {\r\n    if (!text) return \"\";\r\n    if (text.__t == ItemKindEnum.BookItem) {\r\n      return \"Book\";\r\n    } else if (text.__t == ItemKindEnum.EventItem) {\r\n      return \"Event\";\r\n    } else if (text.__t == ItemKindEnum.ExtracurricularActivityItem) {\r\n      return \"Activity\";\r\n    } else if (text.__t == ItemKindEnum.ScholarshipAwardItem) {\r\n      return \"Award\";\r\n    } else if (text.__t == ItemKindEnum.SchoolItem) {\r\n      return \"School\";\r\n    } else if (text.__t == ItemKindEnum.TutorItem) {\r\n      return \"Tutor\";\r\n    } else {\r\n      return \"\";\r\n    }\r\n  };\r\n\r\n  // Initial load: get current location and set in redux and local state\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n    const fetchAndSetLocation = async () => {\r\n      try {\r\n        // If we have valid coordinates in userLocationData, use them\r\n        if (\r\n          userLocationData &&\r\n          typeof userLocationData.latitude === \"number\" &&\r\n          typeof userLocationData.longitude === \"number\"\r\n        ) {\r\n          if (isMounted) {\r\n            setMapCenter({\r\n              lat: userLocationData.latitude,\r\n              lng: userLocationData.longitude,\r\n            });\r\n            setZoom(12);\r\n            setLocationAllowed(true);\r\n            setLoading(false);\r\n          }\r\n          return;\r\n        }\r\n\r\n        // Only fetch location if we don't have coordinates and it's not parish selection\r\n        if (!userLocationData?.isParishSelection) {\r\n          const addressData = await getCurrentLocationAndAddress(\r\n            \"getfullAddress\"\r\n          );\r\n          const parsedAddress = parseGeocodeResponse(addressData);\r\n\r\n          if (parsedAddress && isMounted) {\r\n            // Save in redux in the required format\r\n            const locationData = {\r\n              locality: parsedAddress.locality || \"\",\r\n              latitude: parsedAddress.latitude || \"\",\r\n              longitude: parsedAddress.longitude || \"\",\r\n              currentLocation: true,\r\n            };\r\n            dispatch(updateUserLocationData(locationData));\r\n\r\n            // Set map center for the map and marker\r\n            setMapCenter({\r\n              lat: parsedAddress.latitude,\r\n              lng: parsedAddress.longitude,\r\n            });\r\n            setZoom(12);\r\n            setLocationAllowed(true);\r\n          } else if (isMounted) {\r\n            setLocationAllowed(false);\r\n          }\r\n        } else {\r\n          // For parish selection, we need to set a default center\r\n          // Use Jamaica center as fallback\r\n          setMapCenter({\r\n            lat: 17.9714,\r\n            lng: -76.7931,\r\n          });\r\n          setZoom(10);\r\n          setLocationAllowed(false);\r\n        }\r\n      } catch (err) {\r\n        // fallback to Jamaica center\r\n        if (isMounted) {\r\n          setMapCenter({\r\n            lat: 17.9714,\r\n            lng: -76.7931,\r\n          });\r\n          setZoom(10);\r\n          setLocationAllowed(false);\r\n        }\r\n      } finally {\r\n        if (isMounted) setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchAndSetLocation();\r\n\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (\r\n      userLocationData &&\r\n      typeof userLocationData.latitude === \"number\" &&\r\n      typeof userLocationData.longitude === \"number\"\r\n    ) {\r\n      setMapCenter({\r\n        lat: userLocationData.latitude,\r\n        lng: userLocationData.longitude,\r\n      });\r\n      setLocationAllowed(true);\r\n      setMapKey((prev) => prev + 1);\r\n    } else if (userLocationData?.isParishSelection) {\r\n      // For parish selection, use Jamaica center\r\n      setMapCenter({\r\n        lat: 17.9714,\r\n        lng: -76.7931,\r\n      });\r\n      setZoom(10);\r\n      setLocationAllowed(false);\r\n      setMapKey((prev) => prev + 1);\r\n    } else {\r\n      setLocationAllowed(false);\r\n    }\r\n  }, [userLocationData]);\r\n\r\n  // Handler to update mapCenter when the user moves the map\r\n  const handleCenterChanged = () => {\r\n    if (mapRef.current) {\r\n      const center = mapRef.current.getCenter();\r\n      if (center) {\r\n        setMapCenter({\r\n          lat: center.lat(),\r\n          lng: center.lng(),\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"loader-container\">\r\n        <Skeleton height={height} />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const mapStyle = fullScreen\r\n    ? {\r\n        position: \"fixed\",\r\n        top: 0,\r\n        left: 0,\r\n        width: \"100%\",\r\n        height: \"100%\",\r\n        zIndex: 9999,\r\n        borderRadius: 0,\r\n      }\r\n    : {\r\n        width,\r\n        height,\r\n        borderRadius: \"15px\",\r\n      };\r\n\r\n  console.log(\"===\", isSingleBookDetails, center, mapCenter);\r\n  return (\r\n    <APIProvider apiKey={googleMapsApiKey}>\r\n      <Map\r\n        key={mapKey}\r\n        defaultCenter={isSingleBookDetails ? center : mapCenter}\r\n        defaultZoom={zoom}\r\n        // Remove the center prop to allow the map to be movable\r\n        style={mapStyle}\r\n        mapId=\"DEMO_MAP_ID\"\r\n        gestureHandling={\"greedy\"}\r\n        onLoad={(map) => {\r\n          mapRef.current = map;\r\n        }}\r\n        onCenterChanged={handleCenterChanged}\r\n        options={{\r\n          draggable: true,\r\n          scrollwheel: true,\r\n          clickableIcons: true,\r\n          zoomControl: true,\r\n          disableDoubleClickZoom: false,\r\n        }}\r\n        disableDefaultUI={false}\r\n      >\r\n        {!isSingleBookDetails && locationAllowed && mapCenter && userLocationData?.currentLocation && (\r\n          <AdvancedMarker\r\n            key=\"user-location\"\r\n            position={mapCenter}\r\n            title=\"Your Location\"\r\n          >\r\n            <div className=\"custom-marker p-2 bg-blue-500 text-white rounded-full border-2 border-white shadow-lg\">\r\n              <span role=\"img\" aria-label=\"You\">\r\n                📍\r\n              </span>\r\n            </div>\r\n          </AdvancedMarker>\r\n        )}\r\n\r\n        {locations.map((list, index) => {\r\n          const coords = list?.address?.geometry?.location?.coordinates;\r\n          if (!coords || coords.length < 2) return null;\r\n\r\n          return (\r\n            <AdvancedMarker\r\n              key={index}\r\n              position={{\r\n                lat: coords[1],\r\n                lng: coords[0],\r\n              }}\r\n              title={list?.createdByDoc?.firstName}\r\n            >\r\n              <div\r\n                className=\"custom-marker relative z-10 hover:z-[9999] p-3 bg-white rounded shadow-lg\"\r\n                onClick={() => router.push(`/book-detail?id=${list._id}`)}\r\n              >\r\n                <div className=\"w-fit mx-auto\">\r\n                  <img\r\n                    className=\"border aspect-[3/4] mx-auto w-[50px]\"\r\n                    src={list.images[0]}\r\n                    alt={list.title}\r\n                  />\r\n                </div>\r\n                <div className=\"text-md mt-2\">{toShoInlocation(list)}</div>\r\n                <div className=\"extra-content text-md mt-2 max-w-[100px]\">\r\n                  {list.title}\r\n                </div>\r\n                <div className=\"text-md mt-2\">\r\n                  J${formatWithCommas(list?.price)}\r\n                </div>\r\n                <div className=\"triangle absolute bottom-[-15px] left-[30%]\"></div>\r\n              </div>\r\n            </AdvancedMarker>\r\n          );\r\n        })}\r\n      </Map>\r\n      {fullScreen && (\r\n        <button\r\n          className=\"fixed top-[10px] right-[10px] z-[10000] h-[40px] w-[40px] border-0 rounded-none font-black text-white flex items-center justify-center shadow\"\r\n          style={{\r\n            background:\r\n              \"linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%)\",\r\n          }}\r\n          onClick={onExitFullScreen}\r\n        >\r\n          ✕\r\n        </button>\r\n      )}\r\n    </APIProvider>\r\n  );\r\n};\r\n\r\nexport default Mapview;\r\n"], "names": [], "mappings": ";;;AAoByB;;AAlBzB;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAiBA,0DAA0D;AAC1D,MAAM,eAAe;AAErB,MAAM;AAEN,MAAM,UAAU,CAAC,EACf,KAAK,EACL,MAAM,EACN,IAAI,EACJ,aAAa,KAAK,EAClB,MAAM,EACN,mBAAmB,EACnB,mBAAmB,KAAO,CAAC,EAC5B;;IACC,MAAM,mBAAmB,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;iDACjC,CAAC,QAAU,MAAM,SAAS,CAAC,gBAAgB;;IAG7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC,oBACE,OAAO,iBAAiB,QAAQ,KAAK,YACrC,OAAO,iBAAiB,SAAS,KAAK,WACpC;QACE,KAAK,iBAAiB,QAAQ;QAC9B,KAAK,iBAAiB,SAAS;IACjC,IACA;IAEN,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,YAAY,MAAM,OAAO,CAAC,QAAQ,OAAO;QAAC;KAAK;IAErD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,QAAQ,EAAE;YACrC,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,SAAS,EAAE;YAC7C,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,2BAA2B,EAAE;YAC/D,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,oBAAoB,EAAE;YACxD,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,UAAU,EAAE;YAC9C,OAAO;QACT,OAAO,IAAI,KAAK,GAAG,IAAI,4HAAA,CAAA,eAAY,CAAC,SAAS,EAAE;YAC7C,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,YAAY;YAChB,MAAM;yDAAsB;oBAC1B,IAAI;wBACF,6DAA6D;wBAC7D,IACE,oBACA,OAAO,iBAAiB,QAAQ,KAAK,YACrC,OAAO,iBAAiB,SAAS,KAAK,UACtC;4BACA,IAAI,WAAW;gCACb,aAAa;oCACX,KAAK,iBAAiB,QAAQ;oCAC9B,KAAK,iBAAiB,SAAS;gCACjC;gCACA,QAAQ;gCACR,mBAAmB;gCACnB,WAAW;4BACb;4BACA;wBACF;wBAEA,iFAAiF;wBACjF,IAAI,CAAC,kBAAkB,mBAAmB;4BACxC,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,+BAA4B,AAAD,EACnD;4BAEF,MAAM,gBAAgB,CAAA,GAAA,wHAAA,CAAA,uBAAoB,AAAD,EAAE;4BAE3C,IAAI,iBAAiB,WAAW;gCAC9B,uCAAuC;gCACvC,MAAM,eAAe;oCACnB,UAAU,cAAc,QAAQ,IAAI;oCACpC,UAAU,cAAc,QAAQ,IAAI;oCACpC,WAAW,cAAc,SAAS,IAAI;oCACtC,iBAAiB;gCACnB;gCACA,SAAS,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE;gCAEhC,wCAAwC;gCACxC,aAAa;oCACX,KAAK,cAAc,QAAQ;oCAC3B,KAAK,cAAc,SAAS;gCAC9B;gCACA,QAAQ;gCACR,mBAAmB;4BACrB,OAAO,IAAI,WAAW;gCACpB,mBAAmB;4BACrB;wBACF,OAAO;4BACL,wDAAwD;4BACxD,iCAAiC;4BACjC,aAAa;gCACX,KAAK;gCACL,KAAK,CAAC;4BACR;4BACA,QAAQ;4BACR,mBAAmB;wBACrB;oBACF,EAAE,OAAO,KAAK;wBACZ,6BAA6B;wBAC7B,IAAI,WAAW;4BACb,aAAa;gCACX,KAAK;gCACL,KAAK,CAAC;4BACR;4BACA,QAAQ;4BACR,mBAAmB;wBACrB;oBACF,SAAU;wBACR,IAAI,WAAW,WAAW;oBAC5B;gBACF;;YAEA;YAEA;qCAAO;oBACL,YAAY;gBACd;;QACF;4BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IACE,oBACA,OAAO,iBAAiB,QAAQ,KAAK,YACrC,OAAO,iBAAiB,SAAS,KAAK,UACtC;gBACA,aAAa;oBACX,KAAK,iBAAiB,QAAQ;oBAC9B,KAAK,iBAAiB,SAAS;gBACjC;gBACA,mBAAmB;gBACnB;yCAAU,CAAC,OAAS,OAAO;;YAC7B,OAAO,IAAI,kBAAkB,mBAAmB;gBAC9C,2CAA2C;gBAC3C,aAAa;oBACX,KAAK;oBACL,KAAK,CAAC;gBACR;gBACA,QAAQ;gBACR,mBAAmB;gBACnB;yCAAU,CAAC,OAAS,OAAO;;YAC7B,OAAO;gBACL,mBAAmB;YACrB;QACF;4BAAG;QAAC;KAAiB;IAErB,0DAA0D;IAC1D,MAAM,sBAAsB;QAC1B,IAAI,OAAO,OAAO,EAAE;YAClB,MAAM,SAAS,OAAO,OAAO,CAAC,SAAS;YACvC,IAAI,QAAQ;gBACV,aAAa;oBACX,KAAK,OAAO,GAAG;oBACf,KAAK,OAAO,GAAG;gBACjB;YACF;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,gKAAA,CAAA,UAAQ;gBAAC,QAAQ;;;;;;;;;;;IAGxB;IAEA,MAAM,WAAW,aACb;QACE,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,cAAc;IAChB,IACA;QACE;QACA;QACA,cAAc;IAChB;IAEJ,QAAQ,GAAG,CAAC,OAAO,qBAAqB,QAAQ;IAChD,qBACE,6LAAC,qLAAA,CAAA,cAAW;QAAC,QAAQ;;0BACnB,6LAAC,qLAAA,CAAA,MAAG;gBAEF,eAAe,sBAAsB,SAAS;gBAC9C,aAAa;gBACb,wDAAwD;gBACxD,OAAO;gBACP,OAAM;gBACN,iBAAiB;gBACjB,QAAQ,CAAC;oBACP,OAAO,OAAO,GAAG;gBACnB;gBACA,iBAAiB;gBACjB,SAAS;oBACP,WAAW;oBACX,aAAa;oBACb,gBAAgB;oBAChB,aAAa;oBACb,wBAAwB;gBAC1B;gBACA,kBAAkB;;oBAEjB,CAAC,uBAAuB,mBAAmB,aAAa,kBAAkB,iCACzE,6LAAC,qLAAA,CAAA,iBAAc;wBAEb,UAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,MAAK;gCAAM,cAAW;0CAAM;;;;;;;;;;;uBALhC;;;;;oBAYP,UAAU,GAAG,CAAC,CAAC,MAAM;wBACpB,MAAM,SAAS,MAAM,SAAS,UAAU,UAAU;wBAClD,IAAI,CAAC,UAAU,OAAO,MAAM,GAAG,GAAG,OAAO;wBAEzC,qBACE,6LAAC,qLAAA,CAAA,iBAAc;4BAEb,UAAU;gCACR,KAAK,MAAM,CAAC,EAAE;gCACd,KAAK,MAAM,CAAC,EAAE;4BAChB;4BACA,OAAO,MAAM,cAAc;sCAE3B,cAAA,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,GAAG,EAAE;;kDAExD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,KAAK,KAAK,MAAM,CAAC,EAAE;4CACnB,KAAK,KAAK,KAAK;;;;;;;;;;;kDAGnB,6LAAC;wCAAI,WAAU;kDAAgB,gBAAgB;;;;;;kDAC/C,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAI,WAAU;;4CAAe;4CACzB,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;;;;;;;;;;;;2BAzBZ;;;;;oBA6BX;;eArEK;;;;;YAuEN,4BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YACE;gBACJ;gBACA,SAAS;0BACV;;;;;;;;;;;;AAMT;GA7RM;;QASqB,4JAAA,CAAA,cAAW;QAkBrB,qIAAA,CAAA,YAAS;QAEP,4JAAA,CAAA,cAAW;;;KA7BxB;uCA+RS", "debugId": null}}]}