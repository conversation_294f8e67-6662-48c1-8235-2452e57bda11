"use client";

import React, {useEffect, useState} from "react";
import Image from "next/image";
// import {createInitialsAvatar} from "../../components/common/InitialAvatar/CreateInitialAvatar";
import {getUserDetails} from "@/app/service/userManagment";
import ListingTab from "./ListingTab";
import TransactionTab from "./TransactionTab";
import { useSearchParams } from "next/navigation";
import { createInitialsAvatar } from "../components/common/InitialAvatar/CreateInitialAvatar";

const books = Array(10).fill({
  title: "Can't Hurt Me",
  type: "Book",
  location: "123, Hope Road, Kingston 6, St. Andrew, Jamaica",
  cover: "/book-cover.jpg",
});

export default function UserDetail() {
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  console.log("User ID from URL:", id);
  const [activeTab, setActiveTab] = useState("listing");
  const [userDetails, setUserDetails] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  const fetchUserDetails = async (id) => {
    setIsLoading(true);
    try {
      const response = await getUserDetails(id);
      if (response && response.data) {
        setUserDetails(response.data);
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  // const avatarUrl = createInitialsAvatar("Warish Ahmad");

  useEffect(() => {
    fetchUserDetails(id);
  }, [id]);

  // All consoles
  console.log("User ID:", id);
  console.log("User Details:", userDetails);

  const planListings = userDetails?.user?.planDoc?.listings || [];
  const totalAllowed = planListings.reduce(
    (sum, l) => sum + (l.noOfListing || 0),
    0
  );
  const remainingListings =
    userDetails?.user?.subscriptionDoc?.remainingListings || [];
  const totalRemaining = remainingListings.reduce(
    (sum, l) => sum + (l.noOfListing || 0),
    0
  );
  const totalUsed = totalAllowed - totalRemaining;
  const percentUsed = totalAllowed > 0 ? (totalUsed / totalAllowed) * 100 : 0;

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Left Panel */}
        <div className="w-full md:w-1/3 flex flex-col gap-6">
          {/* Customer Info Card */}
          <div className="bg-white rounded-2xl shadow p-6">
            <div className="flex flex-col items-center text-center">
              <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-100">
                {userDetails?.user?.profilePicture ? (
                  <Image
                    src={userDetails?.user?.profilePicture}
                    alt="Profile"
                    width={96}
                    height={96}
                    className="object-cover"
                  />
                ) : (
                  <Image
                    src={createInitialsAvatar(
                      `${userDetails?.user?.firstName} ${userDetails?.user?.lastName}`
                    )}
                    alt="Profile"
                    width={96}
                    height={96}
                    className="object-cover"
                  />
                )}
              </div>
              <h2 className="mt-4 text-xl font-semibold">
                {`${userDetails?.user?.firstName} ${userDetails?.user?.lastName}`}
              </h2>
              <p className="text-gray-500">{userDetails?.user?.email}</p>
              <span className="mt-2 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                Seller | Listed Items:{" "}
                {userDetails?.user?.itemlistsDoc.length || 0}
              </span>
            </div>
          </div>

          {/* Plan Detail Card */}
          <div className="bg-white rounded-2xl shadow p-6">
            <h3 className="text-lg font-semibold mb-4">Plan Detail</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm text-gray-500">Current Plan Summary</h4>
              <div className="mt-4 grid grid-cols-3 gap-2 text-left">
                <div>
                  <p className="text-xs text-gray-500">Plan Name</p>
                  <p className="font-medium">
                    {userDetails?.user?.planDoc?.planName}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Billing Cycle</p>
                  <p className="font-medium">
                    {userDetails?.user?.planDoc?.planMonths}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Plan Cost</p>
                  <p className="font-medium">
                    {userDetails?.user?.planDoc?.price}
                  </p>
                </div>
              </div>

              <div className="mt-6">
                <p className="text-xs text-gray-500 mb-1">Items Uploaded</p>
                <div className="flex justify-between text-xs text-gray-500">
                  <span className="font-medium">
                    {totalUsed} out of {totalAllowed}
                  </span>
                  <span>
                    Expire on{" "}
                    <span className="font-medium">
                      {new Date(
                        userDetails?.user?.subscriptionDoc?.endDate
                      ).toLocaleDateString()}
                    </span>
                  </span>
                </div>
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden mt-1">
                  <div
                    className="h-full bg-gradient-to-r global_linear_gradient transition-all"
                    style={{width: `${percentUsed}%`}}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel */}
        <div className="w-full md:w-2/3 bg-white rounded-2xl shadow p-6 flex flex-col">
          {/* Tabs */}
          <div className="flex border-b border-gray-300 mb-4">
            <button
              onClick={() => setActiveTab("listing")}
              className={`py-2 px-4 -mb-px font-medium ${
                activeTab === "listing"
                  ? "border-b-2 border-gray-600 text-gray-800"
                  : "text-gray-500"
              }`}
            >
              Listing
            </button>
            <button
              onClick={() => setActiveTab("transaction")}
              className={`py-2 px-4 -mb-px font-medium ${
                activeTab === "transaction"
                  ? "border-b-2 border-gray-600 text-gray-800"
                  : "text-gray-500"
              }`}
            >
              Transaction
            </button>
          </div>

          {/* Content */}
          {activeTab === "listing" && <ListingTab userDetails={userDetails} />}
          {activeTab === "transaction" && (
            <TransactionTab userDetails={userDetails} />
          )}
        </div>
      </div>
    </div>

    // <div>this is the page for the user id : {id}</div>
  );
}