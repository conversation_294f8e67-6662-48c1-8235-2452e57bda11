"use client";
import React from "react";

const TransactionTab = ({userDetails}) => {
  const payments = userDetails?.user?.paymentsDoc || [];

  return (
    // <div className="overflow-x-auto bg-white p-4 rounded-lg shadow-sm">
    <table className="min-w-[600px] w-full text-left">
      <thead>
        <tr className="bg-gray-100">
          {[
            {label: "Invoice", key: "invoice"},
            {label: "Billing Date", key: "date"},
            {label: "Amount", key: "amount"},
            {label: "Plan", key: "plan"},
          ].map((col) => (
            <th
              key={col.key}
              className="px-4 py-3 text-sm font-bold text-gray-700"
            >
              <div className="inline-flex items-center space-x-1 select-none">
                <span>{col.label}</span>
                <span className="text-xs text-gray-500">↓</span>
              </div>
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200">
        {payments.length > 0 ? (
          payments.map((txn, idx) => (
            <tr key={txn?._id || idx}>
              <td className="px-4 py-3 text-sm text-gray-800">
                {/* If you have a real invoice number field, use it here instead of idx+1 */}
                {txn?.invoiceNumber
                  ? `Invoice ${txn?.invoiceNumber}`
                  : `Invoice #${idx + 1}`}
              </td>
              <td className="px-4 py-3 text-sm text-gray-800">
                {new Date(txn?.updatedAt).toLocaleDateString()}
              </td>
              <td className="px-4 py-3 text-sm text-gray-800">{txn?.amount}</td>
              <td className="px-4 py-3 text-sm text-gray-800">
                {txn?.planName}
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={4} className="py-6 text-center text-gray-400 italic">
              No transactions yet.
            </td>
          </tr>
        )}
      </tbody>
    </table>
    // </div>
  );
};

export default TransactionTab;
