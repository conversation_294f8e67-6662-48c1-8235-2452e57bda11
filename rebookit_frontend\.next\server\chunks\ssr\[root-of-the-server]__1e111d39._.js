module.exports = {

"[project]/app/profile/messages/messageComponent.module.scss.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "borderGradient": "messageComponent-module-scss-module__r7VfNq__borderGradient",
  "myMessagesContainer": "messageComponent-module-scss-module__r7VfNq__myMessagesContainer",
});
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[project]/app/profile/messages/predefinedChats.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"Still available? I’m interested!\":[\"Hey! Yes, it’s still available 😊\",\"Yes, still available!\"],\"Hey! Yes, it’s still available 😊\":[\"What's the edition and condition?\",\"Let’s fix a time!\"],\"It’s the 4th edition, no notes or marks. Very well kept.\":[\"That sounds perfect.\",\"I’d like to pick it up tomorrow.\"],\"Let’s confirm!\":[\"Done\",\"Confirmed, see you at 6 PM tomorrow!\"]}"));}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/app/socket.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "socket": (()=>socket)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-ssr] (ecmascript)");
"use client";
;
;
const socket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(("TURBOPACK compile-time value", "https://uatapi.rebookit.club"), {
    extraHeaders: {
        authorization: `Bearer ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])()}`
    }
});
socket.on("connect", ()=>{
    console.log("Connected to socket server:", socket.id);
});
socket.on("connect_error", (err)=>{
    console.log("connecction err", err);
    console.error("Socket connection error:", err.message);
});
}}),
"[project]/app/utils/axiosError.handler.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-ssr] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {
        // Clear local storage and redirect to /login
        localStorage.clear();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
        window.location.href = "/login";
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        // console.log("error log is", error)
        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(er.message)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])()) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[project]/app/services/axios.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/node/axios.cjs [app-ssr] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-ssr] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://uatapi.rebookit.club");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
}}),
"[project]/app/services/profile.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addReviewForSeller": (()=>addReviewForSeller),
    "bookMarkItem": (()=>bookMarkItem),
    "boostItem": (()=>boostItem),
    "deleteChatById": (()=>deleteChatById),
    "deleteMyBooks": (()=>deleteMyBooks),
    "delete_bookMarkItem": (()=>delete_bookMarkItem),
    "editItem": (()=>editItem),
    "getAdPlanById": (()=>getAdPlanById),
    "getAdPlans": (()=>getAdPlans),
    "getAllChat": (()=>getAllChat),
    "getBooksById": (()=>getBooksById),
    "getCategories": (()=>getCategories),
    "getChatById": (()=>getChatById),
    "getFaq": (()=>getFaq),
    "getItemBySeller": (()=>getItemBySeller),
    "getMyBooks": (()=>getMyBooks),
    "getPaymentIntent": (()=>getPaymentIntent),
    "getReviewsOfUser": (()=>getReviewsOfUser),
    "getSubCategories": (()=>getSubCategories),
    "getSubSubCategories": (()=>getSubSubCategories),
    "getSubSubSubCategories": (()=>getSubSubSubCategories),
    "getTestimonials": (()=>getTestimonials),
    "get_bookMarkItems": (()=>get_bookMarkItems),
    "getsubscriptionPlans": (()=>getsubscriptionPlans),
    "listItem": (()=>listItem),
    "paymentHistory": (()=>paymentHistory),
    "searchByName": (()=>searchByName),
    "searchISBN": (()=>searchISBN),
    "searchItemByName": (()=>searchItemByName),
    "supportRequest": (()=>supportRequest),
    "uploadPhotoSingle": (()=>uploadPhotoSingle),
    "verifyUserData": (()=>verifyUserData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/axiosError.handler.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/axios.js [app-ssr] (ecmascript)");
;
;
;
const uri = {
    login: "/user/login",
    userInfo: "/user",
    editProfile: "/user/edit-profile",
    item_by_name: `item/search`,
    subscriptionPlan: "/admin/subscription/plan",
    fetch_category: "/master/category",
    fetchSubCategory: "master/sub-category",
    fetchSubSubCategory: "master/Sub-Sub-category",
    fetchSubSubSubCategory: "master/sub-sub-sub-category",
    getPaymentIntent: "/payment/payment-intent",
    verifyUserData: "user/verify-otp",
    searchISBN: "/books/isbn/{{ISBN}}",
    searchByName: "/books/search?q={search}",
    bookMarkItem_id: "/item/bookmark",
    get_bookMark_by_user: "/item/bookmarks",
    getItems: "/item/search/current-user",
    getItemById: "/item",
    createItem: "/item",
    editItem: "/item",
    deleteItemById: "/item",
    itemBySeller: "/item/user",
    addReview: "/item/addReview",
    userReviews: "/item/{:id}/reviews",
    uploadPhoto: "admin/single-upload",
    history: "/payment/history",
    supportRequest: "/user/support-request",
    boostItem: "/item/boost",
    getTestimonials: "/user/testimonials",
    getFaq: "/faqs/filter-search",
    // ad-management
    getAdPlans: "ad-management/getAdPlans",
    getAdPlanById: "ad-management/getAdPlanById"
};
const chat = {
    chat: "/chat/all",
    chatById: "/chat"
};
const listItem = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.createItem}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const getAdPlans = async ({ type, position, page = 1, limit = 10 } = {})=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlans}`, {
        params: {
            ...type && {
                type
            },
            ...position && {
                position
            },
            page,
            limit
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getAdPlanById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlanById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const editItem = async (payload, id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`${uri.editItem}/${id}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const addReviewForSeller = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.addReview}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getMyBooks = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.getItems}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const deleteMyBooks = async (id, data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`${uri.deleteItemById}/${id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getBooksById = async (id, userId)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.getItemById}/${id}`, {
        params: {
            userId
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getItemBySeller = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.itemBySeller}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const searchItemByName = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.item_by_name}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getsubscriptionPlans = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.subscriptionPlan}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetch_category}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getAllChat = async (payloadData)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${chat.chat}`, payloadData).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const deleteChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const bookMarkItem = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.bookMarkItem_id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const getReviewsOfUser = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.userReviews}`.replace("{:id}", id));
    // .catch(axiosErrorHandler);
    console.log("getSubCategories response", response);
    return response;
};
const delete_bookMarkItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`${uri.bookMarkItem_id}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const get_bookMarkItems = async ()=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.get_bookMark_by_user}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getPaymentIntent = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.getPaymentIntent}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const verifyUserData = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.verifyUserData}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchISBN = async (ISBN)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchISBN}`.replace("{{ISBN}}", ISBN)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchByName = async (search)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchByName}`.replace("{search}", search)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const uploadPhotoSingle = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.uploadPhoto}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const paymentHistory = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.history}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const supportRequest = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.supportRequest}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const boostItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`${uri.boostItem}/` + id).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getTestimonials = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.getTestimonials}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getFaq = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.getFaq}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
} // let data = await fetch(USER_ROUTES.SEARCH_ITEM_BY_NAME.replace("itemName", text), {
 //                 headers: {
 //                     "Content-Type": "application/json",
 //                     "Authorization": `Bearer ${userToken}`
 //                 },
 //             },)
 //             let response = await data.json()
 //             // console.log("data getAllBookOfUser", await data.json())
 //             if (response.data) {
 //                 setBookData(response.data)
 //             }
 // export const login = async (payload, guestId) => {
 //     let response = await instance
 //         .post(`${uri.login}`,payload)
 //         .catch(axiosErrorHandler);
 //         console.log("login test response",response)
 //         return response
 // };
;
}}),
"[project]/app/services/auth.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createNewPassword": (()=>createNewPassword),
    "forgotPassword": (()=>forgotPassword),
    "login": (()=>login),
    "registerUser": (()=>registerUser),
    "resetPassword": (()=>resetPassword),
    "sendOTP": (()=>sendOTP),
    "update_userInfo_api": (()=>update_userInfo_api),
    "userInfo_api": (()=>userInfo_api),
    "verifyForgetPassword": (()=>verifyForgetPassword),
    "verifyOtp": (()=>verifyOtp)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/axiosError.handler.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/axios.js [app-ssr] (ecmascript)");
;
;
;
;
const uri = {
    login: "/user/login",
    userInfo: "/user",
    editProfile: "/user/edit-profile",
    verifyOtp: "user/verify-otp",
    registerUser: "/user/register",
    forgetPassword: "user/forgot-password",
    createNewPassword: "user/create-new-password",
    sendOtp: "user/send-otp",
    verifyForgetPassword: "/user/verify-forgot-otp",
    resetPassword: "/user/reset-password"
};
const login = async (payload, guestId)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.login}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const userInfo_api = async ()=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])()) {
        try {
            let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${uri.userInfo}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
            console.log("login test response", response);
            return response;
        } catch (err) {
            console.log("err in userInfo_api", err);
        }
    }
};
const update_userInfo_api = async (userPayload)=>{
    try {
        let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`${uri.editProfile}`, userPayload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
        console.log("login test response", response);
        return response;
    } catch (err) {
        console.log("err in userInfo_api", err);
    }
};
const verifyOtp = async (payload)=>{
    try {
        let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.verifyOtp}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
        console.log("verifyOtp test response", response);
        return response;
    } catch (err) {
        console.log("err in userInfo_api", err);
    }
};
const registerUser = async (payload)=>{
    try {
        let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.registerUser}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
        console.log("registerUser test response", response);
        return response;
    } catch (err) {
        console.log("err in userInfo_api", err);
    }
};
const forgotPassword = async (payload)=>{
    try {
        let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.forgetPassword}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
        console.log("registerUser test response", response);
        return response;
    } catch (err) {
        console.log("err in userInfo_api", err);
    }
};
const createNewPassword = async (payload)=>{
    try {
        let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.forgetPassword}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
        console.log("registerUser test response", response);
        return response;
    } catch (err) {
        console.log("err in userInfo_api", err);
    }
};
const sendOTP = async (payload)=>{
    try {
        let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.sendOtp}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
        console.log("sendOTP test response", response);
        return response;
    } catch (err) {
        console.log("err in userInfo_api", err);
    }
};
const verifyForgetPassword = async (payload)=>{
    try {
        let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.verifyForgetPassword}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
        console.log("sendOTP test response", response);
        return response;
    } catch (err) {
        console.log("err in userInfo_api", err);
    }
};
const resetPassword = async (payload)=>{
    try {
        let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${uri.resetPassword}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
        console.log("resetPassword test response", response);
        return response;
    } catch (err) {
        console.log("err in userInfo_api", err);
    }
}; // fetch(USER_ROUTES.EDIT_USER_INFO, {
 //                 method: "put",
 //                 headers: {
 //                     "Authorization": `Bearer ${userToken}`,
 //                     "Content-Type": "application/json"
 //                 },
 //                 body: JSON.stringify(userPayload)
 //             }).then(async res => {
 //                 const response = await res.json();
 //                 if (!response.error) {
 //                     toast.success(response.message)
 //                     setBtnDisabled(true)
 //                 } else {
 //                     response.message?.map(x => toast.error(x))
 //                     toast.error(response.message || "No Info Found")
 //                 }
 //             })
}}),
"[project]/app/components/InitialAvatar/CreateInitialAvatar.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createInitialsAvatar": (()=>createInitialsAvatar)
});
function createInitialsAvatar(name, options = {}) {
    // Default options
    const { size = 100, bgColor = "#cccccc", textColor = "#000000", shape = "circle" } = options;
    // Get initials from name
    const getInitials = (name)=>{
        if (!name || typeof name !== "string") return "";
        const words = name.trim().split(/\s+/).filter((word)=>word.length > 0);
        if (words.length === 0) return "";
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        return `${words[0].charAt(0)}${words[words.length - 1].charAt(0)}`.toUpperCase();
    };
    const initials = getInitials(name);
    const fontSize = size * 0.4;
    // Create SVG based on shape
    let svgContent;
    if (shape === "circle") {
        const radius = size / 2;
        svgContent = `
      <circle cx="${radius}" cy="${radius}" r="${radius}" fill="${bgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
    } else {
        svgContent = `
      <rect width="100%" height="100%" fill="${bgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
    }
    // Create full SVG
    const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" 
         width="${size}" 
         height="${size}" 
         viewBox="0 0 ${size} ${size}">
      ${svgContent}
    </svg>
  `;
    // Convert to data URL
    return `data:image/svg+xml,${encodeURIComponent(svg)}`;
}
}}),
"[project]/app/profile/messages/page.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$profile$2f$messages$2f$messageComponent$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/profile/messages/messageComponent.module.scss.module.css [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loader$2d$spinner$2f$dist$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-loader-spinner/dist/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/hi/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/lu/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io5/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/bi/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$profile$2f$messages$2f$predefinedChats$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/app/profile/messages/predefinedChats.json (json)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/socket.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/profile.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$auth$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/auth.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/slices/storeSlice.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/constant.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/InitialAvatar/CreateInitialAvatar.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const chatSuggestions = {
    admin: [
        "Thank you for your interest! Would you like to buy it?",
        "Hi! How can I help you with this item?",
        "Yes, it’s still available. Would you like to buy it?",
        "Sorry, it’s no longer available.",
        "Yes, What you like to offer!"
    ],
    client: [
        "Hi, I’m interested!",
        "Is this item still available?",
        "When would it be available for pick-up/delivery?",
        "Do you offer delivery?",
        "Are there any defect or issues I should know about?",
        "Is the price negotiable?"
    ]
};
function myMessages() {
    let userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
    console.log("userData", userData);
    const itemId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])((x)=>x?.storeData?.itemId);
    const storeData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSelector"])((x)=>x?.storeData);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const menuRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const chatRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const messagesEndRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [openChat, setOpenChat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false); // For Mobile purpose
    const [openFilter, setOpenFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [chatToggle, setChatToggle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("chats"); // chats , make offer
    const [openChatTemplate, setOpenChatTemplate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [predefinedChatsList, setPredefinedChatsList] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([
        "Hey! Yes, it’s still available 😊",
        "No, it's not available"
    ]);
    const [chatList, setChatList] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [chatId, setChatId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [chatCount, setChatCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [selectedChat, setSelectedChat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        itemId: null,
        userId: userData?._id,
        userName: "",
        backCoverImage: "",
        profileImage: "",
        itemPrice: null,
        isSeller: null,
        status: ""
    });
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [sender, setSender] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [searchText, setSearchText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [filter, setFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("all"); // all, buyers, sellers
    const [chatListLoading, setChatListLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [makeOfferValue, setMakeOfferValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [sellerMakeOfferShownComponent, setSellerMakeOfferShownComponent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("offer_pitch"); //  seller -> offer_pitch, rejected_offer, accepted_offer
    const [buyerMakeOfferShownComponent, setBuyerMakeOfferShownComponent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("send_offer"); // buyer -> send_offer, edit_offer, counter_offer, rejected_counter_offer, offer_accepted,
    const [itemOfConversation, setitemOfConversation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const debouncedFetchRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const [isImageLoaded, setisImageLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [currentPosition, setCurrentPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [showOptionThreeDot, setshowOptionThreeDot] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const menuBtnRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDispatch"])();
    const queryParams = new URLSearchParams(window.location.search);
    let urlItemId = queryParams.get("itemId");
    console.log("urlItemId", urlItemId);
    // const [showChatBodyCondition,setshowChatBodyCondition]=useState(false)
    console.log("itemOfConversation", itemOfConversation);
    const showMessageCondition = chatId || itemId || chatList?.length > 0 || searchText?.length;
    let showChatBodyCondition = chatId || itemId;
    console.log("showMessageCondition", showMessageCondition);
    console.log("itemId", itemId);
    console.log("storeData", storeData);
    console.log("chatListLoading", chatListLoading);
    console.log("chatList", chatList);
    console.log("showOptionThreeDot", showOptionThreeDot);
    // Scroll Behavior
    console.log("sender", sender);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (showMessageCondition) {
            // Scroll to the bottom when messages update
            chatRef.current?.scrollIntoView({
                behavior: "smooth"
            });
        }
        const container = messagesEndRef.current;
        if (container) {
            container.scrollTo({
                top: container.scrollHeight,
                behavior: "smooth"
            });
        }
    }, [
        messages
    ]);
    // function to open chat in mobile and update the chat id
    const openChatHandler = (chatId)=>{
        setChatId(chatId);
        fetchChatById(chatId);
        setOpenChat(true);
    };
    const closeChatHandler = ()=>{
        setOpenChat(false);
    };
    // add the predefined list from the chat
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (messages.length < 1) return;
        // console.log("PredefinedChats", PredefinedChats)
        const char = [
            ...messages
        ].reverse().find((m)=>{
            if (m?.sender !== sender?._id) return m;
        });
        // console.log("char", char)
        const list = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$profile$2f$messages$2f$predefinedChats$2e$json__$28$json$29$__["default"][char?.text];
        if (list?.length > 0) {
            setPredefinedChatsList(list);
        }
    }, [
        messages
    ]);
    // fetch user profile
    const fetchCurrentLocation = ()=>{
        let position = navigator.geolocation.getCurrentPosition((position)=>{
            console.log("position", position);
            setCurrentPosition({
                lat: position.coords.latitude,
                long: position.coords.longitude
            });
        });
    };
    console.log(currentPosition, "currentPosition");
    const fetchProfile = async ()=>{
        try {
            let userToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])();
            let userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
            let responseUserInfo = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$auth$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userInfo_api"])();
            setSender(userData);
        // fetch(USER_ROUTES.USER_INFO, {
        //     method: "get",
        //     headers: {
        //         "Authorization": `Bearer ${userToken}`
        //     }
        // }).then(async res => {
        //     const response = await res.json();
        //     console.log("response is", response)
        //     if (!response.error) {
        //         let userData = response.data;
        //         setSender(userData)
        //     } else {
        //         toast.error(response.message || "No Info Found")
        //     }
        // })
        } catch (error) {
            console.log(error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(error || "Internal Server Error");
        }
    };
    // fetch item data
    const fetchItemData = async ()=>{
        try {
            let userToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])();
            let searchData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getBooksById"])(itemId);
            console.log("searchData fetchItemData", searchData);
            if (searchData.status == 200) {
                setitemOfConversation(searchData.data);
                setSelectedChat((prev)=>({
                        ...prev,
                        userId: searchData?.data?.createdBy?._id,
                        itemId: searchData?.data?._id,
                        backCoverImage: searchData?.data?.images.length && searchData?.data?.images[0],
                        profileImage: searchData?.data?.createdBy?.profileImage,
                        userName: `${searchData?.data?.createdBy?.firstName} ${searchData?.data?.createdBy?.lastName}`,
                        isSeller: userData._id == searchData.data?.createdBy?._id,
                        status: searchData?.data.status
                    }));
                // setChatToggle(userData._id == searchData.data?.createdBy?._id?"chats":"make offer")
                setSelectedChat((prev)=>{
                    return {
                        ...prev,
                        itemPrice: searchData.data.price
                    };
                });
                setMakeOfferValue(`${searchData.data.price}`);
            // setSelectedChat(searchData.data?.coverImage)
            }
        } catch (error) {
            console.log("error", error);
        }
    };
    // fetch chat list in the left side [ search and filters ]
    const fetchChatList = async (searchTerm, filterText)=>{
        try {
            setChatListLoading(true);
            let userToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])();
            const payloadData = {};
            if (searchTerm) payloadData["searchTerm"] = searchTerm;
            if (filterText) payloadData["chatType"] = filterText;
            let allChat = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllChat"])(payloadData);
            console.log("allChat", allChat);
            if (allChat.status == 200) {
                let chatIdToOpen = "";
                let nonDeletedData = allChat?.data?.filter((item)=>{
                    if (itemId === item.sellerItemId) {
                        chatIdToOpen = item._id;
                    }
                    return !item.deletedBy.includes(userData._id);
                });
                console.log("chatIdToOpen", chatIdToOpen);
                if (chatIdToOpen) {
                    fetchChatById(chatIdToOpen);
                }
                setChatList(nonDeletedData);
                if (itemId) {
                    for(let index = 0; index < nonDeletedData.length; index++){
                        if (storeData.chatId == nonDeletedData[index]._id) {
                        // fetchChatById(nonDeletedData[index]._id)
                        }
                    }
                }
                setLoading(false);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(allChat?.message || "Internal Server Error");
                setLoading(false);
            }
            setChatListLoading(false);
        // fetch(CHAT_ROUTES.ALL_CHATS, {
        //     method: "POST",
        //     headers: {
        //         "Content-Type": "application/json",
        //         "Authorization": `Bearer ${userToken}`
        //     },
        //     body: JSON.stringify(payloadData)
        // }).then(async res => {
        //     const response = await res.json();
        //     let chatData = response
        //     if (!chatData?.error) {
        //         console.log("chat list Data", chatData)
        //         setChatList(chatData?.data)
        //         setLoading(false)
        //         setChatListLoading(false)
        //     } else {
        //         toast.error(response?.message || "Internal Server Error")
        //         setLoading(false)
        //         setChatListLoading(false)
        //     }
        // })
        } catch (error) {
            console.log("error", error);
            setLoading(false);
            setChatListLoading(false);
        }
    };
    console.log("compare", selectedChat?.userId, "userId", userData?._id);
    // fetch the selected chat data like conversation, participants and messages data
    const fetchChatById = async (chatId)=>{
        try {
            // getAllChat()
            let userToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])();
            let chatData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getChatById"])(chatId);
            console.log("chatData getChatById", chatData);
            if (chatData.status == 200) {
                setitemOfConversation(chatData.data.conversation[0].item[0]);
                setChatCount(chatData?.data?.count);
                const conversation = chatData?.data?.conversation?.[0];
                const participant1 = conversation?.participants_docs?.[0];
                const participant2 = conversation?.participants_docs?.[1];
                console.log("participant1", participant1);
                console.log("participant2", participant2);
                const receiver = userData?._id == participant1._id ? participant2 : participant1;
                console.log("receiver fetch", receiver);
                setSelectedChat((prev)=>({
                        ...prev,
                        conversationId: conversation?._id,
                        itemId: conversation?.sellerItemId,
                        userId: receiver?._id,
                        userName: receiver?.firstName + " " + receiver?.lastName,
                        profileImage: receiver?.profileImage,
                        backCoverImage: conversation?.item?.[0]?.images.length && conversation?.item?.[0]?.images[0],
                        itemPrice: conversation?.item?.[0]?.price,
                        isSeller: conversation?.item?.[0]?.createdBy === userData._id,
                        status: conversation?.item?.[0]?.status
                    }));
                // setChatToggle(conversation?.item?.[0]?.createdBy === userData._id?"chats":"make offer")
                // messages
                setMessages(chatData?.data?.messages);
            }
        } catch (error) {
            console.log("error", error);
        }
    };
    // update user profile of chat types consistency
    const updateProfile = async (chatType)=>{
        try {
            const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getToken"])();
            const payload = {};
            if (chatType === "sellers") payload["chatType"] = chatType;
            else return;
        //do not remove the comment
        // await update_userInfo_api({ selectedChatFilters: payload });
        } catch (error) {
            console.log("error", error);
        }
    };
    // fetching data's
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // fetch item data only when the user came from the search path and there is no chat with that user
        if (itemId) {
            fetchItemData();
            setOpenChat(true);
        }
        fetchCurrentLocation();
        fetchChatList();
        fetchProfile();
        const handleClickOutside = (event)=>{
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setshowOptionThreeDot(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    // Initialize debounced function once
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        debouncedFetchRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Debounce"])(fetchChatList, 500);
        const openCloseMenu = (e)=>{
            if (menuBtnRef.current && !menuBtnRef.current.contains(e.target)) {
                setOpenFilter(false);
            }
        };
        document.addEventListener("click", openCloseMenu);
        return ()=>{
            debouncedFetchRef.current?.cancel();
            document.removeEventListener("click", openCloseMenu);
        // dispatch(nullItemId())
        };
    }, []);
    // filter or search chat list
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (debouncedFetchRef.current) {
            debouncedFetchRef.current(searchText, filter);
        }
    }, [
        searchText,
        filter
    ]);
    // socket connection check
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        console.log("socket", __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"]);
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].on("connect", ()=>{
            console.log("Connected to socket");
            console.log("socket inside", __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"]);
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].on("disconnect", ()=>{
            console.log("disconnected to socket");
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].on("connect_error", (err)=>{
            console.log("Connect error:", err.message); // Example: "xhr poll error", "WebSocket is closed before the connection is established"
        });
    }, []);
    // socket message receive
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleMessage = (data)=>{
            console.log("data on message", data);
            // appending the message in the message array
            setMessages((prev)=>[
                    ...prev,
                    data.message
                ]);
            let nonDeletedData = data?.chats?.filter((item)=>{
                return !item.deletedBy.includes(userData._id);
            });
            setChatList(nonDeletedData);
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].on("on-message", handleMessage);
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].on("connect_error", (err)=>{
            console.error("Socket connection error:", err.message);
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].on("connect", ()=>{
            console.log("Connected to socket server:", __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].id);
        });
        // Cleanup the listener on unmount or rerun
        return ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].off("on-message", handleMessage);
        };
    }, []);
    const avatarUrl = (userName)=>{
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createInitialsAvatar"])(`${userName}`, {
            bgColor: "#3f51b5",
            textColor: "#ffffff"
        });
    };
    console.log("itemId", itemId);
    console.log("selectedChat", selectedChat);
    console.log("sender", sender);
    console.log("messages", messages);
    // socket event to send message
    const sendMessage = (message, mediaType, mediaUrl)=>{
        if (!message || message.trim().length === 0) return;
        let data = {
            recipientId: selectedChat.userId || itemOfConversation.createdBy._id,
            sellerItemId: selectedChat?.itemId || itemId,
            message: {
                text: message.trim()
            }
        };
        if (mediaUrl) {
            data.message[mediaType] = mediaType;
            data.message[mediaUrl] = mediaUrl;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].emit("send-message", data);
    };
    const imageSend = (mediaUrl)=>{
        let data = {
            recipientId: selectedChat.userId || itemOfConversation.createdBy._id,
            sellerItemId: selectedChat?.itemId || itemId,
            message: {
                mediaType: "image",
                mediaUrl: mediaUrl,
                isMedia: true
            }
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].emit("send-message", data);
    };
    const locationSend = (mediaUrl)=>{
        console.log("inside locationSend");
        navigator.permissions?.query({
            name: "geolocation"
        }).then(function(result) {
            console.log("Permission status:", result.state);
        });
        let position = navigator.geolocation.getCurrentPosition((position)=>{
            console.log("position", position);
            if (!position.coords) {
                return;
            }
            setCurrentPosition({
                lat: position.coords.latitude,
                long: position.coords.longitude
            });
            let data = {
                recipientId: selectedChat.userId || itemOfConversation.createdBy._id,
                sellerItemId: selectedChat?.itemId || itemId,
                message: {
                    mediaType: "location",
                    // "mediaUrl": mediaUrl,
                    // "isMedia": true,
                    text: JSON.stringify({
                        lat: position.coords.latitude,
                        long: position.coords.longitude
                    })
                }
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$socket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socket"].emit("send-message", data);
        });
    };
    // sending message
    const sendMessageHandler = (e)=>{
        e.preventDefault();
        // Send message
        sendMessage(e.target.message.value);
        // clear the message
        e.target.message.value = "";
    };
    const filterHandler = (filterText)=>{
        setFilter(filterText);
        updateProfile(filterText);
        setOpenFilter(false);
        setChatId(null);
        setOpenChat(false); // close chat for mobile
    };
    // chat template open handler
    const chatTemplateOpenHandler = ()=>{
        setOpenChatTemplate(!openChatTemplate);
        const container = messagesEndRef.current;
    // if (container) {
    //   container.scrollTo({
    //     top: container.scrollHeight,
    //     behavior: "smooth",
    //   });
    // }
    };
    let prefix = "J$ "; // default prefix
    // handling the default prefix will not get send, without price cannot send something
    const makeOfferInputHandler = (e)=>{
        let inputValue = e.target.value;
        // Remove prefix if user tries to type it again
        if (inputValue.startsWith(prefix)) {
            inputValue = inputValue.slice(prefix.length);
        } else {
            // If user tries to delete the prefix entirely, ignore that change
            inputValue = inputValue.replace(/[^0-9]/g, "");
        }
        // Allow only digits
        const numeric = inputValue.replace(/[^0-9]/g, "");
        setMakeOfferValue(inputValue);
    };
    console.log("makeOfferValue", makeOfferValue);
    // Send Offer price by buyer
    const sendMakeOfferHandler = (e)=>{
        e.preventDefault();
        let sliceData;
        if (makeOfferValue.includes(prefix)) {
            sliceData = makeOfferValue.slice(prefix.length);
        } else {
            sliceData = makeOfferValue;
        }
        console.log(sliceData, "sliceData");
        // if (sliceData < selectedChat.itemPrice) {
        //     toast.error("Can Not Quote Less Then Price")
        //     return
        // }
        if (makeOfferValue === prefix) return;
        let sendOfferText = "J$" + makeOfferValue;
        sendMessage(sendOfferText);
    // setMakeOfferValue("J$ ")
    };
    // handling the make offer component shown to the buyer or seller
    // useEffect(() => {
    //     if (selectedChat?.isSeller) {
    //         // last message of price
    //         const priceRegex = new RegExp(`^${prefix}/[^0-9]/g`);
    //         const lastPriceMessageByBuyer = messages?.toReversed().find(m => {
    //             const text = m.text || ""; // assuming messages have a `text` field
    //             console.log("text", text)
    //             const isPrice = /^J\$ \d+(\.\d{1,2})?$/.test(text.trim())
    //             console.log("isPrice", isPrice)
    //             if (isPrice) return m;
    //         });
    //         console.log("lastPriceMessageByBuyer", lastPriceMessageByBuyer)
    //         // To extract the actual price if found
    //         const price = lastPriceMessageByBuyer
    //             ? parseFloat(lastPriceMessageByBuyer.text.trim().match(priceRegex)[1])
    //             : null;
    //         console.log("price", price)
    //     }
    // }, [messages])
    const handshakeSvg = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "28",
        height: "15",
        viewBox: "0 0 34 20",
        fill: "none",
        className: "md:w-[34px] md:h-5",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M22.6324 0H18.1601C17.7436 0 17.3427 0.156193 17.0355 0.437341L11.9176 5.12314C11.9123 5.12835 11.9071 5.13876 11.9019 5.14397C11.0377 5.95617 11.0533 7.25258 11.7926 8.05958C12.4538 8.78327 13.8439 8.97591 14.7134 8.20015C14.7186 8.19494 14.729 8.19494 14.7342 8.18974L18.8942 4.37862C19.2326 4.07144 19.7637 4.09227 20.0708 4.43069C20.3832 4.7691 20.3572 5.29495 20.0188 5.60734L18.6599 6.85168L26.2405 13.0057C26.3915 13.1307 26.5268 13.266 26.6518 13.4066V3.33212L23.8091 0.489406C23.5019 0.177019 23.075 0 22.6324 0ZM28.3231 3.34254V14.9998C28.3231 15.9213 29.0676 16.6658 29.9891 16.6658H33.3212V3.34254H28.3231ZM30.8222 14.9998C30.364 14.9998 29.9891 14.6249 29.9891 14.1667C29.9891 13.7086 30.364 13.3337 30.8222 13.3337C31.2803 13.3337 31.6552 13.7086 31.6552 14.1667C31.6552 14.6249 31.2803 14.9998 30.8222 14.9998ZM0 16.6606H3.33212C4.25367 16.6606 4.99819 15.9161 4.99819 14.9946V3.34254H0V16.6606ZM2.49909 13.3337C2.95726 13.3337 3.33212 13.7086 3.33212 14.1667C3.33212 14.6249 2.95726 14.9998 2.49909 14.9998C2.04093 14.9998 1.66606 14.6249 1.66606 14.1667C1.66606 13.7034 2.04093 13.3337 2.49909 13.3337ZM25.194 14.3021L17.4208 7.99189L15.8588 9.42367C14.3125 10.8346 11.9436 10.6992 10.5639 9.19458C9.16334 7.66389 9.27268 5.29495 10.793 3.89963L15.0518 0H10.6888C10.2463 0 9.82456 0.177019 9.51217 0.489406L6.66425 3.33212V14.9894H7.61703L12.3289 19.2534C13.7554 20.4145 15.8536 20.1958 17.0147 18.7692L17.0251 18.7588L17.957 19.5658C18.7849 20.2427 20.0084 20.1125 20.68 19.2847L22.3148 17.275L22.596 17.5041C23.3093 18.082 24.361 17.9779 24.9389 17.2594L25.4335 16.6502C26.0166 15.9317 25.9073 14.8852 25.194 14.3021Z",
                fill: "url(#paint0_linear_2930_11490)"
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 637,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                    id: "paint0_linear_2930_11490",
                    x1: "0",
                    y1: "10",
                    x2: "33.3212",
                    y2: "10",
                    gradientUnits: "userSpaceOnUse",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            stopColor: "#E1020C"
                        }, void 0, false, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 650,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                            offset: "1",
                            stopColor: "#4D7906"
                        }, void 0, false, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 651,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 642,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 641,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/profile/messages/page.js",
        lineNumber: 629,
        columnNumber: 5
    }, this);
    const defaultSellerComponent = (title, price, para, btn1, btn2, rejected = false, rejectedPrice = "", showBtn = true, showIcon = false)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5",
            children: [
                rejected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-row gap-[5px] mb-[5px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-xs leading-normal uppercase",
                            children: [
                                "Buyer’s offer: J$",
                                rejectedPrice
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 671,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-xs leading-normal uppercase text-[#FF2929]",
                            children: "Rejected"
                        }, void 0, false, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 674,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 670,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs leading-normal uppercase",
                    children: title
                }, void 0, false, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 680,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-center items-center gap-2.5",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "my-[1px] text-[28px] capitalize font-bold leading-normal global_text_linear_gradient",
                            children: [
                                "J$ ",
                                price
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 683,
                            columnNumber: 9
                        }, this),
                        showIcon && handshakeSvg
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 682,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "capitalize text-xs leading-normal",
                    children: para
                }, void 0, false, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 689,
                    columnNumber: 7
                }, this),
                showBtn && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-full flex justify-between gap-2 mt-[5px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full",
                            children: btn1
                        }, void 0, false, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 693,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full",
                            children: btn2
                        }, void 0, false, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 697,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 692,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/profile/messages/page.js",
            lineNumber: 668,
            columnNumber: 5
        }, this);
    const offerInput = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
        onSubmit: sendMakeOfferHandler,
        className: "flex justify-between",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-[50%]",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full flex items-stretch my-2 ",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-[#C0DEFB] pl-2  text-[12px] rounded-l-md flex items-center text-sm font-medium",
                                children: "J$"
                            }, void 0, false, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 710,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "text",
                                autoComplete: "off",
                                className: "w-full  bg-[#C0DEFB] pr-2 py-2 text-xs font-medium leading-normal rounded-r-md focus:outline-none",
                                value: makeOfferValue,
                                placeholder: "Enter Price",
                                min: itemOfConversation.price,
                                onChange: makeOfferInputHandler
                            }, void 0, false, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 715,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 708,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full flex justify-between gap-2.5 items-center",
                        children: makeOfferValue < selectedChat.itemPrice ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: " w-full bg-[#f00711] py-[3px] px-[15px] rounded-sm",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs md:text-sm text-white font-medium leading-normal",
                                    children: "Too low to offer"
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 729,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-[10px] md:text-xs leading-normal text-[white]",
                                    children: "Less chance of seller’s reply."
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 732,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 728,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-full global_linear_gradient py-[3px] px-[15px] rounded-sm",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs md:text-sm text-white font-medium leading-normal",
                                    children: "Very good offer"
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 738,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-[10px] md:text-xs leading-normal text-[#D8D8D8]",
                                    children: "High chance of seller’s reply."
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 741,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 737,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 726,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 707,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col justify-end align-end",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    type: "submit",
                    className: "uppercase text-xs md:text-sm text-white py-2.5 px-[30px] global_linear_gradient rounded-full",
                    children: "SEND"
                }, void 0, false, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 749,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 748,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/profile/messages/page.js",
        lineNumber: 706,
        columnNumber: 5
    }, this);
    const makeOfferComponents = {
        buyer: {
            // Send Offer with input
            send_offer: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full py-2 px-5 md:px-7",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "overflow-x-auto flex gap-3.5",
                        children: new Array(4).fill(0).map((_, idx)=>{
                            const basePrice = selectedChat?.itemPrice ?? 0;
                            const price = Math.round(basePrice * Math.pow(1.2, idx));
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-sm text-white gradient-all-round-border border-2 lg:py-[6.5px] lg:px-[22px] lg:text-sm font-semibold cursor-pointer active:scale-95",
                                onClick: ()=>sendMessage(`J$ ${price}`),
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "global_text_linear_gradient",
                                    children: [
                                        "J$ ",
                                        price
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 775,
                                    columnNumber: 19
                                }, this)
                            }, idx, false, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 770,
                                columnNumber: 17
                            }, this);
                        })
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 764,
                        columnNumber: 11
                    }, this),
                    offerInput
                ]
            }, void 0, true, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 763,
                columnNumber: 9
            }, this),
            // Edit Offer
            edit_offer: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "pt-2 pb-1 px-5 flex flex-col justify-center items-center w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "uppercase text-xs leading-normal",
                        children: "YOUR OFFER"
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 788,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "py-[1px] text-[28px] capitalize font-bold global_text_linear_gradient",
                        children: "J$ 440"
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 789,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xs capitalize leading-normal",
                        children: "waiting for seller’s response."
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 792,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: "uppercase text-sm leading-normal py-2.5 md:py-[13px] px-5 global_linear_gradient rounded-full w-full text-white mt-2.5 tracking-[0.7px]",
                        children: "Edit offer"
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 795,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 787,
                columnNumber: 9
            }, this),
            // counter offer
            counter_offer: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xs leading-normal uppercase",
                        children: "Seller’s counter offer"
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 804,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "my-[1px] text-[28px] capitalize font-bold leading-normal global_text_linear_gradient",
                        children: "J$ 440"
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 807,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "capitalize text-xs leading-normal",
                        children: "Buyer waiting for your response"
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 810,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full flex justify-between gap-2 mt-[5px]",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full",
                                children: "Make new offer"
                            }, void 0, false, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 815,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full",
                                children: "Let’s go ahead"
                            }, void 0, false, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 819,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 814,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 803,
                columnNumber: 9
            }, this),
            // Rejected than make offer
            rejected_counter_offer: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "py-2 px-5 md:px-7 w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex gap-2 md:gap-3.5 w-full",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "global_linear_gradient py-[7px] px-2.5 md:px-4 text-center text-xs md:text-lg leading-normal font-semibold text-white rounded-sm w-[52%]",
                                children: [
                                    "J$ 435",
                                    " ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-[#FF2929] font-medium",
                                        children: "Rejected"
                                    }, void 0, false, {
                                        fileName: "[project]/app/profile/messages/page.js",
                                        lineNumber: 832,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 830,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "global_linear_gradient py-[7px] px-2.5 md:px-4 text-center text-xs md:text-lg leading-normal font-semibold text-white rounded-sm w-1/2",
                                children: "Seller’s Offer J$ 440"
                            }, void 0, false, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 835,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 829,
                        columnNumber: 11
                    }, this),
                    offerInput
                ]
            }, void 0, true, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 828,
                columnNumber: 9
            }, this),
            // offer accepted
            offer_accepted: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col items-center pt-2.5 pb-2 px-5 md:px-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xs leading-normal uppercase",
                        children: "offer accepted"
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 847,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex gap-2.5 items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-[22px] md:text-[28px] leading-normal capitalize font-bold global_text_linear_gradient",
                                children: "J$ 440"
                            }, void 0, false, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 849,
                                columnNumber: 13
                            }, this),
                            handshakeSvg
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 848,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-xs leading-normal capitalize",
                        children: "now you’re one step closer to thr final deal."
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 854,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: "global_linear_gradient mt-2.5 rounded-full border border-[#0161AB] w-full text-white py-[13px] px-5 uppercase text-sm tracking-[0.7px] leading-normal",
                        children: "let’s meet"
                    }, void 0, false, {
                        fileName: "[project]/app/profile/messages/page.js",
                        lineNumber: 858,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 846,
                columnNumber: 9
            }, this)
        },
        seller: {
            offer_pitch: // <div className='flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5'>
            defaultSellerComponent("Buyer’s Offer", 440, "Buyer waiting for your response", "Make new offer", "Let’s go ahead"),
            // </div>
            rejected_offer: defaultSellerComponent("Your Counter offer", 440, "waiting for buyer’s response", "", "", true, 440, false),
            accepted_offer: defaultSellerComponent("offer accepted", 440, "now you’re one step closer to thr final deal.", "Let’s meet", "Ask contact", false, "", true, true)
        }
    };
    console.log("showChatBodyCondition", showChatBodyCondition);
    async function deleteConversation(id) {
        let deleteResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteChatById"])(id);
        console.log("deleteResponse", deleteResponse);
        if (deleteResponse.status == 200) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Deleted Successfully");
            // showChatBodyCondition=false
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateItemId"])(""));
            setChatId(null);
            fetchChatList();
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(deleteResponse?.data?.message || "Something Went Wrong");
        }
    }
    const handleButtonClick = ()=>{
        fileInputRef.current?.click();
    };
    let str = "image/png";
    console.log(str.includes("image"), "includes");
    const handleFileChange = async (e)=>{
        console.log("event", e.target.files);
        const file = e.target.files[0];
        if (file) {
            let isImage = file.type.includes("image");
            if (!isImage) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error("Only Images Are Allowed");
                return;
            }
            // setLoading(true)
            const formData = new FormData();
            formData.append("file", file);
            try {
                const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["uploadPhotoSingle"])(formData);
                console.log("data uploadPhotoSingle", data);
                if (data.status == 200) {
                    imageSend(data.data.url);
                }
            } catch (err) {
                console.log("err in file chjange", err);
            }
        }
    };
    const ChatMessage = ({ lat, long })=>{
        if (typeof lat !== "number" || typeof long !== "number" || isNaN(lat) || isNaN(long)) {
            console.error("Invalid coordinates:", lat, long);
            return null;
        }
        console.log("ChatMessage", lat, long);
        const mapUrl = `https://www.google.com/maps?q=${lat},${long}&z=15&output=embed`;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full max-w-[150px] h-[150px] rounded overflow-hidden border shadow",
            onClick: ()=>window.open(mapUrl),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("iframe", {
                src: mapUrl,
                width: "100%",
                height: "100%",
                allowFullScreen: "",
                loading: "lazy"
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 965,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/profile/messages/page.js",
            lineNumber: 961,
            columnNumber: 7
        }, this);
    // For normal text messages
    };
    let mediaType = {
        image: "image",
        location: "location"
    };
    const RenderMessage = (message)=>{
        if (mediaType.image == message.mediaType) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                className: `w-[100px] h-[100px] rounded-md object-cover  transition-opacity duration-300 ${isImageLoaded ? "opacity-100" : "opacity-0"}`,
                src: message.mediaUrl,
                onLoad: ()=>setisImageLoaded(true)
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 982,
                columnNumber: 9
            }, this);
        } else if (mediaType.location == message.mediaType) {
            console.log();
            return ChatMessage(JSON.parse(message.text));
        } else {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "py-4 px-6 global_linear_gradient rounded-tl-[32px] rounded-tr-[32px] rounded-bl-[32px] text-xs leading-normal text-white w-fit hyphens-auto wrap-anywhere overflow-hidden",
                children: message?.text
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 995,
                columnNumber: 9
            }, this);
        }
    };
    let lastMessageText = (chat)=>{
        if (chat.lastMessage.mediaType == mediaType.location) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]",
                children: "Map"
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 1005,
                columnNumber: 9
            }, this);
        } else if (chat.lastMessage.mediaType == mediaType.image) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]",
                children: "Image"
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 1011,
                columnNumber: 9
            }, this);
        } else {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]",
                children: chat?.lastMessage?.text
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 1017,
                columnNumber: 9
            }, this);
        }
    };
    console.log("makeOfferValue", makeOfferValue);
    console.log("openChat", openChat);
    let elseImage = "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/03df84d0-59d5-4991-977a-f9ab67fae5eb.jpg";
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: " h-[500px] w-full flex justify-center items-center bg-white ",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$loader$2d$spinner$2f$dist$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Circles"], {
                height: "80",
                width: "80",
                color: "#4fa94d",
                ariaLabel: "circles-loading",
                wrapperStyle: {},
                wrapperClass: "",
                visible: true
            }, void 0, false, {
                fileName: "[project]/app/profile/messages/page.js",
                lineNumber: 1031,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/profile/messages/page.js",
            lineNumber: 1030,
            columnNumber: 7
        }, this);
    } else return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$profile$2f$messages$2f$messageComponent$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].myMessagesContainer} w-full`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: ` ${openChat ? "hidden" : "flex"} lg:flex justify-between items-center px-2.5`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg md:text-2xl font-semibold leading-normal",
                            children: "My Messages"
                        }, void 0, false, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 1052,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                            className: "flex justify-center items-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/search",
                                "aria-label": "View all book categories",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        xmlns: "http://www.w3.org/2000/svg",
                                        width: "91",
                                        height: "34",
                                        viewBox: "0 0 91 34",
                                        fill: "none",
                                        className: "lg:hidden",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M87.5156 17C87.5156 9.26817 80.448 3.00029 71.7296 3.00029L18.7857 3.00028C10.0673 3.00028 2.99966 9.26817 2.99966 17",
                                                stroke: "#211F54",
                                                strokeWidth: "5.49989"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1066,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M87.5156 17C87.5156 9.26817 80.473 3.00029 71.7854 3.00029L19.0285 3.00028",
                                                stroke: "#0161AB",
                                                strokeWidth: "5.49989"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1071,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M3 17C3 24.7318 10.0676 30.9997 18.786 30.9997H71.7299C80.4483 30.9997 87.516 24.7318 87.516 17",
                                                stroke: "#EFDC2A",
                                                strokeWidth: "5.49989"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1076,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M19.0293 30.9997H71.7861C80.4737 30.9997 87.5164 24.7318 87.5164 17",
                                                stroke: "#0161AB",
                                                strokeWidth: "5.49989"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1081,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M71.7305 3L18.7866 3",
                                                stroke: "#FF0009",
                                                strokeWidth: "5.49989"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1086,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M71.7305 31L18.7866 31",
                                                stroke: "#4A8B40",
                                                strokeWidth: "5.49989"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1091,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                                x: "5.85938",
                                                y: "3.91406",
                                                width: "78.5871",
                                                height: "25.714",
                                                rx: "12.857",
                                                fill: "white"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1096,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                                                x: "50%",
                                                y: "50%",
                                                dominantBaseline: "middle",
                                                textAnchor: "middle",
                                                fontSize: "12",
                                                fill: "#211F54",
                                                fontWeight: "500",
                                                fontFamily: "Poppins, sans-serif",
                                                children: "Explore"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1105,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/profile/messages/page.js",
                                        lineNumber: 1058,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        xmlns: "http://www.w3.org/2000/svg",
                                        width: "132",
                                        height: "54",
                                        viewBox: "0 0 132 54",
                                        fill: "none",
                                        className: "hidden lg:inline",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M127.527 26.9922C127.527 14.8465 117.281 5.00045 104.642 5.00045L27.8874 5.00044C15.2481 5.00044 5.00188 14.8465 5.00188 26.9922",
                                                stroke: "#211F54",
                                                strokeWidth: "8.63962"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1127,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M127.527 26.9922C127.527 14.8465 117.317 5.00045 104.723 5.00044L28.2395 5.00044",
                                                stroke: "#0161AB",
                                                strokeWidth: "8.63962"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1132,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M5.00391 26.9922C5.00391 39.1379 15.2501 48.9839 27.8894 48.9839H104.644C117.283 48.9839 127.529 39.1379 127.529 26.9922",
                                                stroke: "#EFDC2A",
                                                strokeWidth: "8.63962"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1137,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M28.2422 48.9839H104.725C117.32 48.9839 127.53 39.1379 127.53 26.9922",
                                                stroke: "#0161AB",
                                                strokeWidth: "8.63962"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1142,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M104.645 5L27.8901 4.99999",
                                                stroke: "#FF0009",
                                                strokeWidth: "8.63962"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1147,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M104.645 48.9844L27.8901 48.9844",
                                                stroke: "#4A8B40",
                                                strokeWidth: "8.63962"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1152,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                                x: "9.14844",
                                                y: "6.43359",
                                                width: "113.93",
                                                height: "40.3934",
                                                rx: "20.1967",
                                                fill: "white"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1157,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                                                x: "50%",
                                                y: "50%",
                                                dominantBaseline: "middle",
                                                textAnchor: "middle",
                                                fontSize: "14",
                                                fill: "#211F54",
                                                fontWeight: "500",
                                                fontFamily: "Poppins, sans-serif",
                                                children: "Explore"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1166,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/profile/messages/page.js",
                                        lineNumber: 1119,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/profile/messages/page.js",
                                lineNumber: 1057,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 1056,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 1047,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `mt-[5px] lg:mt-[30px] flex ${showMessageCondition ? "" : "flex-col"}`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: ` ${openChat ? "hidden" : ""} lg:block w-full ${showMessageCondition ? "lg:w-[40%]" : ""}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "px-2.5 flex justify-between items-center py-4 lg:px-6 w-full border-b border-[#AFB8CF] lg:border-r",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex gap-2 items-center relative w-[200px] justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-base leading-normal font-semibold lg:leading-[35px]",
                                                children: filter === "sellers" ? "All Seller Messages" : filter === "buyers" ? "All Buyer Messages" : "All Messages"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1197,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["IoIosArrowDown"], {
                                                ref: menuBtnRef,
                                                className: `w-[18px] h-[18px] cursor-pointer transition ease-in-out duration-300 ${openFilter ? "-rotate-180" : ""}`,
                                                onClick: ()=>setOpenFilter(!openFilter)
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1204,
                                                columnNumber: 19
                                            }, this),
                                            openFilter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `flex flex-col z-50 absolute top-full right-0 bg-white shadow-md rounded-md`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium py-2.5 px-8 hover:bg-blue-100 rounded-t-md border-b border-[#80808026] text-center cursor-pointer",
                                                        onClick: ()=>filterHandler("all"),
                                                        children: "All"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1216,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium py-2.5 px-8 hover:bg-blue-100 border-b border-[#80808026] text-center cursor-pointer",
                                                        onClick: ()=>filterHandler("buyers"),
                                                        children: "Buyer"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1222,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium py-2.5 px-8 hover:bg-blue-100 rounded-b-md text-center cursor-pointer",
                                                        onClick: ()=>filterHandler("sellers"),
                                                        children: "Seller"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1228,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1213,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/profile/messages/page.js",
                                        lineNumber: 1196,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 1195,
                                    columnNumber: 15
                                }, this),
                                showMessageCondition ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "border-b border-[#AFB8CF] py-4 px-2.5 lg:border-r",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative w-full",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                autoComplete: "off",
                                                type: "search",
                                                className: "focus:outline-none bg-[#F3FAFF] py-2 pl-[44px] pr-4 w-full rounded-lg text-sm placeholder:font-light leading-normal placeholder:text-[#9FA7BE]",
                                                placeholder: "Search or start a new chat",
                                                onChange: (e)=>setSearchText(e.target.value)
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1245,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["IoMdSearch"], {
                                                className: "w-5 h-5 absolute top-[25%] left-4"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1252,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/profile/messages/page.js",
                                        lineNumber: 1244,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 1243,
                                    columnNumber: 17
                                }, this) : "",
                                !chatListLoading ? showMessageCondition ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: " flex flex-col max-h-[60vh] pb-2.5 overflow-auto no_scrollbar",
                                    children: chatList.length > 0 ? [
                                        ...chatList
                                    ]?.map((chat, idx)=>{
                                        console.log("chat?.participantData receiver", chat?.participantData);
                                        const receiver = chat?.participantData?.find((participant)=>participant?._id !== userData?._id);
                                        const isSeller = receiver?._id === chat?.item_doc?.[0]?.createdBy?._id;
                                        console.log("receiver", receiver);
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `cursor-pointer py-4 px-2.5 border-b flex gap-4 border-[#AFB8CF] ${chatId?.toString() === chat._id.toString() ? "border-r-0 bg-[#F3FAFF]" : "lg:border-r"} `,
                                            onClick: ()=>{
                                                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateChatId"])(chat._id));
                                                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateItemId"])(chat.sellerItemId));
                                                openChatHandler(chat._id);
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "relative overflow-hidden aspect-auto w-14 min-w-14 h-16",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                        src: chat?.item_doc?.images?.length ? chat?.item_doc?.images[0] : avatarUrl(selectedChat.userName),
                                                        alt: "Book Img",
                                                        className: "object-cover rounded-lg w-14 h-16"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1290,
                                                        columnNumber: 31
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/profile/messages/page.js",
                                                    lineNumber: 1289,
                                                    columnNumber: 29
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-[90%]",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-base leading-normal font-semibold line-clamp-1",
                                                            children: `${receiver?.firstName} ${receiver?.lastName}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1302,
                                                            columnNumber: 31
                                                        }, this),
                                                        lastMessageText(chat),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "mt-2.5 text-[#0161AB] text-sm leading-normal flex flex-row items-center relative",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$lu$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LuClock3"], {
                                                                    className: "stroke-[#0161AB]"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1306,
                                                                    columnNumber: 33
                                                                }, this),
                                                                chat?.lastMessage?.createdAt ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatRelativeDate"])(chat?.lastMessage?.createdAt) : "Today",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "mx-1  bg-[#0161AB] w-[1px] h-[8px] scale-200 rounded-full relative -top-[2px]"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1312,
                                                                    columnNumber: 33
                                                                }, this),
                                                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatTo12HourTime"])(chat?.lastMessage?.createdAt ?? new Date())
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1305,
                                                            columnNumber: 31
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/profile/messages/page.js",
                                                    lineNumber: 1301,
                                                    columnNumber: 29
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MdDeleteOutline"], {
                                                        size: 25,
                                                        onClick: ()=>deleteConversation(chat._id)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1319,
                                                        columnNumber: 31
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/profile/messages/page.js",
                                                    lineNumber: 1318,
                                                    columnNumber: 29
                                                }, this)
                                            ]
                                        }, `chat-list-${idx}`, true, {
                                            fileName: "[project]/app/profile/messages/page.js",
                                            lineNumber: 1276,
                                            columnNumber: 27
                                        }, this);
                                    }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center py-4 px-2 bg-gray-50 mt-2 rounded-md",
                                        children: "No Data Found"
                                    }, void 0, false, {
                                        fileName: "[project]/app/profile/messages/page.js",
                                        lineNumber: 1328,
                                        columnNumber: 23
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 1262,
                                    columnNumber: 19
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full  flex justify-center items-center"
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 1334,
                                    columnNumber: 19
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: " w-full flex justify-center items-center"
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 1337,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 1190,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `  ${openChat ? "w-[100%]" : "w[0px]"} lg:w-[60%] relative p-0 m-0`,
                            children: [
                                showChatBodyCondition ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    ref: chatRef,
                                    style: {
                                        filter: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemListStatusEnum"].MARKED_AS_SOLD == selectedChat.status ? `blur(5px)` : ""
                                    },
                                    className: `${openChat ? "flex " : "hidden"} w-full px-2.5 z-[5] lg:flex flex-col lg:p-0  ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemListStatusEnum"].MARKED_AS_SOLD == selectedChat.status ? "backdrop-blur-md bg-white/30" : ""}`,
                                    children: [
                                        showChatBodyCondition && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "border-b border-[#AFB8CF] flex justify-between py-2.5 lg:py-4 lg:px-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-3.5",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["IoArrowBack"], {
                                                            className: "cursor-pointer lg:hidden",
                                                            onClick: closeChatHandler
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1367,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center gap-1.5 lg:gap-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "relative w-6 h-6 lg:w-[40px] lg:h-[40px] rounded-lg",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                            src: selectedChat?.backCoverImage || avatarUrl(selectedChat.userName),
                                                                            alt: "Book Image",
                                                                            className: "object-cover rounded-lg w-full h-full"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1374,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "absolute -bottom-[5px] -right-[5px] rounded-full w-3 h-3 lg:w-[17px] lg:h-[17px]",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                                src: selectedChat?.profileImage || avatarUrl(selectedChat.userName),
                                                                                alt: "Profile",
                                                                                className: "object-cover rounded-full z-10 w-full h-full"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                lineNumber: 1383,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1382,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1373,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-[11px] font-semibold leading-normal text-[#636363] lg:text-base",
                                                                    children: selectedChat?.userName
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1393,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1372,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/profile/messages/page.js",
                                                    lineNumber: 1366,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    ref: menuRef,
                                                    className: "relative cursor-pointer py-0.5 px-1 flex justify-center items-center custom_shadow_first rounded-lg lg:px-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HiOutlineDotsVertical"], {
                                                            onClick: ()=>setshowOptionThreeDot(true)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1403,
                                                            columnNumber: 25
                                                        }, this),
                                                        showOptionThreeDot && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "absolute left-[-100px] cursor-pointer py-[5px] px-1 flex justify-center items-center custom_shadow_first rounded-md lg:px-2",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                onClick: ()=>router.push(`/book-detail?id=${selectedChat.itemId}`),
                                                                children: "View More"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                lineNumber: 1408,
                                                                columnNumber: 29
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1407,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/profile/messages/page.js",
                                                    lineNumber: 1399,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/profile/messages/page.js",
                                            lineNumber: 1365,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "lg:pl-6",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                ref: messagesEndRef,
                                                className: `flex h-[60vh] py-10  flex-col gap-8 pt-11 transition-all linear duration-300 ${!openChatTemplate ? "pb-10" : chatToggle === "chats" ? "pb-35 md:pb-45" : "pb-[220px] md:pb-[270px]"} overflow-auto h-[75vh] no_scrollbar`,
                                                children: messages?.map((message, idx)=>sender?._id?.toString() === message?.sender?.toString() ? //  right side message
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex flex-col items-end self-end gap-1 w-[70%] pr-2",
                                                        children: RenderMessage(message)
                                                    }, `message-${idx}`, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1438,
                                                        columnNumber: 27
                                                    }, this) : // left side message
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex flex-col items-start self-start gap-1 w-[70%]",
                                                        children: RenderMessage(message)
                                                    }, `message-${idx}`, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1446,
                                                        columnNumber: 27
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1424,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/profile/messages/page.js",
                                            lineNumber: 1423,
                                            columnNumber: 19
                                        }, this),
                                        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemListStatusEnum"].MARKED_AS_SOLD !== selectedChat.status && showChatBodyCondition && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative z-[20] border",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute bottom-full w-full",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `flex gap-2 items-center bg-[#F9F9F9] ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$profile$2f$messages$2f$messageComponent$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].borderGradient}`,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `!absolute -top-[80%] left-1/2 -translate-x-1/2 translate-y-1/2 w-[41px] h-[22px] flex justify-center items-end cursor-pointer !rounded-t-full lg:w-[61px] lg:h-[33px] lg:top-[-110%] ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$profile$2f$messages$2f$messageComponent$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].borderGradient} ${openChatTemplate ? "" : ""}`,
                                                                    onClick: chatTemplateOpenHandler,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                            width: "0",
                                                                            height: "0",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                                                                id: "blue-gradient",
                                                                                x1: "100%",
                                                                                y1: "100%",
                                                                                x2: "0%",
                                                                                y2: "0%",
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                                                        stopColor: "#211f54",
                                                                                        offset: "11%"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/app/profile/messages/page.js",
                                                                                        lineNumber: 1478,
                                                                                        columnNumber: 35
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                                                        stopColor: "#0161ab",
                                                                                        offset: "98%"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/app/profile/messages/page.js",
                                                                                        lineNumber: 1479,
                                                                                        columnNumber: 35
                                                                                    }, this)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                lineNumber: 1471,
                                                                                columnNumber: 33
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1470,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MdKeyboardArrowDown"], {
                                                                            style: {
                                                                                fill: "url(#blue-gradient)"
                                                                            },
                                                                            className: `lg:w-7 lg:h-7 transition-transform duration-200 ${openChatTemplate ? "" : "-rotate-180"}`
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1482,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1464,
                                                                    columnNumber: 29
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `flex items-center ${selectedChat.isSeller ? "w-full" : "w-1/2"} justify-center gap-2 text-[9.5px] leading-[13px] pt-3 pb-2 cursor-pointer lg:text-sm lg:leading-[19px]  ${chatToggle !== "chats" ? "text-[#A0A0A0]" : "global_text_linear_gradient"}`,
                                                                    onClick: ()=>setChatToggle("chats"),
                                                                    children: [
                                                                        chatToggle !== "chats" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                            xmlns: "http://www.w3.org/2000/svg",
                                                                            width: "16",
                                                                            height: "13",
                                                                            viewBox: "0 0 22 18",
                                                                            fill: "none",
                                                                            className: "lg:w-[22px] lg:h-[18px]",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                                d: "M2 9C0.9 9 0 8.1 0 7V2C0 0.9 0.9 0 2 0H10C11.1 0 12 0.9 12 2V7C12 8.1 11.1 9 10 9H8V12L5 9H2ZM20 15C21.1 15 22 14.1 22 13V8C22 6.9 21.1 6 20 6H14V7C14 9.2 12.2 11 10 11V13C10 14.1 10.9 15 12 15H14V18L17 15H20Z",
                                                                                fill: "#A0A0A0"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                lineNumber: 1509,
                                                                                columnNumber: 35
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1501,
                                                                            columnNumber: 33
                                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                            xmlns: "http://www.w3.org/2000/svg",
                                                                            width: "16",
                                                                            height: "13",
                                                                            viewBox: "0 0 22 18",
                                                                            fill: "none",
                                                                            className: "lg:w-[22px] lg:h-[18px]",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                                    d: "M2 9C0.9 9 0 8.1 0 7V2C0 0.9 0.9 0 2 0H10C11.1 0 12 0.9 12 2V7C12 8.1 11.1 9 10 9H8V12L5 9H2ZM20 15C21.1 15 22 14.1 22 13V8C22 6.9 21.1 6 20 6H14V7C14 9.2 12.2 11 10 11V13C10 14.1 10.9 15 12 15H14V18L17 15H20Z",
                                                                                    fill: "url(#paint0_linear_2021_12436)"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                                    lineNumber: 1523,
                                                                                    columnNumber: 35
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                                                                        id: "paint0_linear_2021_12436",
                                                                                        x1: "19.5451",
                                                                                        y1: "1.55769",
                                                                                        x2: "-0.248734",
                                                                                        y2: "2.28634",
                                                                                        gradientUnits: "userSpaceOnUse",
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                                                                stopColor: "#211F54"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                                lineNumber: 1536,
                                                                                                columnNumber: 39
                                                                                            }, this),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                                                                offset: "1",
                                                                                                stopColor: "#0161AB"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                                lineNumber: 1537,
                                                                                                columnNumber: 39
                                                                                            }, this)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/app/profile/messages/page.js",
                                                                                        lineNumber: 1528,
                                                                                        columnNumber: 37
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                                    lineNumber: 1527,
                                                                                    columnNumber: 35
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1515,
                                                                            columnNumber: 33
                                                                        }, this),
                                                                        "Chats"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1490,
                                                                    columnNumber: 29
                                                                }, this),
                                                                !selectedChat.isSeller && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: `flex items-center w-1/2 justify-center gap-2 text-[9.5px] leading-[13px] pt-3 pb-2 cursor-pointer lg:text-sm lg:leading-[19px] ${chatToggle === "make offer" ? "global_text_linear_gradient" : "text-[#A0A0A0]"} `,
                                                                    onClick: ()=>setChatToggle("make offer"),
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                            xmlns: "http://www.w3.org/2000/svg",
                                                                            width: "15",
                                                                            height: "15",
                                                                            viewBox: "0 0 20 20",
                                                                            fill: "none",
                                                                            className: "lg:w-[18px] lg:h-[18px]",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                                    d: "M19.41 9.58L10.41 0.58C10.05 0.22 9.55 0 9 0H2C0.9 0 0 0.9 0 2V9C0 9.55 0.22 10.05 0.59 10.42L9.59 19.42C9.95 19.78 10.45 20 11 20C11.55 20 12.05 19.78 12.41 19.41L19.41 12.41C19.78 12.05 20 11.55 20 11C20 10.45 19.77 9.94 19.41 9.58ZM3.5 5C2.67 5 2 4.33 2 3.5C2 2.67 2.67 2 3.5 2C4.33 2 5 2.67 5 3.5C5 4.33 4.33 5 3.5 5Z",
                                                                                    fill: `${chatToggle === "make offer" ? "url(#paint0_linear_2021_16128)" : "#A0A0A0"} `
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                                    lineNumber: 1561,
                                                                                    columnNumber: 35
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                                                                        id: "paint0_linear_2021_16128",
                                                                                        x1: "17.7683",
                                                                                        y1: "1.73077",
                                                                                        x2: "-0.234176",
                                                                                        y2: "2.27298",
                                                                                        gradientUnits: "userSpaceOnUse",
                                                                                        children: [
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                                                                stopColor: "#211F54"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                                lineNumber: 1578,
                                                                                                columnNumber: 39
                                                                                            }, this),
                                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                                                                offset: "1",
                                                                                                stopColor: "#0161AB"
                                                                                            }, void 0, false, {
                                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                                lineNumber: 1579,
                                                                                                columnNumber: 39
                                                                                            }, this)
                                                                                        ]
                                                                                    }, void 0, true, {
                                                                                        fileName: "[project]/app/profile/messages/page.js",
                                                                                        lineNumber: 1570,
                                                                                        columnNumber: 37
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                                    lineNumber: 1569,
                                                                                    columnNumber: 35
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1553,
                                                                            columnNumber: 33
                                                                        }, this),
                                                                        "Make Offer"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1545,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1461,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: `global_linear_gradient text-white text-[9.5px] leading-normal py-3 px-5 lg:py-4 lg:px-8 lg:text-sm transition-all duration-300 ${openChatTemplate ? "max-h-[200px] opacity-100" : "max-h-0 opacity-0 !p-0"} `,
                                                            children: [
                                                                "Don't buy nuh 'puss in a bag'!",
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1596,
                                                                    columnNumber: 29
                                                                }, this),
                                                                "Ask about the item's condition, request additional photos, and confirm payment and pick-up/delivery options before you close the sale."
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1588,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: ` w-full bg-white transition-all duration-300 ${openChatTemplate ? "max-h-[200px] opacity-100" : "max-h-0 opacity-0"}`,
                                                            children: chatToggle === "chats" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex gap-3.5 pt-1 px-5 lg:py-[14px] pb-2.5 overflow-auto no_scrollbar",
                                                                children: userData?._id == itemOfConversation?.createdBy ? chatSuggestions.admin?.map((chat, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: `w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-t-xl rounded-bl-xl gradient-all-round-border border-2 lg:py-2 lg:px-3.5 lg:text-xs lg:rounded-t-[18px] lg:rounded-bl-[18px] cursor-pointer active:scale-95`,
                                                                        onClick: ()=>sendMessage(chat),
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "global_text_linear_gradient font-medium",
                                                                            children: chat
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1618,
                                                                            columnNumber: 41
                                                                        }, this)
                                                                    }, `predefined - chats - ${idx} `, false, {
                                                                        fileName: "[project]/app/profile/messages/page.js",
                                                                        lineNumber: 1613,
                                                                        columnNumber: 39
                                                                    }, this)) : chatSuggestions.client?.map((chat, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: `w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-t-xl rounded-bl-xl gradient-all-round-border border-2 lg:py-2 lg:px-3.5 lg:text-xs lg:rounded-t-[18px] lg:rounded-bl-[18px] cursor-pointer active:scale-95`,
                                                                        onClick: ()=>sendMessage(chat),
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "global_text_linear_gradient font-medium",
                                                                            children: chat
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/profile/messages/page.js",
                                                                            lineNumber: 1629,
                                                                            columnNumber: 41
                                                                        }, this)
                                                                    }, `predefined - chats - ${idx} `, false, {
                                                                        fileName: "[project]/app/profile/messages/page.js",
                                                                        lineNumber: 1624,
                                                                        columnNumber: 39
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                lineNumber: 1610,
                                                                columnNumber: 31
                                                            }, this) : // make offer components render based on seller and buyer
                                                            // selectedChat?.isSeller ?
                                                            // makeOfferComponents.seller[sellerMakeOfferShownComponent]
                                                            // :
                                                            makeOfferComponents.buyer[buyerMakeOfferShownComponent]
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1602,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/profile/messages/page.js",
                                                    lineNumber: 1460,
                                                    columnNumber: 25
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "relative w-full",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "w-full  flex gap-3 py-2.5 pr-4 bg-[#F3F3F3] items-center lg:py-4 lg:pr-6",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex justify-center items-center  mx-2",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FaMapMarkerAlt"], {
                                                                    className: "lg:w-5 lg:h-[27px] fill-[#575757] cursor-pointer",
                                                                    onClick: locationSend
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1650,
                                                                    columnNumber: 31
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                lineNumber: 1649,
                                                                columnNumber: 29
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                                                                className: "w-full   flex bg-white rounded-full",
                                                                onSubmit: sendMessageHandler,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                        type: "text",
                                                                        autoComplete: "off",
                                                                        name: "message",
                                                                        className: "w-full focus:outline-none text-[10px] leading-normal py-1.5 pl-3 pr-[50px] rounded-full placeholder:text-[#9FA7BE] lg:py-2.5 lg:pl-[21px] lg:text-[15px]",
                                                                        placeholder: "Type your message here ..."
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/app/profile/messages/page.js",
                                                                        lineNumber: 1660,
                                                                        columnNumber: 31
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "flex  items-center justify-between mr-3 ",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                autoComplete: "off",
                                                                                type: "file",
                                                                                ref: fileInputRef,
                                                                                onChange: handleFileChange,
                                                                                className: "hidden"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                lineNumber: 1669,
                                                                                columnNumber: 33
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                type: "button",
                                                                                className: " cursor-pointer flex justify-center items-center px-2  rounded-full",
                                                                                onClick: handleButtonClick,
                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FaPlusCircle"], {
                                                                                    size: 25
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                                    lineNumber: 1681,
                                                                                    columnNumber: 35
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                lineNumber: 1676,
                                                                                columnNumber: 33
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                                type: "submit",
                                                                                className: " cursor-pointer flex justify-center items-center p-1 lg:p-1.5 lg:px-1.5 bg-[#575757] rounded-full",
                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BiLogoTelegram"], {
                                                                                    size: 25,
                                                                                    className: "fill-white w-[14px] h-[14px] lg:w-3.5 lg:h-3.5"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                                    lineNumber: 1687,
                                                                                    columnNumber: 35
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                                lineNumber: 1683,
                                                                                columnNumber: 33
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/app/profile/messages/page.js",
                                                                        lineNumber: 1668,
                                                                        columnNumber: 31
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/app/profile/messages/page.js",
                                                                lineNumber: 1656,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1648,
                                                        columnNumber: 27
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/profile/messages/page.js",
                                                    lineNumber: 1647,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/profile/messages/page.js",
                                            lineNumber: 1459,
                                            columnNumber: 23
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 1348,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `${showMessageCondition ? "hidden lg:w-[60%]" : "flex"} lg:flex flex-col w-full justify-center items-center min-h-[70vh] md:min-h-[75vh] gap-[60px]`,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs",
                                            children: "No, messages,yet?"
                                        }, void 0, false, {
                                            fileName: "[project]/app/profile/messages/page.js",
                                            lineNumber: 1705,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                xmlns: "http://www.w3.org/2000/svg",
                                                width: "90",
                                                height: "70",
                                                viewBox: "0 0 129 106",
                                                fill: "none",
                                                className: "md:w-[129px] md:h-[106px]",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                        d: "M11.7273 52.7727C5.27727 52.7727 0 47.4955 0 41.0455V11.7273C0 5.27727 5.27727 0 11.7273 0H58.6364C65.0864 0 70.3636 5.27727 70.3636 11.7273V41.0455C70.3636 47.4955 65.0864 52.7727 58.6364 52.7727H46.9091V70.3636L29.3182 52.7727H11.7273ZM117.273 87.9545C123.723 87.9545 129 82.6773 129 76.2273V46.9091C129 40.4591 123.723 35.1818 117.273 35.1818H82.0909V41.0455C82.0909 53.9455 71.5364 64.5 58.6364 64.5V76.2273C58.6364 82.6773 63.9136 87.9545 70.3636 87.9545H82.0909V105.545L99.6818 87.9545H117.273Z",
                                                        fill: "url(#paint0_linear_2933_4071)"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1716,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                                            id: "paint0_linear_2933_4071",
                                                            x1: "114.605",
                                                            y1: "9.13373",
                                                            x2: "-1.45849",
                                                            y2: "13.4062",
                                                            gradientUnits: "userSpaceOnUse",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                                    stopColor: "#211F54"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1729,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                                                    offset: "1",
                                                                    stopColor: "#0161AB"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/profile/messages/page.js",
                                                                    lineNumber: 1730,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/profile/messages/page.js",
                                                            lineNumber: 1721,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/profile/messages/page.js",
                                                        lineNumber: 1720,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1708,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/profile/messages/page.js",
                                            lineNumber: 1707,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs leading-normal",
                                            children: "We’ll keep messages for any item you’re selling in here"
                                        }, void 0, false, {
                                            fileName: "[project]/app/profile/messages/page.js",
                                            lineNumber: 1735,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 1700,
                                    columnNumber: 17
                                }, this),
                                __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$constant$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ItemListStatusEnum"].MARKED_AS_SOLD == selectedChat.status && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white   max-w-full h-[30%] absolute bottom-[0%] flex items-cemter justify-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-fit  flex flex-col items-center justify-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                className: "mx-auto",
                                                src: "/icons/bookEmpty.png"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1743,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "md:w-[60%] text-center mx-auto",
                                                children: "The chat has been removed because the seller mark the book as sold"
                                            }, void 0, false, {
                                                fileName: "[project]/app/profile/messages/page.js",
                                                lineNumber: 1744,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/profile/messages/page.js",
                                        lineNumber: 1742,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/profile/messages/page.js",
                                    lineNumber: 1741,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/profile/messages/page.js",
                            lineNumber: 1342,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/profile/messages/page.js",
                    lineNumber: 1184,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/profile/messages/page.js",
            lineNumber: 1045,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/profile/messages/page.js",
        lineNumber: 1044,
        columnNumber: 7
    }, this);
}
const __TURBOPACK__default__export__ = myMessages;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1e111d39._.js.map