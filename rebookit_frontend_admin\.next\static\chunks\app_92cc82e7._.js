(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/redux/slices/storeSlice.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "changeCategoryState": (()=>changeCategoryState),
    "changeCategoryTab": (()=>changeCategoryTab),
    "changeMemberShipTab": (()=>changeMemberShipTab),
    "changeSubCategoryState": (()=>changeSubCategoryState),
    "changeSubSubCategoryState": (()=>changeSubSubCategoryState),
    "changeTransactionTab": (()=>changeTransactionTab),
    "clearOtp": (()=>clearOtp),
    "default": (()=>__TURBOPACK__default__export__),
    "resetSubscription": (()=>resetSubscription),
    "resetcurrentSubscription": (()=>resetcurrentSubscription),
    "selectPageHandler": (()=>selectPageHandler),
    "setOtp": (()=>setOtp),
    "setcurrentSubscription": (()=>setcurrentSubscription)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    otp: null,
    token: null,
    categoryTab: 0,
    selectedPage: "Dashboard",
    memberShipTab: 0,
    transactionTab: 0,
    active: true,
    currentSubscription: {},
    categoriesState: {
        categoryId: "",
        subCategoryId: "",
        subSubCategoryId: ""
    }
};
const storeSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'Rebookit store',
    initialState,
    reducers: {
        setOtp: (state, action)=>{
            state.otp = action.payload;
        },
        changeMemberShipTab: (state, action)=>{
            state.memberShipTab = action.payload;
        },
        changeTransactionTab: (state, action)=>{
            state.transactionTab = action.payload;
        },
        changeCategoryTab: (state, action)=>{
            state.categoryTab = action.payload;
        },
        clearOtp: (state)=>{
            state.otp = null;
        },
        setcurrentSubscription: (state, action)=>{
            state.currentSubscription = {
                ...action.payload,
                isEdit: true
            };
        },
        resetcurrentSubscription: (state, action)=>{
            state.currentSubscription = {};
        },
        resetSubscription: (state)=>{
            state.currentSubscription = {};
        },
        selectPageHandler: (state, action)=>{
            state.selectedPage = action.payload;
        },
        changeCategoryState: (state, action)=>{
            state.categoriesState.categoryId = action.payload;
        },
        changeSubCategoryState: (state, action)=>{
            state.categoriesState.subCategoryId = action.payload;
        },
        changeSubSubCategoryState: (state, action)=>{
            state.categoriesState.subSubCategoryId = action.payload;
        }
    }
});
const { resetcurrentSubscription, changeCategoryState, changeSubCategoryState, changeSubSubCategoryState, setOtp, clearOtp, setcurrentSubscription, resetSubscription, selectPageHandler, changeMemberShipTab, changeTransactionTab, changeCategoryTab } = storeSlice.actions;
const __TURBOPACK__default__export__ = storeSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/redux/store.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "persistor": (()=>persistor),
    "store": (()=>store)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistStore.js [app-client] (ecmascript) <export default as persistStore>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistReducer.js [app-client] (ecmascript) <export default as persistReducer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/lib/storage/index.js [app-client] (ecmascript)"); // localStorage
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/slices/storeSlice.js [app-client] (ecmascript)");
;
;
;
;
const persistConfig = {
    key: 'rebookit-store',
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
};
const persistedReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(persistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        storeData: persistedReducer
    },
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({
            serializableCheck: false
        })
});
const persistor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__["persistStore"])(store); // ------------------------------------------------------------------------------------------------------
 // import { configureStore } from '@reduxjs/toolkit';
 // import { persistStore, persistReducer } from 'redux-persist';
 // import storage from 'redux-persist/lib/storage'; // localStorage
 // import storeReducer from './slices/storeSlice';
 // const persistConfig = {
 //   key: 'rebookit-store',
 //   storage,
 // };
 // const persistedReducer = persistReducer(persistConfig, storeReducer);
 // export const store = configureStore({
 //   reducer: {
 //     storeData: persistedReducer,
 //   },
 //   middleware: (getDefaultMiddleware) =>
 //     getDefaultMiddleware({
 //       serializableCheck: false, // required for redux-persist
 //     }),
 // });
 // export const persistor = persistStore(store);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/utils/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "debouncFunc": (()=>debouncFunc),
    "getToken": (()=>getToken),
    "randomColorPickHandler": (()=>randomColorPickHandler),
    "removeToken": (()=>removeToken),
    "setToken": (()=>setToken),
    "shortNameHandler": (()=>shortNameHandler),
    "userDataFromLocal": (()=>userDataFromLocal)
});
const setToken = (token)=>{
    localStorage.setItem("token", token);
};
const getToken = ()=>{
    return localStorage.getItem("token");
};
const removeToken = ()=>{
    return localStorage.removeItem("token");
};
const randomColorPickHandler = ()=>{
    const colors = [
        "bg-slate-200",
        "bg-red-200",
        "bg-orange-200",
        "bg-amber-200",
        "bg-yellow-200",
        "bg-lime-200",
        "bg-green-200",
        "bg-emerald-200",
        "bg-teal-200",
        "bg-cyan-200",
        "bg-sky-200",
        "bg-blue-200",
        "bg-indigo-200",
        "bg-violet-200",
        "bg-purple-200",
        "bg-fuchsia-200",
        "bg-pink-200",
        "bg-rose-200"
    ];
    return colors[Math.floor(Math.random() * colors.length)];
};
const shortNameHandler = (val)=>{
    if (!val || val === "undefined") return val;
    let arr = val?.split(" ");
    let result = (arr[0][0] === undefined ? "" : arr[0][0]) + (arr[1] ? arr[1][0] === undefined ? "" : arr[1][0] : "");
    return result;
};
const debouncFunc = (func, delay)=>{
    let interval = null;
    return (args)=>{
        clearTimeout(interval);
        interval = setTimeout(()=>{
            func(args);
        }, delay);
    };
};
const userDataFromLocal = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return JSON.parse(localStorage.getItem("userData"));
    }
    "TURBOPACK unreachable";
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/config/api.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://api.rebookitclub.com");
const USER_BASE_URL = `${BASE_URL}`;
const CHAT_BASE_URL = `${BASE_URL}/chat`;
const ELASTIC_BASE_URL = `${BASE_URL}/elasticsearch`;
// const USER_BASE_URL = `http://localhost:4002`;
// const CHAT_BASE_URL = `http://localhost:4001`;
// const ELASTIC_BASE_URL = `http://localhost:4004`;
const USER_ROUTES = {
    // ADMIN ROUTES
    GET_ADMIN: `${USER_BASE_URL}/api/admin`,
    //LOGIN ROUTES
    lOGIN: `${USER_BASE_URL}/api/user/login`,
    REGISTER: `${USER_BASE_URL}/api/user/register`,
    SEND_OTP: `${USER_BASE_URL}/api/user/send-otp`,
    VERIFY_OTP: `${USER_BASE_URL}/api/user/verify-otp`,
    RESEND_OTP: `${USER_BASE_URL}/api/user/resend-otp`,
    FORGOT_PASSWORD: `${USER_BASE_URL}/api/user/forgot-password`,
    CREATE_NEW_PASSWORD: `${USER_BASE_URL}/api/user/create-new-password`,
    // book search routes
    ISBN_SEARCH: `${USER_BASE_URL}/api/books/isbn/{{ISBN}}`,
    // upload
    MULTI_UPLOAD_FILES: `${USER_BASE_URL}/api/admin/multiple-upload`,
    SINGLE_UPLOAD_FILE: `${USER_BASE_URL}/api/admin/single-upload`,
    // USER INFO
    USER_INFO: `${USER_BASE_URL}/api/user`,
    EDIT_USER_INFO: `${USER_BASE_URL}/api/user/edit-profile`,
    // list item
    LIST_ITEM: `${USER_BASE_URL}/api/item/search/admin`,
    DELETE_LIST_ITEM: `${USER_BASE_URL}/api/admin/item`,
    UPDATE_LIST_ITEM: `${USER_BASE_URL}/api/admin/item/actionStatus`,
    // get subscription plans     
    GET_ALL_SUBSCRIPTION: `${USER_BASE_URL}/api/admin/subscription/plan`,
    CREATE_SUBSCRIPTION: `${USER_BASE_URL}/api/admin/create/subscription/plan`,
    UPDATE_SUBSCRIPTION: `${USER_BASE_URL}/api/ admin/subscription/plan/{{planId}}`,
    // dashboard routes
    GET_OVERVIEW: `${USER_BASE_URL}/api/admin/dashboard/overview`,
    GET_LAST_MONTH_SUBSCRIBERS: `${USER_BASE_URL}/api/admin/dashboard/lastMonthSubscribers`,
    GET_TOTAL_AD_REQUEST: `${USER_BASE_URL}/api/admin/dashboard/totalAdRequest`
};
const CHAT_ROUTES = {
    SERVER: ``
};
const ELASTIC_DB_ROUTES = {
    SEARCH: ``
};
module.exports = {
    USER_ROUTES,
    CHAT_ROUTES,
    ELASTIC_DB_ROUTES
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/layout.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/store.js [app-client] (ecmascript)");
// import { publicRoutes } from './utils/publicRoutes';
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/integration/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
;
;
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
const Header = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/app/components/layout/Header/Header.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/app/components/layout/Header/Header.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    suspense: true
});
_c = Header;
const Sidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/app/components/layout/Sidebar/Sidebar.js [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/app/components/layout/Sidebar/Sidebar.js [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    suspense: true
});
_c1 = Sidebar;
;
;
;
const publicRoutes = [
    '/login',
    '/create-password',
    '/forgot-password'
];
function RootLayout({ children }) {
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const isPublicRoute = publicRoutes.includes(pathname);
    const fetchAdmin = ()=>{
        let token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])();
        fetch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["USER_ROUTES"].GET_ADMIN, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
            }
        }).then(async (res)=>{
            if (res.status === 401) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeToken"])();
                router.push("/login");
                setIsLoading(false);
                return;
            }
            const response = await res.json();
            if (!response?.data) {
                // Optionally show an error, but do not log out
                setIsLoading(false);
                return;
            }
            setIsLoading(false);
        }).catch((error)=>{
            setIsLoading(false);
        // Optionally show an error, but do not log out
        });
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RootLayout.useEffect": ()=>{
            setIsLoading(true);
            fetchAdmin();
        }
    }["RootLayout.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RootLayout.useEffect": ()=>{
            const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])();
            if (auth && isPublicRoute) {
                router.replace("/");
                return;
            }
            // If auth exists or it's a public route, stop loading
            if (auth || isPublicRoute) {
                setIsLoading(false);
                return;
            }
            // If no auth and not public route, redirect
            if (!auth && !isPublicRoute) {
                router.replace('/login');
            }
        }
    }["RootLayout.useEffect"], [
        pathname
    ]);
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
            lang: "en",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: "flex items-center justify-center min-h-screen",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"
                        }, void 0, false, {
                            fileName: "[project]/app/layout.js",
                            lineNumber: 99,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mt-4 text-lg",
                            children: "Loading..."
                        }, void 0, false, {
                            fileName: "[project]/app/layout.js",
                            lineNumber: 100,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/layout.js",
                    lineNumber: 98,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/layout.js",
                lineNumber: 97,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/layout.js",
            lineNumber: 96,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
            store: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"],
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PersistGate"], {
                loading: null,
                persistor: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persistor"],
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                    style: {
                        overflow: 'hidden'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ToastContainer"], {
                            className: "z-[999]"
                        }, void 0, false, {
                            fileName: "[project]/app/layout.js",
                            lineNumber: 113,
                            columnNumber: 11
                        }, this),
                        isPublicRoute ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-full h-screen overflow-y-auto",
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/app/layout.js",
                            lineNumber: 115,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex h-screen",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Sidebar, {}, void 0, false, {
                                    fileName: "[project]/app/layout.js",
                                    lineNumber: 118,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col flex-1 h-screen",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Header, {
                                            className: "h-[100px]"
                                        }, void 0, false, {
                                            fileName: "[project]/app/layout.js",
                                            lineNumber: 120,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "max-w-[1440px] w-full mx-auto p-[24px] md:p-[24px] overflow-y-auto",
                                            style: {
                                                height: 'calc(100vh - 100px)',
                                                scrollBehavior: 'smooth'
                                            },
                                            children: children
                                        }, void 0, false, {
                                            fileName: "[project]/app/layout.js",
                                            lineNumber: 121,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/layout.js",
                                    lineNumber: 119,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/layout.js",
                            lineNumber: 117,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/layout.js",
                    lineNumber: 112,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/layout.js",
                lineNumber: 111,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/layout.js",
            lineNumber: 110,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/layout.js",
        lineNumber: 109,
        columnNumber: 5
    }, this);
}
_s(RootLayout, "H3BA3ayeFwXbMcsA87Zhz1SPBK4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c2 = RootLayout;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "Header");
__turbopack_context__.k.register(_c1, "Sidebar");
__turbopack_context__.k.register(_c2, "RootLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=app_92cc82e7._.js.map