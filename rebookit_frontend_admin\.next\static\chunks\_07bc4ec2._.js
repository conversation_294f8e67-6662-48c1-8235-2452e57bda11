(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
// import history from "./history";
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    console.log("error", error);
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    // Only log out on true 401 Unauthorized from response
    if (responseStatus === 401) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeToken"])();
        if ("object" !== 'undefined' && window.location) {
            window.location.href = "/login";
        }
        return;
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.messages)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        console.log("error log is", error);
        if (Array.isArray(error?.response?.data?.message) || Array?.isArray(error?.data?.message)) error?.response?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if (localStorage.getItem("token")) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if (localStorage.getItem("token")) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://api.rebookitclub.com");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    // const token = localStorage.getItem("auth");
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/membership.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMemberShipPlan": (()=>getMemberShipPlan),
    "getMembers": (()=>getMembers),
    "saveMemberShipPlan": (()=>saveMemberShipPlan),
    "updateMemberShipPlan": (()=>updateMemberShipPlan)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/service/axios.js [app-client] (ecmascript)");
;
;
let uri = {
    plan: "/admin/plan",
    savePlan: "/admin/plan",
    updatePlan: `/admin/plan`,
    members: "/admin/members/filter-search"
};
const getMemberShipPlan = async ()=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.plan}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const saveMemberShipPlan = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.savePlan}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const updateMemberShipPlan = async (id, body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.updatePlan + "/" + id}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getMembers = async (data, query)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.members}` + query, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
} // replace("{{planId}}")
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/userManagment.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getUserDetails": (()=>getUserDetails),
    "getUsers": (()=>getUsers),
    "login": (()=>login),
    "updateUser": (()=>updateUser)
});
const { axiosErrorHandler } = __turbopack_context__.r("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
const instance = __turbopack_context__.r("[project]/app/service/axios.js [app-client] (ecmascript)");
let uri = {
    login: "/user/login",
    addSubCategory: "/master/subCategory",
    upload: `/admin/single-upload`,
    users: `admin/search-users`,
    user: `/user`,
    userDetail: `/admin/user`
};
const login = async (data)=>{
    let response = await instance.post(uri.login, data).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const getUsers = async (data, query)=>{
    let response = await instance.post(uri.users + query, data).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const updateUser = async (id, data)=>{
    let response = await instance.put(uri.user + `/${id}`, data).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const getUserDetails = async (id)=>{
    let response = await instance.get(uri.userDetail + `/${id}`).catch(axiosErrorHandler);
    // console.log("getUserDetails response", response)
    return response;
}; // export const updateUser = async (id, data) => {
 //   let response = await instance
 //     .put(uri.user + `/${id}`, data)
 //     .catch(axiosErrorHandler);
 //   // console.log("login test response", response)
 //   return response;
 // };
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/user-management/ActionBox.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ActionBox)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bs$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/bs/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$userManagment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/service/userManagment.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function ActionBox({ data, funcAfterAction }) {
    _s();
    let menuRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const copyFunction = ()=>{
        navigator.clipboard.writeText(data._id).then((item)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("User ID Copied");
            document.getElementById(data._id).classList.add("hidden");
        }).catch((err)=>{});
    };
    const states = {
        active: "active",
        suspend: "suspended"
    };
    const blockUnblockFunc = async (name)=>{
        let payload = {
            status: data.status == "active" ? states.suspend : states.active
        };
        let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$userManagment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateUser"])(data._id, payload);
        console.log("response", response);
        if (response.status == 200) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("User Status Changes");
            document.getElementById(data._id).classList.add("hidden");
            funcAfterAction();
        }
    };
    const referseObject = (name)=>{
        if (states[name] == states.active) {
            return "Suspend";
        } else {
            return states.active;
        }
    };
    console.log("referseObject", referseObject());
    let dropDownValue = [
        {
            label: "copy",
            func: copyFunction
        },
        {
            label: referseObject(data.status),
            func: blockUnblockFunc
        }
    ];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ActionBox.useEffect": ()=>{
            const handleClickOutside = {
                "ActionBox.useEffect.handleClickOutside": (e)=>{
                    if (menuRef.current && !menuRef.current.contains(e.target)) {
                        document.getElementById(data._id).classList.add("hidden");
                    }
                }
            }["ActionBox.useEffect.handleClickOutside"];
            document.addEventListener("mousedown", handleClickOutside);
            return ({
                "ActionBox.useEffect": ()=>document.removeEventListener("mousedown", handleClickOutside)
            })["ActionBox.useEffect"];
        }
    }["ActionBox.useEffect"], []);
    document.body.click(()=>document.getElementById(data._id).classList.add("hidden"));
    const DropDown = ()=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            id: data._id,
            ref: menuRef,
            className: "hidden absolute z-10 mt-1 min-w-[100px] max-w-[100px] w-fit rounded-md bg-white shadow-lg border border-slate-200 py-1",
            children: dropDownValue?.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: (e)=>{
                        e.stopPropagation();
                        item?.func?.();
                    // closeDropdown();
                    },
                    className: "w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100 transition-colors",
                    children: item?.label
                }, `${item.id}-${index}`, false, {
                    fileName: "[project]/app/user-management/ActionBox.js",
                    lineNumber: 66,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/app/user-management/ActionBox.js",
            lineNumber: 58,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$bs$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BsThreeDotsVertical"], {
                className: "cursor-pointer mx-auto",
                onClick: ()=>document.getElementById(data._id).classList.remove("hidden")
            }, void 0, false, {
                fileName: "[project]/app/user-management/ActionBox.js",
                lineNumber: 83,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DropDown, {}, void 0, false, {
                fileName: "[project]/app/user-management/ActionBox.js",
                lineNumber: 89,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/user-management/ActionBox.js",
        lineNumber: 82,
        columnNumber: 5
    }, this);
}
_s(ActionBox, "lbfKxozlpk19p2tUpYavRIkbEU0=");
_c = ActionBox;
var _c;
__turbopack_context__.k.register(_c, "ActionBox");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/InitialAvatar/CreateInitialAvatar.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createInitialsAvatar": (()=>createInitialsAvatar)
});
function createInitialsAvatar(name, options = {}) {
    // Default options
    const { size = 100, bgColor = null, textColor = "#ffffff", shape = "circle", randomColor = true } = options;
    // Generate a random dark/rich color that works well with white text
    const generateRandomColor = ()=>{
        const hue = Math.floor(Math.random() * 360);
        const saturation = 70 + Math.floor(Math.random() * 30); // 70-100%
        const lightness = 30 + Math.floor(Math.random() * 20); // 30-50%
        return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    };
    // Get initials from name
    const getInitials = (name)=>{
        if (!name || typeof name !== "string") return "";
        const words = name.trim().split(/\s+/).filter((word)=>word.length > 0);
        if (words.length === 0) return "";
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        return `${words[0].charAt(0)}${words[words.length - 1].charAt(0)}`.toUpperCase();
    };
    const initials = getInitials(name);
    const fontSize = size * 0.4;
    // Determine background color
    const finalBgColor = bgColor || (randomColor ? generateRandomColor() : "#cccccc");
    // Create SVG based on shape
    let svgContent;
    if (shape === "circle") {
        const radius = size / 2;
        svgContent = `
      <circle cx="${radius}" cy="${radius}" r="${radius}" fill="${finalBgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
    } else {
        svgContent = `
      <rect width="100%" height="100%" fill="${finalBgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
    }
    // Create full SVG
    const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" 
         width="${size}" 
         height="${size}" 
         viewBox="0 0 ${size} ${size}">
      ${svgContent}
    </svg>
  `;
    // Convert to data URL
    return `data:image/svg+xml,${encodeURIComponent(svg)}`;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/Pagination.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Pagination)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
function Pagination({ setPageSize, setCurrentPage, getListing, currentPage, totalPages, totalItems, pageSize }) {
    console.log("totalPages inpagination", totalPages);
    const handlePageChange = (number)=>{
        setCurrentPage(number);
    };
    if (totalPages > 1) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col md:flex-row items-center justify-end mt-4 gap-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>handlePageChange(currentPage - 1),
                    disabled: currentPage === 1,
                    className: `px-3 py-1 rounded border text-sm ${currentPage === 1 ? "bg-gray-100 cursor-not-allowed" : "hover:bg-gray-100"}`,
                    children: "Previous"
                }, void 0, false, {
                    fileName: "[project]/app/components/common/Pagination.js",
                    lineNumber: 45,
                    columnNumber: 9
                }, this),
                Array.from({
                    length: totalPages
                }, (_, i)=>{
                    let pageNum;
                    if (totalPages <= 5) {
                        pageNum = i + 1;
                    } else if (currentPage <= 3) {
                        pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                    } else {
                        pageNum = currentPage - 2 + i;
                    }
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>handlePageChange(pageNum),
                        className: `px-3 py-1 rounded border text-sm ${currentPage === pageNum ? "bg-[linear-gradient(268deg,_#211f54_11.09%,_#0161ab_98.55%)] text-white" : "hover:bg-gray-100"}`,
                        children: pageNum
                    }, pageNum, false, {
                        fileName: "[project]/app/components/common/Pagination.js",
                        lineNumber: 69,
                        columnNumber: 13
                    }, this);
                }),
                totalPages > 5 && currentPage < totalPages - 2 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "px-2",
                    children: "..."
                }, void 0, false, {
                    fileName: "[project]/app/components/common/Pagination.js",
                    lineNumber: 83,
                    columnNumber: 11
                }, this),
                totalPages > 5 && currentPage < totalPages - 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>handlePageChange(totalPages),
                    className: `px-3 py-1 rounded border text-sm ${currentPage === totalPages ? "bg-blue-500 text-white" : "hover:bg-gray-100"}`,
                    children: totalPages
                }, void 0, false, {
                    fileName: "[project]/app/components/common/Pagination.js",
                    lineNumber: 87,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>handlePageChange(currentPage + 1),
                    disabled: currentPage === totalPages,
                    className: `px-3 py-1 rounded border text-sm ${currentPage === totalPages ? "bg-gray-100 cursor-not-allowed" : "hover:bg-gray-100"}`,
                    children: "Next"
                }, void 0, false, {
                    fileName: "[project]/app/components/common/Pagination.js",
                    lineNumber: 98,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/common/Pagination.js",
            lineNumber: 44,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/common/Pagination.js",
        lineNumber: 18,
        columnNumber: 5
    }, this);
    else return "";
}
_c = Pagination;
var _c;
__turbopack_context__.k.register(_c, "Pagination");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/icons/magnifier_icon.svg (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/magnifier_icon.75c99ed5.svg");}}),
"[project]/public/icons/magnifier_icon.svg.mjs { IMAGE => \"[project]/public/icons/magnifier_icon.svg (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$icons$2f$magnifier_icon$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/icons/magnifier_icon.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$icons$2f$magnifier_icon$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 24,
    height: 24,
    blurDataURL: null,
    blurWidth: 0,
    blurHeight: 0
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/user-management/page.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$membership$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/service/membership.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$userManagment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/service/userManagment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$user$2d$management$2f$ActionBox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/user-management/ActionBox.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/InitialAvatar/CreateInitialAvatar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$Pagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/Pagination.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$icons$2f$magnifier_icon$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$icons$2f$magnifier_icon$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/icons/magnifier_icon.svg.mjs { IMAGE => "[project]/public/icons/magnifier_icon.svg (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function Member({ setSelectedTab }) {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [membersList, setmembersList] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [currentPage, setCurrentPage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [pageSize, setPageSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(10);
    const [totalItems, setTotalItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [totalPages, setTotalPages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    console.log("memberlist", membersList);
    const [isLoading, setisLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Member.useEffect": ()=>{
            // setSelectedTab(0); // <- maybe causing loop
            getMembersFunc();
        }
    }["Member.useEffect"], [
        currentPage,
        pageSize
    ]);
    let memberTable = [
        {
            image: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
            name: "Tenner Finisha",
            email: "<EMAIL>",
            planName: "Gold",
            expireDate: "15-03-2025",
            transactionId: "#2342",
            status: "active"
        },
        {
            image: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
            name: "Emeto  Winner",
            email: "<EMAIL>",
            planName: "Basic",
            expireDate: "15-03-2025",
            transactionId: "#45634",
            status: "active"
        },
        {
            image: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
            name: "Tessy  Ommah",
            email: "<EMAIL>",
            planName: "Basic",
            expireDate: "15-03-2025",
            transactionId: "#45634",
            status: "inActive"
        }
    ];
    // let activeInactive={
    //     active:return <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div><div className="text-[#027A48]"> {item.status}</div></div>
    //     ,inActive:<div className="rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"></div><div className="text-[#F15046]"> {item.status}</div></div>
    // }
    const ActiveInactive = (name)=>{
        if (name == "active") {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center mx-auto ",
                children: [
                    " ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"
                    }, void 0, false, {
                        fileName: "[project]/app/user-management/page.js",
                        lineNumber: 77,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-[#027A48] text-sm",
                        children: [
                            " ",
                            name
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/user-management/page.js",
                        lineNumber: 78,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/user-management/page.js",
                lineNumber: 75,
                columnNumber: 9
            }, this);
        } else {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center mx-auto ",
                children: [
                    " ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"
                    }, void 0, false, {
                        fileName: "[project]/app/user-management/page.js",
                        lineNumber: 85,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-[#F15046] text-sm",
                        children: [
                            " ",
                            name
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/user-management/page.js",
                        lineNumber: 86,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/user-management/page.js",
                lineNumber: 83,
                columnNumber: 9
            }, this);
        }
    };
    const copyFunction = ()=>{};
    const getMembersFunc = async (value = "")=>{
        let query = "?";
        // if (currentPage) {
        //   query = query + `&page=${currentPage}`;
        // }
        if (pageSize) {
            query = query + `&pageSize=${pageSize}`;
        }
        setisLoading(true);
        let membersData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$userManagment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUsers"])(value, query);
        if (membersData.status == 200) {
            setmembersList(membersData.data.data);
            setPageSize(membersData.data.pageSize);
            setTotalPages(membersData.data.totalPages);
            setTotalItems(membersData.data.totalCount);
        }
        // setTotalItems(bookData?.totalCount || 0);
        // setTotalPages(bookData?.totalPages || 1);
        setisLoading(false);
    };
    console.log("membersList", membersList);
    const searchHandle = (event)=>{
        console.log(event.target.value, "debounce event");
        let payload = {
            searchTerm: event.target.value.toString() || ""
        };
        getMembersFunc(payload);
    };
    let debounceHandle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["debouncFunc"])(searchHandle, 1000);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-lg p-3 ",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between my-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "font-semibold text-[20px]",
                        children: "User Management"
                    }, void 0, false, {
                        fileName: "[project]/app/user-management/page.js",
                        lineNumber: 130,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-gray-50 px-2 rounded-lg flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-6 h-6  relative overflow-hidden",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$icons$2f$magnifier_icon$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$icons$2f$magnifier_icon$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                            alt: "Search Icon",
                                            fill: true,
                                            className: "object-cover",
                                            sizes: "24px"
                                        }, void 0, false, {
                                            fileName: "[project]/app/user-management/page.js",
                                            lineNumber: 139,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/user-management/page.js",
                                        lineNumber: 138,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        placeholder: "Search Items...",
                                        className: "rounded-lg outline-none bg-gray-50 ps-2 p-2",
                                        onChange: debounceHandle
                                    }, void 0, false, {
                                        fileName: "[project]/app/user-management/page.js",
                                        lineNumber: 147,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/user-management/page.js",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "statusFilter"
                            }, void 0, false, {
                                fileName: "[project]/app/user-management/page.js",
                                lineNumber: 170,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/user-management/page.js",
                        lineNumber: 131,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/user-management/page.js",
                lineNumber: 129,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                className: "w-full border border-[#EFF1F4] rounded-lg border-separate table-fixed",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                        className: "border-b border-[#EFF1F4]",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "px-3 py-3 font-medium text-[14px] bg-[#FAFBFB] w-[30%] text-left",
                                    children: "Customer"
                                }, void 0, false, {
                                    fileName: "[project]/app/user-management/page.js",
                                    lineNumber: 179,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "py-3 font-medium text-[14px] w-[13%] bg-[#FAFBFB] text-center",
                                    children: "Book Listed"
                                }, void 0, false, {
                                    fileName: "[project]/app/user-management/page.js",
                                    lineNumber: 182,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "py-3 font-medium text-[14px] w-[13%] bg-[#FAFBFB] text-center",
                                    children: "Regd. Date"
                                }, void 0, false, {
                                    fileName: "[project]/app/user-management/page.js",
                                    lineNumber: 185,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "py-3 font-medium text-[14px] w-[13%] bg-[#FAFBFB] text-center",
                                    children: "Plan Name"
                                }, void 0, false, {
                                    fileName: "[project]/app/user-management/page.js",
                                    lineNumber: 188,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "py-3 font-medium text-[14px] w-[13%] bg-[#FAFBFB] text-center",
                                    children: "Status"
                                }, void 0, false, {
                                    fileName: "[project]/app/user-management/page.js",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "py-3 font-medium text-[14px] w-[8%] bg-[#FAFBFB] text-center",
                                    children: "Action"
                                }, void 0, false, {
                                    fileName: "[project]/app/user-management/page.js",
                                    lineNumber: 194,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/user-management/page.js",
                            lineNumber: 175,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/user-management/page.js",
                        lineNumber: 174,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                        children: !isLoading ? membersList?.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                className: "bg-white",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "border-b border-[#EFF1F4] px-3 py-3 bg-white",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            onClick: ()=>router.push(`/userdetail?id=${item._id}`),
                                            className: "flex items-center cursor-pointer",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                    className: "w-10 h-10 rounded-full mr-3 shrink-0",
                                                    src: item.image || (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createInitialsAvatar"])(`${item.firstName || ""} ${item.lastName || ""}`.trim(), {
                                                        size: 40
                                                    }),
                                                    alt: `${item.firstName} ${item.lastName}`
                                                }, void 0, false, {
                                                    fileName: "[project]/app/user-management/page.js",
                                                    lineNumber: 211,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: " min-w-0",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "font-medium text-[14px] truncate",
                                                            children: [
                                                                item?.firstName,
                                                                " ",
                                                                item?.lastName
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/user-management/page.js",
                                                            lineNumber: 225,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-gray-300 text-[13px] truncate",
                                                            children: item?.email
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/user-management/page.js",
                                                            lineNumber: 228,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/user-management/page.js",
                                                    lineNumber: 224,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/user-management/page.js",
                                            lineNumber: 207,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/user-management/page.js",
                                        lineNumber: 206,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center",
                                        children: item?.allListingItems?.length
                                    }, void 0, false, {
                                        fileName: "[project]/app/user-management/page.js",
                                        lineNumber: 234,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center",
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(item.registeredAt).format("DD-MM-YYYY") || "NA"
                                    }, void 0, false, {
                                        fileName: "[project]/app/user-management/page.js",
                                        lineNumber: 237,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center",
                                        children: item?.planDoc?.planName
                                    }, void 0, false, {
                                        fileName: "[project]/app/user-management/page.js",
                                        lineNumber: 240,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center",
                                        children: ActiveInactive(item?.status, item)
                                    }, void 0, false, {
                                        fileName: "[project]/app/user-management/page.js",
                                        lineNumber: 243,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$user$2d$management$2f$ActionBox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            data: item,
                                            funcAfterAction: getMembersFunc
                                        }, void 0, false, {
                                            fileName: "[project]/app/user-management/page.js",
                                            lineNumber: 247,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/user-management/page.js",
                                        lineNumber: 246,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/app/user-management/page.js",
                                lineNumber: 202,
                                columnNumber: 15
                            }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                colSpan: 7,
                                className: "border-b border-[#EFF1F4] px-3 py-4 bg-white text-center",
                                children: "...Loading"
                            }, void 0, false, {
                                fileName: "[project]/app/user-management/page.js",
                                lineNumber: 253,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/user-management/page.js",
                            lineNumber: 252,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/user-management/page.js",
                        lineNumber: 199,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/user-management/page.js",
                lineNumber: 173,
                columnNumber: 7
            }, this),
            !isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$Pagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                setPageSize: setPageSize,
                setCurrentPage: setCurrentPage,
                getListing: getMembersFunc,
                currentPage: currentPage,
                totalPages: totalPages,
                totalItems: totalItems,
                pageSize: pageSize
            }, void 0, false, {
                fileName: "[project]/app/user-management/page.js",
                lineNumber: 263,
                columnNumber: 22
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/user-management/page.js",
        lineNumber: 127,
        columnNumber: 5
    }, this);
}
_s(Member, "ngziIRDY/iA2kEYTuaZ0+WvgXWs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = Member;
const __TURBOPACK__default__export__ = Member;
var _c;
__turbopack_context__.k.register(_c, "Member");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_07bc4ec2._.js.map