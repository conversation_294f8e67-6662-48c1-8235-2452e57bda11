(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/categories/categories.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "gradientAllRoundBorder": "categories-module-scss-module__Pg8QXG__gradientAllRoundBorder",
  "membershipContainer": "categories-module-scss-module__Pg8QXG__membershipContainer",
  "shimmer": "categories-module-scss-module__Pg8QXG__shimmer",
  "skeleton-shimmer": "categories-module-scss-module__Pg8QXG__skeleton-shimmer",
});
}}),
"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
// import history from "./history";
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    console.log("error", error);
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    // Only log out on true 401 Unauthorized from response
    if (responseStatus === 401) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeToken"])();
        if ("object" !== 'undefined' && window.location) {
            window.location.href = "/login";
        }
        return;
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.messages)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        console.log("error log is", error);
        if (Array.isArray(error?.response?.data?.message) || Array?.isArray(error?.data?.message)) error?.response?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if (localStorage.getItem("token")) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if (localStorage.getItem("token")) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://api.rebookitclub.com");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    // const token = localStorage.getItem("auth");
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/category.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addCategory": (()=>addCategory),
    "addSubCategory": (()=>addSubCategory),
    "addSubSubCategory": (()=>addSubSubCategory),
    "addSubSubSubCategory": (()=>addSubSubSubCategory),
    "deleteSubCategory": (()=>deleteSubCategory),
    "deleteSubSubCategory": (()=>deleteSubSubCategory),
    "deleteSubSubSubCategory": (()=>deleteSubSubSubCategory),
    "editSubCategory": (()=>editSubCategory),
    "editSubSubCategory": (()=>editSubSubCategory),
    "editSubSubSubCategory": (()=>editSubSubSubCategory),
    "getCategories": (()=>getCategories),
    "getSubCategories": (()=>getSubCategories),
    "getSubSubCategories": (()=>getSubSubCategories),
    "getSubSubSubCategories": (()=>getSubSubSubCategories),
    "uploadFile": (()=>uploadFile)
});
const { axiosErrorHandler } = __turbopack_context__.r("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
const instance = __turbopack_context__.r("[project]/app/service/axios.js [app-client] (ecmascript)");
let uri = {
    //category
    addCategory: "/master/category",
    //sub category
    addSubCategory: "/master/subCategory",
    getSubCategories: "/master/sub-category",
    editSubCategory: "/master/sub-category",
    deleteSubCategory: "/master/sub-category",
    //sub sub category
    addSubSubCategory: "/master/subSubCategory",
    getSubSubCategory: "/master/sub-sub-category",
    editSubSubCategory: "/master/sub-sub-category",
    deleteSubSubCategory: "/master/sub-sub-category",
    //sub sub sub category
    addSubSubSubCategory: "/master/subsubsubCategory",
    getSubSubSubCategory: "/master/sub-sub-sub-category",
    editSubSubSubCategory: "/master/sub-sub-sub-category",
    deleteSubSubSubCategory: "/master/sub-sub-sub-category",
    //file upload
    upload: `/admin/single-upload`
};
const addCategory = async (data)=>{
    let response = await instance.post(uri.addCategory, data).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const getCategories = async ()=>{
    let response = await instance.get(uri.addCategory).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const addSubCategory = async (data)=>{
    let response = await instance.post(uri.addSubCategory, data).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const getSubCategories = async (id)=>{
    let response = await instance.get(uri.getSubCategories + `/${id}`).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const addSubSubCategory = async (data)=>{
    let response = await instance.post(uri.addSubSubCategory, data).catch(axiosErrorHandler);
    return response;
};
const getSubSubCategories = async (id)=>{
    let response = await instance.get(uri.getSubSubCategory + `/${id}`).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const addSubSubSubCategory = async (data)=>{
    let response = await instance.post(uri.addSubSubSubCategory, data).catch(axiosErrorHandler);
    return response;
};
const getSubSubSubCategories = async (id)=>{
    let response = await instance.get(uri.getSubSubSubCategory + `/${id}`).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const uploadFile = async (data)=>{
    let response = await instance.post(uri.upload, data).catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response;
};
const editSubCategory = async (id, data)=>{
    let response = await instance.put(`${uri.getSubCategories}/${id}`, data).catch(axiosErrorHandler);
    return response;
};
const deleteSubCategory = async (id)=>{
    let response = await instance.delete(`${uri.getSubCategories}/${id}`).catch(axiosErrorHandler);
    return response;
};
const editSubSubCategory = async (id, data)=>{
    let response = await instance.put(`${uri.getSubSubCategory}/${id}`, data).catch(axiosErrorHandler);
    return response;
};
const deleteSubSubCategory = async (id)=>{
    let response = await instance.delete(`${uri.getSubSubCategory}/${id}`).catch(axiosErrorHandler);
    return response;
};
const editSubSubSubCategory = async (id, data)=>{
    let response = await instance.put(`${uri.getSubSubSubCategory}/${id}`, data).catch(axiosErrorHandler);
    return response;
};
const deleteSubSubSubCategory = async (id)=>{
    let response = await instance.delete(`${uri.getSubSubSubCategory}/${id}`).catch(axiosErrorHandler);
    return response;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/public/landing/bookCategory.png (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/bookCategory.5ef3a1b5.png");}}),
"[project]/public/landing/bookCategory.png.mjs { IMAGE => \"[project]/public/landing/bookCategory.png (static in ecmascript)\" } [app-client] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$bookCategory$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/landing/bookCategory.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$bookCategory$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 167,
    height: 173,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABE0lEQVR42gEIAff+AFgNDW3JLRv2yy4b+MolHfjJGx34yiEe+MUeHfFFDAxUAF0PDXLPKhz9zzcj/8lDOP/HPj7/2nIs/9NbJflLDw1aAJtBGK3KHh3/xjAt/8c6M//MMCn/1Ecj/9JBIf6UShmgAL+RNvKQXDj/mGU1/6R6QP+7iEf/wZFZ/8WYWv/brDvqAKSgTvlmhGj/bnpK/0R5d/9pk3//RI+W/z+Ol/+sslzxAHuCTMaBiFX/jXo2/01/ev92lmv/PmZi/0xsbv9yilW8ABQvLkhzekzdlHo6/0GIhv9WjXj/MmFg/z5NUNkULClAAAECAgM0KQ9FkWgbwLqLKfWei0P0RmpquxYXGEABBAUDTaR9MTUgkHMAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 8
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/categories/Tabs.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Tabs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$categories$2f$categories$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/categories/categories.module.scss.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/slices/storeSlice.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/service/category.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$bookCategory$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$landing$2f$bookCategory$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/landing/bookCategory.png.mjs { IMAGE => "[project]/public/landing/bookCategory.png (static in ecmascript)" } [app-client] (structured image object, ecmascript)');
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
function Tabs() {
    _s();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const [isLoading, setisLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [categories, setcategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // const [isLoading, setisLoading] = useState(false);
    const storeData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "Tabs.useSelector[storeData]": (state)=>state.storeData
    }["Tabs.useSelector[storeData]"]);
    // Add this to preserve category ID in URL
    const getTabUrl = (key)=>{
        const categoryId = searchParams.get("categoryId");
        return categoryId ? `/categories/${key}?categoryId=${categoryId}` : `/categories/${key}`;
    };
    const routeMap = {
        // category: "Category",
        subcategory: "SubCategory",
        sub_subcategory: "SubSubCategory",
        sub_sub_subcategory: "SubSubSubCategory"
    };
    console.log("storeData", storeData);
    console.log("location", window.location.pathname.split("/"));
    let allnestPast = window.location.pathname.split("/");
    const tabs = Object.entries(routeMap);
    console.log("tabs", tabs);
    const activeIndex = storeData.categoryTab;
    console.log("activeIndex", activeIndex);
    let pathName = allnestPast[allnestPast.length];
    const getCategoriesFunc = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Tabs.useCallback[getCategoriesFunc]": async ()=>{
            setisLoading(true);
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategories"])();
            console.log("response getCategories", response);
            if (response.status == 200) {
                setcategories(response.data.categories);
            }
            setisLoading(false);
        }
    }["Tabs.useCallback[getCategoriesFunc]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Tabs.useEffect": ()=>{
            getCategoriesFunc();
        }
    }["Tabs.useEffect"], [
        activeIndex
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Tabs.useEffect": ()=>{
            const defaultPath = `/categories/${tabs[0][0]}`;
            // router.asPath is not available in next/navigation; use window.location.pathname
            const currentPath = window.location.pathname;
            if (activeIndex === 0 && currentPath !== defaultPath) {
                dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["changeCategoryTab"])(0));
                router.push(`/categories/${tabs[0][0]}`);
            }
        }
    }["Tabs.useEffect"], [
        activeIndex,
        dispatch,
        router,
        tabs
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-xl font-semibold mb-5",
                        children: "Select Main Category"
                    }, void 0, false, {
                        fileName: "[project]/app/categories/Tabs.js",
                        lineNumber: 78,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex mb-6  gap-5 min-h-[200px]",
                        children: !isLoading ? categories?.map((item, index)=>{
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `cursor-pointer flex flex-col px-[35px] py-[15px] rounded-lg gap-4 w-[180px] min-h-[140px]  border ${storeData.categoriesState?.categoryId == item._id ? "border-gray-500" : "border-gray-200"}    items-center justify-start`,
                                onClick: ()=>{
                                    // router.push(`/categories/subcategory?categoryId=${item._id}`)
                                    dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["changeCategoryState"])(item._id));
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-gray-200 rounded-full w-[100px] p-5 h-[100px]",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            // src={item.image}
                                            src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$landing$2f$bookCategory$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$landing$2f$bookCategory$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$client$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"].src,
                                            alt: "categories_image",
                                            className: "rounded-full w-full h-full object-cover"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/Tabs.js",
                                            lineNumber: 92,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/categories/Tabs.js",
                                        lineNumber: 91,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "w-[90%] text-center break-after-all",
                                        children: item.name
                                    }, void 0, false, {
                                        fileName: "[project]/app/categories/Tabs.js",
                                        lineNumber: 99,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/app/categories/Tabs.js",
                                lineNumber: 82,
                                columnNumber: 15
                            }, this);
                        }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `w-[200px] h-[200px] rounded-md ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$categories$2f$categories$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"]['skeleton-shimmer']}`
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/Tabs.js",
                                    lineNumber: 106,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `w-[200px] h-[200px] rounded-md ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$categories$2f$categories$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"]['skeleton-shimmer']}`
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/Tabs.js",
                                    lineNumber: 107,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/categories/Tabs.js",
                            lineNumber: 105,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/categories/Tabs.js",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/categories/Tabs.js",
                lineNumber: 76,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `bg-white min-h-full h-full rounded-2xl p-5 ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$categories$2f$categories$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].membershipContainer}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative flex justify-around items-center py-2 px-3 rounded-[5px] border border-[#F3F3F3] gap-2 h-[64px] overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute h-[36px] rounded-[8px] bg-gradient-to-r from-[#0161AB] to-[#211F54] transition-all duration-400 ease-in-out",
                            style: {
                                width: `calc(100% / ${tabs.length})`,
                                left: `calc(${activeIndex} * 100% / ${tabs.length})`,
                                top: "50%",
                                transform: "translateY(-50%)",
                                zIndex: 10
                            }
                        }, void 0, false, {
                            fileName: "[project]/app/categories/Tabs.js",
                            lineNumber: 118,
                            columnNumber: 11
                        }, this),
                        tabs.map(([key, label], index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: `py-1  px-1.5 text-sm leading-[29px] text-center w-1/1 cursor-pointer z-20 transition-colors duration-300 ${activeIndex === index ? "text-white font-semibold" : "text-[#444]"}`,
                                onClick: ()=>{
                                    dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["changeCategoryTab"])(index));
                                    router.push(`/categories/${key}`);
                                },
                                children: label
                            }, key, false, {
                                fileName: "[project]/app/categories/Tabs.js",
                                lineNumber: 131,
                                columnNumber: 13
                            }, this))
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/categories/Tabs.js",
                    lineNumber: 116,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/categories/Tabs.js",
                lineNumber: 113,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(Tabs, "YGNa8B3qZdVo9vG7OrHybY7jV7I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDispatch"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"]
    ];
});
_c = Tabs;
var _c;
__turbopack_context__.k.register(_c, "Tabs");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/SubmitButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SubmitButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
function SubmitButton({ isLoading, InnerDiv, type, btnAction }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        type: type,
        onClick: btnAction,
        className: "mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5",
        children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-4 h-4 animate-spin text-white",
                    fill: "none",
                    viewBox: "0 0 24 24",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            className: "opacity-25",
                            cx: "12",
                            cy: "12",
                            r: "10",
                            stroke: "currentColor",
                            strokeWidth: "4"
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/SubmitButton.js",
                            lineNumber: 12,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            className: "opacity-75",
                            fill: "currentColor",
                            d: "M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/SubmitButton.js",
                            lineNumber: 20,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/common/SubmitButton.js",
                    lineNumber: 7,
                    columnNumber: 17
                }, this),
                "Loading..."
            ]
        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(InnerDiv, {}, void 0, false, {
            fileName: "[project]/app/components/common/SubmitButton.js",
            lineNumber: 27,
            columnNumber: 19
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/common/SubmitButton.js",
        lineNumber: 5,
        columnNumber: 6
    }, this);
}
_c = SubmitButton;
var _c;
__turbopack_context__.k.register(_c, "SubmitButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/categories/sub_sub_subcategory/page.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$categories$2f$Tabs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/categories/Tabs.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/service/category.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/ri/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$SubmitButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/SubmitButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$redux$2f$slices$2f$storeSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/redux/slices/storeSlice.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
const SubSubSubCategoryPage = ()=>{
    _s();
    const storeData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"])({
        "SubSubSubCategoryPage.useSelector[storeData]": (state)=>state.storeData
    }["SubSubSubCategoryPage.useSelector[storeData]"]);
    const [subSubSubCategories, setSubSubSubCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [subSubCategories, setSubSubCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedCategory, setselectedCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(storeData.categoriesState.categoryId || "");
    const [subCategories, setsubCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [categories, setcategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedSubCategory, setselectedSubCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedSubSubCategory, setSelectedSubSubCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [submitIsLoading, setSubmitIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [selectedImage, setSelectedImage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [enteredName, setEnteredName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [selectedImageOnUpload, setSelectedImageOnUpload] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showModal, setShowModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [subCategoryValue, setSubCategoryValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [editingId, setEditingId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [confirmDeleteId, setConfirmDeleteId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // const fetchSubSubCategories = async () => {
    //   try {
    //     const response = await getSubSubCategories();
    //     if (response.status === 200) {
    //       setSubSubCategories(response.data.subSubCategories || []);
    //       if (
    //         response.data.subSubCategories.length > 0 &&
    //         !selectedSubSubCategory
    //       ) {
    //         setSelectedSubSubCategory(response.data.subSubCategories[0]._id);
    //       }
    //     }
    //   } catch (error) {
    //     console.error("Error fetching sub-sub-categories:", error);
    //     toast.error("Failed to load sub-sub-categories");
    //   }
    // };
    const fetchSubCategories = async (id)=>{
        let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSubCategories"])(id);
        if (response.status == 200) {
            response.data.subCategories;
            setsubCategories(response.data.subCategories);
        }
    };
    const fetchSubSubCategories = async (id)=>{
        try {
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSubSubCategories"])(id);
            if (response.status == 200) {
                setSubSubCategories(response.data.subSubCategories);
            }
        } catch (err) {
            console.log("err", err);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubSubSubCategoryPage.useEffect": ()=>{
            if (selectedCategory.length) {
                fetchSubCategories(selectedCategory);
            }
        }
    }["SubSubSubCategoryPage.useEffect"], [
        selectedCategory
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubSubSubCategoryPage.useEffect": ()=>{
            if (selectedSubCategory.length) {
                fetchSubSubCategories(selectedSubCategory);
            }
        }
    }["SubSubSubCategoryPage.useEffect"], [
        selectedSubCategory
    ]);
    console.log("category", categories);
    console.log("subCategory", subCategories);
    console.log("subSubCategory", subSubCategories);
    console.log("subSubSubCategory", subSubSubCategories);
    console.log("selectedCatrgory", selectedCategory);
    console.log("selectedSubCategory", selectedSubCategory);
    console.log("selectedSubCategory", selectedSubCategory);
    const fetchAllData = async (id)=>{
        setIsLoading(true);
        try {
            // Fetch all categories
            const categoriesRes = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSubSubSubCategories"])(id);
            if (categoriesRes.status !== 200) return;
            const categories = categoriesRes.data?.subSubSubCategories;
            setSubSubSubCategories(categories);
        // Process all levels
        // setSubSubSubCategories(allData.flat());
        } catch (error) {
            console.error("Error fetching data:", error);
        } finally{
            setIsLoading(false);
        }
    };
    console.log("subSubSubCategories", subSubSubCategories);
    const submitSubSubSubCategory = async ()=>{
        setSubmitIsLoading(true);
        try {
            // Upload image first if exists
            let imageUrl = "";
            if (selectedImageOnUpload) {
                const formData = new FormData();
                formData.append("file", selectedImageOnUpload);
                const fileResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["uploadFile"])(formData);
                imageUrl = fileResponse.data.url;
            }
            // Create payload
            const payload = {
                name: enteredName,
                image: imageUrl,
                subSubCategoryId: selectedSubSubCategory
            };
            // Add sub-sub-sub-category
            const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addSubSubSubCategory"])(payload);
            if (response.status === 200) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Sub-Sub-Sub-Category Added Successfully");
                // fetchAllData(); // Refresh data
                // Close modal and reset form
                document.getElementById("subSubSubModal").classList.add("hidden");
                setEnteredName("");
                setSelectedImage(null);
                setSelectedImageOnUpload(null);
                fetchAllData(response.data.subSubCategoryId);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to add sub-sub-sub-category");
            }
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Error adding sub-sub-sub-category");
            console.error("Error adding sub-sub-sub-category:", error);
        } finally{
            setSubmitIsLoading(false);
        }
    };
    const InnerDiv = ()=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "px-4",
            children: "Submit"
        }, void 0, false, {
            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
            lineNumber: 162,
            columnNumber: 12
        }, this);
    };
    const handleButtonClick = ()=>{
        fileInputRef.current.click();
    };
    const handleFileChange = (e)=>{
        const file = e.target.files[0];
        if (file) {
            setSelectedImageOnUpload(file);
            const reader = new FileReader();
            reader.onloadend = ()=>{
                setSelectedImage(reader.result);
            };
            reader.readAsDataURL(file);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubSubSubCategoryPage.useEffect": ()=>{
        // fetchSubSubCategories();
        // fetchAllData();
        }
    }["SubSubSubCategoryPage.useEffect"], []);
    const getCategoriesFunc = async ()=>{
        setIsLoading(true);
        let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategories"])();
        console.log("response getCategories", response);
        if (response.status == 200) {
            // setcategories(response.data.categories)
            let list = response.data?.categories;
            setcategories(list);
            // fetchSubCategory(list[0]._id)
            // setselectedSubCategory(list[0]._id)
            if (list.length > 0) {
            // dispatch(changeCategoryState(list[0]._id))
            // getSubCategory(list[0]._id);
            }
        }
    // setIsLoading(false);
    };
    const fetchSubSubCategory = async (id)=>{
        try {
            // setIsLoading(true)
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSubSubCategories"])(id);
            if (response.status == 200) {
                let list = response.data.subSubCategories || [];
                setSubSubCategories(list);
                if (list.length) {
                    fetchAllData(list[0]._id);
                    setSelectedSubSubCategory(list[0]._id);
                }
            // setSubSubSubCategories(response.data.subSubSubCategories)
            }
            setIsLoading(false);
        } catch (err) {
            setIsLoading(false);
        }
    };
    console.log(subSubCategories, "subSubCategories");
    const fetchSubCategory = async (id)=>{
        try {
            setIsLoading(true);
            let response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSubCategories"])(id);
            if (response.status == 200) {
                // response.data.categories
                let list = response.data?.subCategories || [];
                setsubCategories(list);
                if (list.length > 0) {
                    setselectedSubCategory(list[0]._id);
                // fetchSubSubCategory(list[0]._id)
                }
                setIsLoading(true);
            }
        } catch (err) {
            console.log("Error in fetchin");
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubSubSubCategoryPage.useEffect": ()=>{
            getCategoriesFunc();
        }
    }["SubSubSubCategoryPage.useEffect"], [
        storeData.categoriesState.categoryId
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubSubSubCategoryPage.useEffect": ()=>{
            if (storeData.categoriesState.categoryId) {
                fetchSubCategory(storeData.categoriesState.categoryId);
            }
        }
    }["SubSubSubCategoryPage.useEffect"], [
        storeData.categoriesState.categoryId
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubSubSubCategoryPage.useEffect": ()=>{
            if (selectedSubCategory.length) {
                fetchSubSubCategory(selectedSubCategory);
            }
        }
    }["SubSubSubCategoryPage.useEffect"], [
        selectedSubCategory
    ]);
    const handleEdit = async ()=>{
        const payload = {
            name: subCategoryValue
        };
        const data = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["editSubSubSubCategory"])(editingId, payload);
        if (data.status == 200) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Edited Successfully");
            // getCategories();
            await fetchSubSubCategory(selectedSubCategory);
            setShowModal(false);
            setEditingId(null);
            setSubCategoryValue("");
        }
    };
    const handleDelete = async (id)=>{
        const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$category$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteSubSubSubCategory"])(id);
        if (res.status == 200) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Deleted Successfully");
            // getCategories();
            await fetchSubSubCategory(selectedSubCategory);
            setConfirmDeleteId(null);
        }
    };
    const confirmDelete = (id)=>{
        console.log("id to delete", id);
        setConfirmDeleteId(id); // Set the ID to confirm deletion
    };
    const cancelDelete = ()=>{
        setConfirmDeleteId(null); // Reset confirmation
    };
    const openModalForEdit = (item)=>{
        setEditingId(item._id);
        setSubCategoryValue(item.name);
        setShowModal(true);
    };
    const closeModal = ()=>{
        setShowModal(false);
        setEditingId(null);
        setSubCategoryValue("");
    };
    console.log("selectedCategory", selectedCategory);
    console.log("selectedSubSubCategory", selectedSubSubCategory);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$categories$2f$Tabs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                lineNumber: 311,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between my-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "",
                        children: !isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "min-h-[50px]",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    defaultValue: storeData.categoriesState.categoryId,
                                    className: "ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1",
                                    children: categories?.map((item)=>{
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: item._id,
                                            children: item.name
                                        }, item._id, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 325,
                                            columnNumber: 26
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 316,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    className: "ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1",
                                    onChange: (e)=>setselectedSubCategory(e.target.value),
                                    children: subCategories?.map((item)=>{
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: item._id,
                                            children: item.name
                                        }, item._id, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 333,
                                            columnNumber: 26
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 328,
                                    columnNumber: 15
                                }, this),
                                subSubCategories.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    className: "ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1",
                                    onChange: (e)=>setSelectedSubSubCategory(e.target.value),
                                    children: subSubCategories?.map((item)=>{
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: item._id,
                                            children: item.name
                                        }, item._id, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 342,
                                            columnNumber: 28
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 337,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 315,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: "...Loading"
                        }, void 0, false, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 348,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                        lineNumber: 313,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>document.getElementById("subSubSubModal").classList.remove("hidden"),
                        className: "bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white px-5 py-2 rounded-full",
                        children: "Create New Sub-Sub-Sub-Category"
                    }, void 0, false, {
                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                        lineNumber: 351,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                lineNumber: 312,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                className: "w-full border border-[#EFF1F4] rounded-lg border-separate",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                        className: "border-b border-[#EFF1F4]",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]",
                                    children: "Name"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 362,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]",
                                    children: "Created On"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 364,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                    className: "text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]",
                                    children: " Action"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 368,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 361,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                        lineNumber: 360,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                        className: "w-full",
                        children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                colSpan: 6,
                                className: "text-center py-4",
                                children: "Loading..."
                            }, void 0, false, {
                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                lineNumber: 374,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 373,
                            columnNumber: 13
                        }, this) : subSubSubCategories?.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                colSpan: 6,
                                className: "text-center py-4",
                                children: "No data found"
                            }, void 0, false, {
                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                lineNumber: 380,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 379,
                            columnNumber: 13
                        }, this) : subSubSubCategories.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                className: "py-[10px] bg-white px-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "border-b border-[#EFF1F4] px-[10px] py-[10px] bg-white flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                        className: "ml-2 w-[40px] h-[40px] rounded-full",
                                                        src: item.image || "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/952747fd-94f3-4f7f-ba93-eaf188f302aa.png"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                        lineNumber: 389,
                                                        columnNumber: 21
                                                    }, this),
                                                    " "
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                lineNumber: 388,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "ml-2",
                                                children: item?.name
                                            }, void 0, false, {
                                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                lineNumber: 394,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                        lineNumber: 387,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "bg-white border-b border-[#EFF1F4]",
                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(item.createdAt).format("DD-MM-YYYY")
                                    }, void 0, false, {
                                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                        lineNumber: 405,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "bg-white border-b border-[#EFF1F4] w-2/12",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "px-3 py-2 rounded-lg border-blue-400 border-2 mr-2",
                                                onClick: ()=>openModalForEdit(item),
                                                children: "edit"
                                            }, void 0, false, {
                                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                lineNumber: 410,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "px-2 py-2 rounded-lg bg-red-200 border-red-400 border-2 ml-2",
                                                onClick: ()=>confirmDelete(item._id),
                                                children: "delete"
                                            }, void 0, false, {
                                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                lineNumber: 413,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                        lineNumber: 409,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, item._id, true, {
                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                lineNumber: 386,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                        lineNumber: 371,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                lineNumber: 359,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                id: "subSubSubModal",
                className: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg w-full max-w-lg shadow-lg relative p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute top-4 right-4 cursor-pointer",
                            onClick: ()=>{
                                document.getElementById("subSubSubModal").classList.add("hidden");
                                setEnteredName("");
                                setSelectedImage(null);
                                setSelectedImageOnUpload(null);
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "text-gray-500 hover:text-red-600 text-2xl",
                                children: "×"
                            }, void 0, false, {
                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                lineNumber: 435,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 426,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold mb-4 text-center border-b pb-2",
                            children: "Create Sub-Sub-Sub-Category"
                        }, void 0, false, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 438,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block mb-1",
                                            children: "Name"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 442,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            placeholder: "Enter name",
                                            type: "text",
                                            value: enteredName,
                                            onChange: (e)=>setEnteredName(e.target.value),
                                            className: "w-full px-3 py-2 border rounded-md"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 443,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 441,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block mb-1",
                                            children: "Category"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 453,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            value: storeData.categoriesState.categoryId || selectedCategory,
                                            onChange: (e)=>setselectedCategory(e.target.value),
                                            className: "w-full px-3 py-2 border rounded-md",
                                            children: categories.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: item._id,
                                                    children: item.name
                                                }, item._id, false, {
                                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                    lineNumber: 460,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 454,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 452,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block mb-1",
                                            children: "Sub-Category"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 467,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            value: selectedSubSubCategory || "",
                                            onChange: (e)=>setselectedSubCategory(e.target.value),
                                            className: "w-full px-3 py-2 border rounded-md",
                                            children: subCategories.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: item._id,
                                                    children: item.name
                                                }, item._id, false, {
                                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                    lineNumber: 474,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 468,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 466,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block mb-1",
                                            children: "Sub-Sub-Category"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 481,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            value: selectedSubSubCategory || "",
                                            onChange: (e)=>setSelectedSubSubCategory(e.target.value),
                                            className: "w-full px-3 py-2 border rounded-md",
                                            children: subSubCategories.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: item._id,
                                                    children: item.name
                                                }, item._id, false, {
                                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                    lineNumber: 488,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 482,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 480,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            ref: fileInputRef,
                                            type: "file",
                                            className: "hidden",
                                            onChange: handleFileChange
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 496,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: handleButtonClick,
                                            className: "w-full py-2 px-3 flex items-center justify-center bg-gray-100 text-gray-700 rounded-md",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RiFileUploadLine"], {
                                                    className: "mr-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                    lineNumber: 501,
                                                    columnNumber: 17
                                                }, this),
                                                "Upload Image"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 497,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 495,
                                    columnNumber: 13
                                }, this),
                                selectedImage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative mt-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>{
                                                setSelectedImage(null);
                                                setSelectedImageOnUpload(null);
                                            },
                                            className: "absolute -top-3 -right-3 bg-white rounded-full w-6 h-6 flex items-center justify-center shadow-md",
                                            children: "×"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 508,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            src: selectedImage,
                                            className: "max-h-40 mx-auto rounded-md",
                                            alt: "Preview"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 517,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 507,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex justify-center pt-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$SubmitButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        isLoading: submitIsLoading,
                                        InnerDiv: InnerDiv,
                                        type: "button",
                                        btnAction: submitSubSubSubCategory,
                                        className: "w-40"
                                    }, void 0, false, {
                                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                        lineNumber: 522,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 521,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 440,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                    lineNumber: 425,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                lineNumber: 424,
                columnNumber: 7
            }, this),
            confirmDeleteId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg w-full max-w-lg shadow-lg relative p-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold",
                            children: "Confirm Deletion"
                        }, void 0, false, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 538,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: "Are you sure you want to delete this sub-category?"
                        }, void 0, false, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 539,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-end mt-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: cancelDelete,
                                    className: "mr-2 px-4 py-2 bg-gray-300 rounded",
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 541,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>handleDelete(confirmDeleteId),
                                    className: "px-4 py-2 bg-red-600 text-white rounded",
                                    children: "Delete"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 544,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 540,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                    lineNumber: 537,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                lineNumber: 536,
                columnNumber: 9
            }, this),
            showModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-lg w-full max-w-lg shadow-lg relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-between p-4 border-b",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-xl font-semibold",
                                    children: editingId ? "Edit Sub-Category" : "Create Sub-Category"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 558,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: closeModal,
                                    className: "text-gray-500 hover:text-red-600 text-xl font-bold",
                                    children: "×"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 559,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 557,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    children: "Name"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 564,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    placeholder: "Add Name",
                                    type: "text",
                                    value: subCategoryValue,
                                    onChange: (e)=>setSubCategoryValue(e.target.value),
                                    className: "px-2 py-2 rounded-md w-full border-[1px] border-[#dddddd] outline-none"
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 565,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "my-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        className: "mt-3 px-2 py-2 rounded-md w-full border-[1px] border-[#dddddd] outline-none",
                                        onChange: (e)=>setselectedCategory(e.target.value),
                                        children: categories.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: item._id,
                                                children: item.name
                                            }, item._id, false, {
                                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                lineNumber: 578,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                        lineNumber: 573,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 572,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    ref: fileInputRef,
                                    type: "file",
                                    className: "hidden",
                                    onChange: handleFileChange
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 584,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleButtonClick,
                                        className: "py-3 px-2 flex items-center bg-[#f4f4f4] text-[#8C8C8C] w-full",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RiFileUploadLine"], {}, void 0, false, {
                                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                                lineNumber: 587,
                                                columnNumber: 19
                                            }, this),
                                            " Upload Image"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                        lineNumber: 586,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 585,
                                    columnNumber: 15
                                }, this),
                                selectedImage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "my-3 relative w-fit",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setselectedImage(null),
                                            className: "absolute bg-[white] top-[-10px] rounded-full w-[20px] h-[20px] right-0 z-[100] text-gray-500 hover:text-red-600 text-xl font-bold",
                                            children: "×"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 592,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            src: selectedImage,
                                            className: "h-[100px]",
                                            alt: "image"
                                        }, void 0, false, {
                                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                            lineNumber: 598,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                    lineNumber: 591,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 563,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "my-2 flex justify-end mx-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$SubmitButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                isLoading: submitIsLoading,
                                InnerDiv: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "px-4",
                                        children: "Submit"
                                    }, void 0, false, {
                                        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                        lineNumber: 605,
                                        columnNumber: 33
                                    }, void 0),
                                type: "button",
                                btnAction: editingId ? handleEdit : submitQuestion
                            }, void 0, false, {
                                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                                lineNumber: 603,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                            lineNumber: 602,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                    lineNumber: 556,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
                lineNumber: 555,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/categories/sub_sub_subcategory/page.js",
        lineNumber: 310,
        columnNumber: 5
    }, this);
};
_s(SubSubSubCategoryPage, "soF2spbgtx29BYxbbcRr7kQs1rI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelector"]
    ];
});
_c = SubSubSubCategoryPage;
const __TURBOPACK__default__export__ = SubSubSubCategoryPage;
var _c;
__turbopack_context__.k.register(_c, "SubSubSubCategoryPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_406e5a07._.js.map