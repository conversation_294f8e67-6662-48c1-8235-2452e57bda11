/* [project]/app/signup/signup.module.scss.module.css [app-client] (css) */
.signup-module-scss-module__t2cgJW__signupContainer {
  padding: 20px;
  overflow-x: hidden;
}

.signup-module-scss-module__t2cgJW__signupContainer .signup-module-scss-module__t2cgJW__imageContainer {
  flex-shrink: 0;
  width: 100%;
  height: 358px;
  margin: 3vh auto;
}

.signup-module-scss-module__t2cgJW__signupContainer .signup-module-scss-module__t2cgJW__imageContainer .signup-module-scss-module__t2cgJW__imageElem {
  width: 369.656px;
  height: 358px;
}

.signup-module-scss-module__t2cgJW__signupContainer .signup-module-scss-module__t2cgJW__fields {
  background: #fff;
  border: .825px solid #79747e;
  border-radius: 3.301px;
  flex-shrink: 0;
  align-self: stretch;
  align-items: center;
  height: 70px;
  padding: 6.602px 0 6.602px 13.204px;
  display: flex;
}

.signup-module-scss-module__t2cgJW__signupContainer .signup-module-scss-module__t2cgJW__submitButton {
  background: var(--Linear, linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%));
  cursor: pointer;
  color: #fff;
  border-radius: 96.836px;
  justify-content: center;
  align-self: stretch;
  align-items: center;
  gap: 2.891px;
  width: 100%;
  height: 41px;
  padding: 5.781px 11.563px;
  display: flex;
}

@media (width >= 769px) {
  .signup-module-scss-module__t2cgJW__signupContainer {
    justify-content: space-between;
    margin: 40px 0;
    padding: 40px 100px;
    display: flex;
  }

  .signup-module-scss-module__t2cgJW__signupContainer .signup-module-scss-module__t2cgJW__imageContainer {
    flex-shrink: 0;
    width: 100%;
    height: 724px;
    margin: 5vh auto;
  }

  .signup-module-scss-module__t2cgJW__signupContainer .signup-module-scss-module__t2cgJW__imageContainer .signup-module-scss-module__t2cgJW__imageElem {
    height: 617px;
  }

  .signup-module-scss-module__t2cgJW__signupContainer .signup-module-scss-module__t2cgJW__fields {
    font-style: 14px;
  }

  .signup-module-scss-module__t2cgJW__signupContainer .signup-module-scss-module__t2cgJW__submitButton {
    background: var(--Linear, linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%));
    border-radius: 134px;
    justify-content: center;
    align-self: stretch;
    align-items: center;
    gap: 4px;
    height: 62px;
    padding: 8px 16px;
    display: flex;
  }
}


/*# sourceMappingURL=app_signup_signup_module_scss_module_3b2a6491.css.map*/