(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
// import history from "./history";
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    console.log("error", error);
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    // Only log out on true 401 Unauthorized from response
    if (responseStatus === 401) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeToken"])();
        if ("object" !== 'undefined' && window.location) {
            window.location.href = "/login";
        }
        return;
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.messages)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        console.log("error log is", error);
        if (Array.isArray(error?.response?.data?.message) || Array?.isArray(error?.data?.message)) error?.response?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if (localStorage.getItem("token")) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if (localStorage.getItem("token")) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://api.rebookitclub.com");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    // const token = localStorage.getItem("auth");
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/adManagement.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdPricingRule": (()=>createAdPricingRule),
    "createIndividualPricingRuleInResource": (()=>createIndividualPricingRuleInResource),
    "createOverridePricingRuleInResource": (()=>createOverridePricingRuleInResource),
    "deleteIndividualRule": (()=>deleteIndividualRule),
    "deleteOverrightRuleDate": (()=>deleteOverrightRuleDate),
    "deleteResource": (()=>deleteResource),
    "delete_AddRule": (()=>delete_AddRule),
    "fetchAdPricingRules": (()=>fetchAdPricingRules),
    "getAdPricingRuleById": (()=>getAdPricingRuleById),
    "updateBasePrice": (()=>updateBasePrice),
    "updateIndividualRule": (()=>updateIndividualRule)
});
const { axiosErrorHandler } = __turbopack_context__.r("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
const instance = __turbopack_context__.r("[project]/app/service/axios.js [app-client] (ecmascript)");
let uri = {
    //create
    createAdPricingRule: "/ad-management",
    createIndividualPricingRuleInResource: "/ad-management/priceRule",
    createOverridePricingRuleInResource: "/ad-management/overrideRule",
    //get
    fetchAdPricingRules: "/ad-management",
    getAdPricingRule_byId: "/ad-management",
    // update
    updateBasePrice: "/ad-management/basePrice",
    updateIndividualRule: "/ad-management/priceRule",
    // delete
    deleteResource: "/ad-management",
    delete_AddRule: "/ad-management",
    deleteIndividualRule: "/ad-management/priceRule",
    deleteOverrightRuleDate: "/ad-management/overrideRule"
};
const createAdPricingRule = async (data)=>{
    let response = await instance.post(uri.createAdPricingRule, data).catch(axiosErrorHandler);
    return response;
};
const createIndividualPricingRuleInResource = async (data)=>{
    let response = await instance.post(uri.createIndividualPricingRuleInResource, data).catch(axiosErrorHandler);
    return response;
};
const createOverridePricingRuleInResource = async (data)=>{
    let response = await instance.post(uri.createOverridePricingRuleInResource, data).catch(axiosErrorHandler);
    return response;
};
const fetchAdPricingRules = async ()=>{
    let response = await instance.get(uri.fetchAdPricingRules).catch(axiosErrorHandler);
    return response;
};
const getAdPricingRuleById = async (id)=>{
    let response = await instance.get(`${uri.getAdPricingRule_byId}/${id}`).catch(axiosErrorHandler);
    return response;
};
const updateBasePrice = async (data)=>{
    let response = await instance.put(`${uri.updateBasePrice}`, data).catch(axiosErrorHandler);
    return response;
};
const updateIndividualRule = async (data)=>{
    let response = await instance.put(`${uri.updateIndividualRule}`, data).catch(axiosErrorHandler);
    return response;
};
const deleteResource = async (id)=>{
    let response = await instance.delete(`${uri.deleteResource}/${id}`).catch(axiosErrorHandler);
    return response;
};
const delete_AddRule = async (id, data)=>{
    let response = await instance.delete(`${uri.delete_AddRule}/${id}`, data).catch(axiosErrorHandler);
    return response;
};
const deleteIndividualRule = async (data)=>{
    let response = await instance.delete(`${uri.deleteIndividualRule}`, {
        data
    }).catch(axiosErrorHandler);
    return response;
};
const deleteOverrightRuleDate = async (data)=>{
    let response = await instance.delete(`${uri.deleteOverrightRuleDate}`, {
        data
    }).catch(axiosErrorHandler);
    return response;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/CalendarRuleView.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CalendarRuleView)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
const weekDays = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
];
const formatJMD = (value)=>{
    if (value === undefined || value === null || value === '') return '';
    return 'J$' + Number(value).toLocaleString('en-JM');
};
function getDaysInMonth(year, month) {
    return new Date(year, month + 1, 0).getDate();
}
function getFirstDayIndex(year, month) {
    // Monday as first day (0=Monday, 6=Sunday)
    const jsDay = new Date(year, month, 1).getDay();
    return jsDay === 0 ? 6 : jsDay - 1;
}
function getCalendarMatrix(year, month) {
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayIdx = getFirstDayIndex(year, month);
    const matrix = [];
    let day = 1 - firstDayIdx;
    for(let row = 0; row < 6; row++){
        const week = [];
        for(let col = 0; col < 7; col++){
            week.push(day > 0 && day <= daysInMonth ? day : '');
            day++;
        }
        matrix.push(week);
    }
    return matrix;
}
function normalizeDate(date) {
    if (!date) return '';
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) return date;
    const d = new Date(date);
    return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
}
function getRuleForDateDefault(rules, year, month, day) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const matching = rules.filter((rule)=>{
        if (rule.isActive === false) return false;
        if (!rule.startDate || !rule.endDate) return false;
        const start = normalizeDate(rule.startDate);
        const end = normalizeDate(rule.endDate);
        return start <= dateStr && dateStr <= end;
    });
    if (matching.length === 0) return null;
    const selected = matching.reduce((a, b)=>{
        if (a.priority == null) return b;
        if (b.priority == null) return a;
        return Number(a.priority) > Number(b.priority) ? a : b;
    });
    return selected;
}
function formatPrice(currency, price) {
    if (currency === 'USD') {
        // Format as US$ 1,234.56
        if (price === undefined || price === null || price === '') return '';
        return 'US$' + Number(price).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    } else {
        // Default to JMD
        return formatJMD(price);
    }
}
function CalendarRuleView({ rules = [], basePrice = '', baseCurrency = 'JMD', dateOverrides = {}, onCellClick = ()=>{}, onRemoveOverride = ()=>{}, calendarMonth, calendarYear, setCalendarMonth, setCalendarYear, getRuleForDate }) {
    const calendar = getCalendarMatrix(calendarYear, calendarMonth);
    // Use custom getRuleForDate if provided, else default
    const ruleForDateFn = getRuleForDate || getRuleForDateDefault;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-lg shadow p-0 overflow-x-auto border border-gray-200",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
            className: "min-w-full text-sm font-[poppins]",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                        children: weekDays.map((day)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                className: "p-3 text-center font-semibold text-[#211F54] bg-[#F5F6FA] border-b border-gray-200",
                                children: day
                            }, day, false, {
                                fileName: "[project]/app/components/common/CalendarRuleView.js",
                                lineNumber: 103,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/components/common/CalendarRuleView.js",
                        lineNumber: 101,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/common/CalendarRuleView.js",
                    lineNumber: 100,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                    children: calendar.map((week, rowIdx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                            children: week.map((day, colIdx)=>{
                                if (!day) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {}, colIdx, false, {
                                    fileName: "[project]/app/components/common/CalendarRuleView.js",
                                    lineNumber: 116,
                                    columnNumber: 34
                                }, this);
                                let isDisabled = false;
                                const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                                const override = dateOverrides[dateStr];
                                if (override) {
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                        className: "p-0 text-center align-top ",
                                        style: {
                                            minWidth: 120,
                                            height: 90
                                        },
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col items-center justify-center h-full py-2 bg-gray-200 rounded-lg relative border",
                                            style: {
                                                border: 'none',
                                                position: 'relative'
                                            },
                                            onClick: ()=>!isDisabled && onCellClick(day),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "absolute truncate max-w-[100px] left-1 top-1 text-[10px] font-semibold px-1 rounded",
                                                    children: "override"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/common/CalendarRuleView.js",
                                                    lineNumber: 132,
                                                    columnNumber: 27
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-normal text-xl text-center",
                                                    children: String(day).padStart(2, '0')
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/common/CalendarRuleView.js",
                                                    lineNumber: 150,
                                                    columnNumber: 25
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2 mt-2",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs font-semibold",
                                                        style: {
                                                            color: '#888'
                                                        },
                                                        children: formatPrice(override.currency, override.price)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/common/CalendarRuleView.js",
                                                        lineNumber: 154,
                                                        columnNumber: 27
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/common/CalendarRuleView.js",
                                                    lineNumber: 153,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/common/CalendarRuleView.js",
                                            lineNumber: 127,
                                            columnNumber: 23
                                        }, this)
                                    }, colIdx, false, {
                                        fileName: "[project]/app/components/common/CalendarRuleView.js",
                                        lineNumber: 122,
                                        columnNumber: 21
                                    }, this);
                                }
                                const ruleForDay = ruleForDateFn(rules, calendarYear, calendarMonth, day);
                                const highlightStyle = ruleForDay ? {
                                    border: `1px solid ${ruleForDay.color}`,
                                    color: ruleForDay.color,
                                    fontWeight: 600
                                } : {};
                                const priceToShow = ruleForDay ? ruleForDay.price : basePrice;
                                const currencyToShow = ruleForDay ? ruleForDay.currency : baseCurrency;
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    className: "p-0 text-center align-top ",
                                    style: {
                                        minWidth: 120,
                                        height: 90
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-col items-center justify-center h-full py-2 border border-gray-100 rounded-lg relative",
                                        style: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : highlightStyle,
                                        onClick: ()=>!isDisabled && onCellClick(day),
                                        children: [
                                            ruleForDay && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "absolute truncate max-w-[100px] left-1 top-1 text-[10px] font-semibold px-1 rounded",
                                                style: {
                                                    background: '#fff',
                                                    color: ruleForDay.color,
                                                    border: `1px solid ${ruleForDay.color}`,
                                                    zIndex: 2
                                                },
                                                title: ruleForDay.name,
                                                children: ruleForDay.name
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/common/CalendarRuleView.js",
                                                lineNumber: 188,
                                                columnNumber: 25
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "font-normal text-xl text-center",
                                                children: String(day).padStart(2, '0')
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/common/CalendarRuleView.js",
                                                lineNumber: 196,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2 mt-2",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs font-semibold",
                                                    style: ruleForDay ? {
                                                        color: ruleForDay.color
                                                    } : {
                                                        color: '#888'
                                                    },
                                                    children: formatPrice(currencyToShow, priceToShow)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/common/CalendarRuleView.js",
                                                    lineNumber: 200,
                                                    columnNumber: 25
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/common/CalendarRuleView.js",
                                                lineNumber: 199,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/common/CalendarRuleView.js",
                                        lineNumber: 178,
                                        columnNumber: 21
                                    }, this)
                                }, colIdx, false, {
                                    fileName: "[project]/app/components/common/CalendarRuleView.js",
                                    lineNumber: 173,
                                    columnNumber: 19
                                }, this);
                            })
                        }, rowIdx, false, {
                            fileName: "[project]/app/components/common/CalendarRuleView.js",
                            lineNumber: 114,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/app/components/common/CalendarRuleView.js",
                    lineNumber: 112,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/common/CalendarRuleView.js",
            lineNumber: 99,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/common/CalendarRuleView.js",
        lineNumber: 98,
        columnNumber: 5
    }, this);
}
_c = CalendarRuleView;
var _c;
__turbopack_context__.k.register(_c, "CalendarRuleView");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ViewAdPlanPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/service/adManagement.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$CalendarRuleView$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/CalendarRuleView.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function formatDate(dateStr) {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
}
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    return d.toLocaleString('en-JM', {
        dateStyle: 'medium',
        timeStyle: 'short'
    });
}
const formatJMD = (value)=>{
    if (value === undefined || value === null || value === '') return '';
    return 'J$' + Number(value).toLocaleString('en-JM');
};
function ViewAdPlanPage() {
    _s();
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const [ad, setAd] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [calendarMonth, setCalendarMonth] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Date().getMonth());
    const [calendarYear, setCalendarYear] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Date().getFullYear());
    const [dateOverrides, setDateOverrides] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [rules, setRules] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    async function fetchData() {
        setLoading(true);
        setError(null);
        const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAdPricingRuleById"])(params['plan.id']);
        if (res && res.data && res.data.resource) {
            setAd(res.data.resource);
            if (res.data.resource.pricingRules) setRules(res.data.resource.pricingRules);
            setDateOverrides({});
            if (res.data.resource.overrideRules) {
                res.data.resource.overrideRules.forEach((override)=>{
                    const date = new Date(override.date);
                    setDateOverrides((prev)=>({
                            ...prev,
                            [`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`]: override
                        }));
                });
            }
        } else if (res && res.data && res.data.message) {
            setError(res.data.message);
        } else {
            setError('Something went wrong');
        }
        setLoading(false);
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ViewAdPlanPage.useEffect": ()=>{
            if (!params['plan.id']) return;
            fetchData();
        }
    }["ViewAdPlanPage.useEffect"], [
        params
    ]);
    // Find the most recent rule (excluding base price)
    const nonBaseRules = rules.filter((rule)=>rule.name?.toLowerCase() !== 'base price');
    const mostRecentRule = nonBaseRules.reduce((a, b)=>new Date(a.updatedAt) > new Date(b.updatedAt) ? a : b, nonBaseRules[0]);
    const mostRecentRuleId = mostRecentRule?._id;
    function getRuleForDateWithUpdatedAt(rules, year, month, day) {
        const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const matching = rules.filter((rule)=>{
            if (rule.isActive === false) return false;
            if (!rule.startDate || !rule.endDate) return false;
            const start = formatDate(rule.startDate);
            const end = formatDate(rule.endDate);
            return start <= dateStr && dateStr <= end;
        });
        if (matching.length === 0) return null;
        matching.sort((a, b)=>{
            if (Number(b.priority) !== Number(a.priority)) {
                return Number(b.priority) - Number(a.priority);
            }
            return new Date(b.updatedAt) - new Date(a.updatedAt);
        });
        return matching[0];
    }
    if (loading) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center justify-center min-h-[60vh]",
        children: "Loading..."
    }, void 0, false, {
        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
        lineNumber: 89,
        columnNumber: 23
    }, this);
    if (error) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center justify-center min-h-[60vh] text-red-500",
        children: error
    }, void 0, false, {
        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
        lineNumber: 90,
        columnNumber: 21
    }, this);
    if (!ad) return null;
    // Resource details for header
    const resourceType = ad.type ? ad.type.charAt(0).toUpperCase() + ad.type.slice(1) : '';
    const resourcePage = ad.page ? ad.page.charAt(0).toUpperCase() + ad.page.slice(1) : '';
    const resourcePosition = ad.position ? ad.position.charAt(0).toUpperCase() + ad.position.slice(1) : '';
    const resourceStatus = ad.isActive ? 'Active' : 'Inactive';
    const basePrice = rules.find((rule)=>rule.name?.toLowerCase() === 'base price')?.price;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-[#F5F6FA] min-h-screen w-full",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full bg-gradient-to-tr from-[#0161AB] to-[#211F54] py-10 px-6 flex flex-col md:flex-row items-center justify-between gap-2 shadow-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col gap-2 w-full",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-4 mb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        className: "w-10 h-10 flex items-center justify-center rounded-full bg-white border border-gray-200 shadow hover:bg-[#F3F8FC] transition-all duration-200 mr-2",
                                        style: {
                                            outline: "none"
                                        },
                                        onClick: ()=>{
                                            window.location.href = '/ad-management/manage-ad-pricing';
                                        // router.push(
                                        //   `/ad-management/manage-ad-pricing`
                                        // );
                                        },
                                        tabIndex: 0,
                                        "aria-label": "Back",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                            width: "22",
                                            height: "22",
                                            viewBox: "0 0 22 22",
                                            fill: "none",
                                            xmlns: "http://www.w3.org/2000/svg",
                                            className: "text-[#0161AB]",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                    cx: "11",
                                                    cy: "11",
                                                    r: "11",
                                                    fill: "#F5F8FE"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 127,
                                                    columnNumber: 15
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    d: "M13.5 16L8.5 11L13.5 6",
                                                    stroke: "#0161AB",
                                                    strokeWidth: "2",
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 128,
                                                    columnNumber: 15
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                            lineNumber: 119,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 106,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-4xl font-extrabold text-white tracking-tight drop-shadow",
                                        children: resourcePage
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 137,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                lineNumber: 105,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-4 mt-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: `px-4 py-2 rounded-full text-base font-semibold ${ad.type === 'grid' ? 'bg-blue-200 text-blue-900' : 'bg-purple-200 text-purple-900'}`,
                                        children: [
                                            "Type: ",
                                            resourceType
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 140,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "px-4 py-2 rounded-full text-base font-semibold bg-gray-200 text-gray-900",
                                        children: [
                                            "Position: ",
                                            resourcePosition
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 141,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: `px-4 py-2 rounded-full text-base font-semibold ${ad.isActive ? 'bg-green-200 text-green-900' : 'bg-gray-300 text-gray-600'}`,
                                        children: resourceStatus
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 142,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                lineNumber: 139,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                        lineNumber: 104,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-shrink-0 w-full md:w-auto flex justify-center",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "rounded-3xl shadow-2xl bg-white/30 backdrop-blur-md border border-white/40 px-16 py-10 flex flex-col items-center min-w-[260px] max-w-full",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-lg font-semibold text-white/90 mb-2 tracking-wide",
                                    children: "Base Price"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                    lineNumber: 148,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-5xl font-extrabold text-white drop-shadow-lg flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                            xmlns: "http://www.w3.org/2000/svg",
                                            width: "32",
                                            height: "32",
                                            fill: "none",
                                            viewBox: "0 0 24 24",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                fill: "#fff",
                                                d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17.93c-2.83.48-5.48-1.51-5.96-4.34-.09-.52.36-.99.89-.99.44 0 .***********.34 1.81 2.01 3.08 3.83 2.74 1.81-.34 3.08-2.01 2.74-3.83-.34-1.81-2.01-3.08-3.83-2.74-.52.09-.99-.36-.99-.89 0-.44.32-.81.75-.89 2.83-.48 5.48 1.51 5.96 **********-.36.99-.89.99-.44 0-.81-.32-.89-.75-.34-1.81-2.01-3.08-3.83-2.74-1.81.34-3.08 2.01-2.74 3.83.34 1.81 2.01 3.08 3.83 2.74.52-.***********.89 0 .44-.32.81-.75.89z"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                lineNumber: 150,
                                                columnNumber: 110
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                            lineNumber: 150,
                                            columnNumber: 15
                                        }, this),
                                        basePrice ? formatJMD(basePrice) : '-'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                    lineNumber: 149,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                            lineNumber: 147,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                        lineNumber: 146,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                lineNumber: 103,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full px-0 md:px-2 py-2",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-3xl shadow-2xl p-10 w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-2xl font-extrabold mb-6 text-[#211F54] tracking-tight",
                            children: "Pricing Rules"
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                            lineNumber: 159,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "overflow-x-auto",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                className: "w-full text-lg rounded-2xl overflow-hidden",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            className: "bg-[#F5F6FA] text-[#211F54]",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: " text-sm p-4 text-left font-bold",
                                                    children: "Name"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 164,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: " text-sm p-4 text-left font-bold",
                                                    children: "Price"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 165,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: " text-sm p-4 text-left font-bold",
                                                    children: "Priority"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 166,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: " text-sm p-4 text-left font-bold",
                                                    children: "Start Date"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 167,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: " text-sm p-4 text-left font-bold",
                                                    children: "End Date"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 168,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: " text-sm p-4 text-left font-bold",
                                                    children: "Color"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 169,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: " text-sm p-4 text-left font-bold",
                                                    children: "Status"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 170,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: " text-sm p-4 text-left font-bold",
                                                    children: "Last Updated"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 171,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                            lineNumber: 163,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 162,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                        children: nonBaseRules.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                colSpan: 8,
                                                className: "text-center p-8 text-gray-400 text-xl",
                                                children: "No pricing rules"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                lineNumber: 176,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                            lineNumber: 176,
                                            columnNumber: 19
                                        }, this) : nonBaseRules.map((rule, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                className: idx % 2 === 0 ? "bg-white" : "bg-[#F9FAFB] hover:bg-[#F5F6FA]",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: " text-sm p-4 font-semibold text-[#211F54]",
                                                        children: rule.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 179,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: " text-sm p-4 font-semibold",
                                                        children: formatJMD(rule.price)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 180,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: " text-sm p-4",
                                                        children: rule.priority
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 181,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: " text-sm p-4",
                                                        children: formatDate(rule.startDate)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 182,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: " text-sm p-4",
                                                        children: formatDate(rule.endDate)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 183,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: " text-sm p-4",
                                                        children: rule.color && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "inline-flex items-center text-sm gap-2",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    background: rule.color,
                                                                    width: 22,
                                                                    height: 22,
                                                                    borderRadius: '50%',
                                                                    border: '2px solid #e5e7eb',
                                                                    display: 'inline-block'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                                lineNumber: 187,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                            lineNumber: 186,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 184,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "p-4",
                                                        children: rule.isActive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "bg-green-100  text-green-700 px-3 py-1 rounded-full text-sm font-bold",
                                                            children: "Active"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                            lineNumber: 193,
                                                            columnNumber: 25
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "bg-gray-200 text-gray-500 px-3 py-1 rounded-full text-sm font-bold",
                                                            children: "Inactive"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                            lineNumber: 195,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 191,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "p-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-mono text-sm text-gray-500",
                                                                children: formatDateTime(rule.updatedAt)
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                                lineNumber: 199,
                                                                columnNumber: 23
                                                            }, this),
                                                            rule._id === mostRecentRuleId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "ml-3 px-3 py-1 bg-green-100 text-green-700 rounded text-sm font-bold",
                                                                children: "Recent"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                                lineNumber: 201,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 198,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, rule._id, true, {
                                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                lineNumber: 178,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 174,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                lineNumber: 161,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                            lineNumber: 160,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                    lineNumber: 158,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                lineNumber: 157,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full px-0 md:px-2 py-2",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-3xl shadow-2xl p-10 w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-2xl font-extrabold mb-6 text-[#211F54] tracking-tight",
                            children: "Override Rules"
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                            lineNumber: 214,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "overflow-x-auto",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                className: "w-full text-lg rounded-2xl overflow-hidden",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            className: "bg-[#F5F6FA] text-[#211F54]",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "text-base  p-4 text-left font-bold",
                                                    children: "Date"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 219,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "text-base  p-4 text-left font-bold",
                                                    children: "Price"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                    lineNumber: 220,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                            lineNumber: 218,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 217,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                        children: Object.entries(dateOverrides).length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                colSpan: 2,
                                                className: "text-center p-8 text-gray-400 text-xl",
                                                children: "No override rules"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                lineNumber: 225,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                            lineNumber: 225,
                                            columnNumber: 19
                                        }, this) : Object.entries(dateOverrides).map(([date, override], idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                className: idx % 2 === 0 ? "bg-white" : "bg-[#F9FAFB] hover:bg-[#F5F6FA]",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "text-sm  p-4 text-[#211F54]",
                                                        children: date
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 228,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "text-sm  p-4",
                                                        children: formatJMD(override.price)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                        lineNumber: 229,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, date, true, {
                                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                                lineNumber: 227,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                        lineNumber: 223,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                lineNumber: 216,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                            lineNumber: 215,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                    lineNumber: 213,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                lineNumber: 212,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full  px-0 md:px-2 py-2",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-3xl shadow-2xl p-10 w-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-4 mb-6 sticky top-0 z-10 bg-white rounded-t-3xl",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "font-[poppins] w-[350px] font-bold text-2xl bg-white px-6 py-3 rounded shadow-sm border border-gray-200",
                                    children: new Date(calendarYear, calendarMonth).toLocaleString('default', {
                                        month: 'long',
                                        year: 'numeric'
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                    lineNumber: 241,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "px-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm text-2xl text-[#211F54]",
                                    onClick: ()=>setCalendarMonth((prev)=>prev === 0 ? 0 : prev - 1),
                                    children: '<'
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                    lineNumber: 244,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "px-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm text-2xl text-[#211F54]",
                                    onClick: ()=>setCalendarMonth((prev)=>prev === 11 ? 11 : prev + 1),
                                    children: '>'
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                                    lineNumber: 245,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                            lineNumber: 240,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$CalendarRuleView$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            rules: rules.filter((rule)=>rule.name?.toLowerCase() !== 'base price'),
                            basePrice: basePrice,
                            baseCurrency: 'JMD',
                            dateOverrides: dateOverrides,
                            onCellClick: ()=>{},
                            calendarMonth: calendarMonth,
                            calendarYear: calendarYear,
                            setCalendarMonth: setCalendarMonth,
                            setCalendarYear: setCalendarYear,
                            getRuleForDate: getRuleForDateWithUpdatedAt
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                            lineNumber: 247,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                    lineNumber: 239,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
                lineNumber: 238,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/ad-management/(tabs)/current-ad-plan/view/[plan.id]/page.js",
        lineNumber: 101,
        columnNumber: 5
    }, this);
}
_s(ViewAdPlanPage, "ZlYv129FpaX6TTubgLQ/S8yLjJ0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"]
    ];
});
_c = ViewAdPlanPage;
var _c;
__turbopack_context__.k.register(_c, "ViewAdPlanPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=app_c21ffc73._.js.map