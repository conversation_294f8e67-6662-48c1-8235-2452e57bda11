(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/components/landingPage/community/community.module.scss.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "community-module-scss-module__J_eKEq__active",
  "askQuestion": "community-module-scss-module__J_eKEq__askQuestion",
  "card": "community-module-scss-module__J_eKEq__card",
  "communityContainer": "community-module-scss-module__J_eKEq__communityContainer",
  "hide-scrollbar": "community-module-scss-module__J_eKEq__hide-scrollbar",
  "hr": "community-module-scss-module__J_eKEq__hr",
  "response": "community-module-scss-module__J_eKEq__response",
  "searchBox": "community-module-scss-module__J_eKEq__searchBox",
  "share": "community-module-scss-module__J_eKEq__share",
});
}}),
"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    if (dataStatus === 401 || responseStatus === 401 || requestStatus === 401) {
        // Clear local storage and redirect to /login
        localStorage.clear();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
        window.location.href = "/login";
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        // console.log("error log is", error)
        if (Array.isArray(error?.response?.data?.errors) || Array?.isArray(error?.data?.errors)) error?.response?.data?.errors?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.message)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "https://devapi.rebookit.club");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/community.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addQuestion": (()=>addQuestion),
    "allAnswerOfQuestion": (()=>allAnswerOfQuestion),
    "allQuestion": (()=>allQuestion),
    "deleteQuestion": (()=>deleteQuestion),
    "editAnswer": (()=>editAnswer),
    "myAnswers": (()=>myAnswers),
    "myquestion": (()=>myquestion),
    "submitAnswer": (()=>submitAnswer),
    "usersQuestionAnswer": (()=>usersQuestionAnswer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/axios.js [app-client] (ecmascript)");
;
;
let uri = {
    allQuestion: "/community/questions",
    addQuestion: "/community/question",
    userQuestions: "/community/user-questions-with-answer",
    submitAnswer: "/community/answer",
    myquestions: "community/user-questions",
    userAnswers: "/community/user-answers"
};
const allQuestion = async (query)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.allQuestion}${query ? "?" + query : ""}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("allQuestion response", response);
    return response;
};
const usersQuestionAnswer = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.userQuestions}?${payload}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("allQuestion response", response);
    return response;
};
const addQuestion = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.addQuestion}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("addQuestion response", response);
    return response;
};
const allAnswerOfQuestion = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.addQuestion}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("allQuestion response", response);
    return response;
};
const submitAnswer = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.submitAnswer}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("addQuestion response", response);
    return response;
};
const myquestion = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.myquestions}?${payload}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("allQuestion response", response);
    return response;
};
const myAnswers = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.userAnswers}?${payload}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("allQuestion response", response);
    return response;
};
const deleteQuestion = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${uri.addQuestion}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const editAnswer = async (id, payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.submitAnswer}?answerId=${id}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/services/profile.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addReviewForSeller": (()=>addReviewForSeller),
    "bookMarkItem": (()=>bookMarkItem),
    "boostItem": (()=>boostItem),
    "deleteChatById": (()=>deleteChatById),
    "deleteMyBooks": (()=>deleteMyBooks),
    "delete_bookMarkItem": (()=>delete_bookMarkItem),
    "editItem": (()=>editItem),
    "getAdPlanById": (()=>getAdPlanById),
    "getAdPlans": (()=>getAdPlans),
    "getAllChat": (()=>getAllChat),
    "getBooksById": (()=>getBooksById),
    "getCategories": (()=>getCategories),
    "getChatById": (()=>getChatById),
    "getFaq": (()=>getFaq),
    "getItemBySeller": (()=>getItemBySeller),
    "getMyBooks": (()=>getMyBooks),
    "getPaymentIntent": (()=>getPaymentIntent),
    "getReviewsOfUser": (()=>getReviewsOfUser),
    "getSubCategories": (()=>getSubCategories),
    "getSubSubCategories": (()=>getSubSubCategories),
    "getSubSubSubCategories": (()=>getSubSubSubCategories),
    "getTestimonials": (()=>getTestimonials),
    "get_bookMarkItems": (()=>get_bookMarkItems),
    "getsubscriptionPlans": (()=>getsubscriptionPlans),
    "listItem": (()=>listItem),
    "paymentHistory": (()=>paymentHistory),
    "searchByName": (()=>searchByName),
    "searchISBN": (()=>searchISBN),
    "searchItemByName": (()=>searchItemByName),
    "supportRequest": (()=>supportRequest),
    "uploadPhotoSingle": (()=>uploadPhotoSingle),
    "verifyUserData": (()=>verifyUserData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$config$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/config/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/axios.js [app-client] (ecmascript)");
;
;
;
const uri = {
    login: "/user/login",
    userInfo: "/user",
    editProfile: "/user/edit-profile",
    item_by_name: `item/search`,
    subscriptionPlan: "/admin/subscription/plan",
    fetch_category: "/master/category",
    fetchSubCategory: "master/sub-category",
    fetchSubSubCategory: "master/Sub-Sub-category",
    fetchSubSubSubCategory: "master/sub-sub-sub-category",
    getPaymentIntent: "/payment/payment-intent",
    verifyUserData: "user/verify-otp",
    searchISBN: "/books/isbn/{{ISBN}}",
    searchByName: "/books/search?q={search}",
    bookMarkItem_id: "/item/bookmark",
    get_bookMark_by_user: "/item/bookmarks",
    getItems: "/item/search/current-user",
    getItemById: "/item",
    createItem: "/item",
    editItem: "/item",
    deleteItemById: "/item",
    itemBySeller: "/item/user",
    addReview: "/item/addReview",
    userReviews: "/item/{:id}/reviews",
    uploadPhoto: "admin/single-upload",
    history: "/payment/history",
    supportRequest: "/user/support-request",
    boostItem: "/item/boost",
    getTestimonials: "/user/testimonials",
    getFaq: "/faqs/filter-search",
    // ad-management
    getAdPlans: "ad-management/getAdPlans",
    getAdPlanById: "ad-management/getAdPlanById"
};
const chat = {
    chat: "/chat/all",
    chatById: "/chat"
};
const listItem = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.createItem}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const getAdPlans = async ({ type, position, page = 1, limit = 10 } = {})=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlans}`, {
        params: {
            ...type && {
                type
            },
            ...position && {
                position
            },
            page,
            limit
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getAdPlanById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getAdPlanById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const editItem = async (payload, id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.editItem}/${id}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("login test response", response)
    return response;
};
const addReviewForSeller = async (payload)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.addReview}`, payload).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getMyBooks = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getItems}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const deleteMyBooks = async (id, data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.deleteItemById}/${id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getBooksById = async (id, userId)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getItemById}/${id}`, {
        params: {
            userId
        }
    }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getItemBySeller = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.itemBySeller}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const searchItemByName = async (data, queryString)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.item_by_name}` + queryString, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getsubscriptionPlans = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.subscriptionPlan}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetch_category}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("login test response", response);
    return response;
};
const getSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getSubSubSubCategories = async (text)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.fetchSubSubSubCategory}/${text}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getAllChat = async (payloadData)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${chat.chat}`, payloadData).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const deleteChatById = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${chat.chatById}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const bookMarkItem = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.bookMarkItem_id}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    // console.log("getSubCategories response", response)
    return response;
};
const getReviewsOfUser = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.userReviews}`.replace("{:id}", id));
    // .catch(axiosErrorHandler);
    console.log("getSubCategories response", response);
    return response;
};
const delete_bookMarkItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(`${uri.bookMarkItem_id}/${id}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const get_bookMarkItems = async ()=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.get_bookMark_by_user}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const getPaymentIntent = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getPaymentIntent}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("getSubCategories response", response);
    return response;
};
const verifyUserData = async (body)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.verifyUserData}`, body).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchISBN = async (ISBN)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchISBN}`.replace("{{ISBN}}", ISBN)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const searchByName = async (search)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.searchByName}`.replace("{search}", search)).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const uploadPhotoSingle = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.uploadPhoto}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const paymentHistory = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.history}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const supportRequest = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.supportRequest}`, data).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    console.log("verifyUserData response", response);
    return response;
};
const boostItem = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(`${uri.boostItem}/` + id).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getTestimonials = async (id)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(`${uri.getTestimonials}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
};
const getFaq = async (data)=>{
    let response = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(`${uri.getFaq}`).catch(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$axiosError$2e$handler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["axiosErrorHandler"]);
    return response;
} // let data = await fetch(USER_ROUTES.SEARCH_ITEM_BY_NAME.replace("itemName", text), {
 //                 headers: {
 //                     "Content-Type": "application/json",
 //                     "Authorization": `Bearer ${userToken}`
 //                 },
 //             },)
 //             let response = await data.json()
 //             // console.log("data getAllBookOfUser", await data.json())
 //             if (response.data) {
 //                 setBookData(response.data)
 //             }
 // export const login = async (payload, guestId) => {
 //     let response = await instance
 //         .post(`${uri.login}`,payload)
 //         .catch(axiosErrorHandler);
 //         console.log("login test response",response)
 //         return response
 // };
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/ShareCard.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ShareCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/io5/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$rx$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/rx/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
function ShareCard({ url }) {
    // allresponses?id=684bad555ed120f70572e24c
    let urlLocation = `${window.location.origin}/${url}`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        id: "myShareModal",
        className: " fixed inset-0 flex items-center justify-center  bg-opacity-10 z-50  hidden bg-[#000000bf] px-[20px] ",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white flex flex-col  h-[400px] rounded-lg w-full max-w-lg shadow-lg relative rounded-[20px]",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex w-[50px] cursor-pointer mx-3 flex items-center h-[50px] justify-center absolute top-[-10px] right-[-20px] rounded-full bg-[#fefefe] ",
                    onClick: ()=>{
                        let docElement = document.getElementById('myShareModal').classList.add("hidden");
                        console.log("docElement", docElement);
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$rx$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RxCross1"], {
                        className: "text-[#000000]"
                    }, void 0, false, {
                        fileName: "[project]/app/components/common/ShareCard.js",
                        lineNumber: 22,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/common/ShareCard.js",
                    lineNumber: 17,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-[220px] flex items-center justify-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                        className: "w-[50px]",
                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/49e5dbbe-fb83-49e2-8475-9ff3a11971c3.png"
                    }, void 0, false, {
                        fileName: "[project]/app/components/common/ShareCard.js",
                        lineNumber: 25,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/common/ShareCard.js",
                    lineNumber: 24,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-[220px] w-full  global_linear_gradient rounded-t-[10px] rounded-b-[20px]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-center mt-4",
                            onClick: ()=>{
                                navigator.clipboard.writeText(urlLocation);
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("URL copied");
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: " w-[50px] h-[50px] flex items-center p-3 bg-white rounded-full cursor-pointer",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoCopy"], {
                                        size: 25,
                                        className: "text-[#211F54]"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 33,
                                        columnNumber: 108
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 33,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-white ml-4 flex items-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-[20px]",
                                        children: "Copy Link"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 36,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 35,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/common/ShareCard.js",
                            lineNumber: 29,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center text-white mt-2",
                            children: urlLocation
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/ShareCard.js",
                            lineNumber: 42,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-5  justify-center mt-[25px] pb-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.facebook.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/4cea7427-6894-463a-bff6-34aa09bc1f06.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 46,
                                        columnNumber: 154
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 46,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.instagram.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/47f5618b-0452-4f67-b2db-6c5fec6827a1.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 47,
                                        columnNumber: 155
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 47,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.whatsapp.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/18681b6c-c4e2-4072-9a82-be38416d1d17.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 48,
                                        columnNumber: 154
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 48,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.x.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/8652b904-0d14-4d3e-a913-eb999d96cf06.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 49,
                                        columnNumber: 147
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 49,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer",
                                    onClick: ()=>window.open("https://www.pintrest.com", "_blank"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/76225cab-d847-4a93-a24d-0ff3b1bdff6d.png"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/ShareCard.js",
                                        lineNumber: 50,
                                        columnNumber: 154
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/ShareCard.js",
                                    lineNumber: 50,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/common/ShareCard.js",
                            lineNumber: 45,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/common/ShareCard.js",
                    lineNumber: 28,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/common/ShareCard.js",
            lineNumber: 14,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/common/ShareCard.js",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = ShareCard;
var _c;
__turbopack_context__.k.register(_c, "ShareCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/SubmitButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>SubmitButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
function SubmitButton({ isLoading, InnerDiv, type, btnAction }) {
    _s();
    const [isDisabled, setIsDisabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleClick = (e)=>{
        if (isDisabled || isLoading) return;
        setIsDisabled(true);
        btnAction?.(e); // safely call btnAction if defined
        // Re-enable after 1 second (adjust if needed)
        setTimeout(()=>setIsDisabled(false), 2000);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        type: type,
        onClick: handleClick,
        disabled: isLoading,
        //   className="mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5"
        className: `mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5 ${isDisabled || isLoading ? "opacity-50 cursor-not-allowed" : ""}`,
        children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-4 h-4 animate-spin text-white",
                    fill: "none",
                    viewBox: "0 0 24 24",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                            className: "opacity-25",
                            cx: "12",
                            cy: "12",
                            r: "10",
                            stroke: "currentColor",
                            strokeWidth: "4"
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/SubmitButton.js",
                            lineNumber: 33,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            className: "opacity-75",
                            fill: "currentColor",
                            d: "M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/SubmitButton.js",
                            lineNumber: 41,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/common/SubmitButton.js",
                    lineNumber: 28,
                    columnNumber: 11
                }, this),
                "Loading..."
            ]
        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(InnerDiv, {}, void 0, false, {
            fileName: "[project]/app/components/common/SubmitButton.js",
            lineNumber: 50,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/common/SubmitButton.js",
        lineNumber: 17,
        columnNumber: 5
    }, this);
}
_s(SubmitButton, "Sb9Ajwj8Ob/sw24rXx2/9Lml4vg=");
_c = SubmitButton;
var _c;
__turbopack_context__.k.register(_c, "SubmitButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/InitialAvatar/CreateInitialAvatar.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createInitialsAvatar": (()=>createInitialsAvatar)
});
function createInitialsAvatar(name, options = {}) {
    // Default options
    const { size = 100, bgColor = "#cccccc", textColor = "#000000", shape = "circle" } = options;
    // Get initials from name
    const getInitials = (name)=>{
        if (!name || typeof name !== "string") return "";
        const words = name.trim().split(/\s+/).filter((word)=>word.length > 0);
        if (words.length === 0) return "";
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        return `${words[0].charAt(0)}${words[words.length - 1].charAt(0)}`.toUpperCase();
    };
    const initials = getInitials(name);
    const fontSize = size * 0.4;
    // Create SVG based on shape
    let svgContent;
    if (shape === "circle") {
        const radius = size / 2;
        svgContent = `
      <circle cx="${radius}" cy="${radius}" r="${radius}" fill="${bgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
    } else {
        svgContent = `
      <rect width="100%" height="100%" fill="${bgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
    }
    // Create full SVG
    const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" 
         width="${size}" 
         height="${size}" 
         viewBox="0 0 ${size} ${size}">
      ${svgContent}
    </svg>
  `;
    // Convert to data URL
    return `data:image/svg+xml,${encodeURIComponent(svg)}`;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/common/AskQuestion.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AskQuestion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$community$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/community.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/profile.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$SubmitButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/SubmitButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/InitialAvatar/CreateInitialAvatar.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
function AskQuestion({ modelForAnswer, setmodelForAnswer, userAnswerForEdit, functionCallAfterSubmit }) {
    _s();
    const [addQuestionInput, setaddQuestionInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [categoryState, setcategoryState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedCategory, setselectedCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [isLoading, setisLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    console.log("userAnswerForEdit in model", userAnswerForEdit);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    let userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
    console.log("selectedCategory", selectedCategory);
    console.log("addQuestionInput in model", addQuestionInput);
    console.log("functionCallAfterSubmit", functionCallAfterSubmit);
    const fetchMasterCategory = async ()=>{
        try {
            let getCateogriesData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategories"])();
            console.log("getCateogriesData", getCateogriesData);
            if (getCateogriesData?.status == 200) {
                if (getCateogriesData?.data?.categories) {
                    let state = [
                        {
                            name: "Other",
                            _id: ""
                        },
                        ...getCateogriesData?.data?.categories
                    ];
                    setcategoryState(state);
                }
            }
        // setisLoading(false)
        } catch (err) {
            // setisLoading(false)
            console.log("Err fetchMasterCategory", err);
        }
    };
    // useEffect(() => {
    //   if(userAnswerForEdit){
    //     setaddQuestionInput(userAnswerForEdit)
    //   }
    //   return()=>{
    //     setaddQuestionInput("")
    //   }
    // }, [userAnswerForEdit])
    const submitQuestion = async ()=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
            // RedirectToLoginIfNot("/",router)
            router.push("/login");
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RedirectToLoginIfNot"])("/community", router);
            return;
        }
        setisLoading(true);
        if (modelForAnswer) {} else {
            let payload = {
                title: addQuestionInput
            };
            if (selectedCategory) {
                payload.categoryId = selectedCategory;
            }
            let submitResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$community$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addQuestion"])(payload);
            if (submitResponse.status == 200) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Question Added Successfully");
                let docElement = document.getElementById('myModal').classList.add("hidden");
                setaddQuestionInput("");
                setselectedCategory("");
                functionCallAfterSubmit && functionCallAfterSubmit();
            // setisLoading(false)
            }
        }
        setisLoading(false);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AskQuestion.useEffect": ()=>{
            fetchMasterCategory();
            return ({
                "AskQuestion.useEffect": ()=>{
                    if (modelForAnswer) {
                        setmodelForAnswer(false);
                    }
                }
            })["AskQuestion.useEffect"];
        }
    }["AskQuestion.useEffect"], []);
    const InnerDiv = ()=>{
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            onClick: ()=>{},
            children: "Submit"
        }, void 0, false, {
            fileName: "[project]/app/components/common/AskQuestion.js",
            lineNumber: 87,
            columnNumber: 16
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            id: "myModal",
            className: " text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-[#fcfcfc]  rounded-lg w-full max-w-lg shadow-lg relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex w-[30px] cursor-pointer  shadow shadow-lg h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full bg-[white] ",
                        onClick: ()=>{
                            let docElement = document.getElementById('myModal').classList.add("hidden");
                            console.log("docElement", docElement);
                            setaddQuestionInput("");
                            setmodelForAnswer(false);
                            setisLoading(false);
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: "text-gray-500 hover:text-red-600 text-xl font-bold",
                            children: "×"
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/AskQuestion.js",
                            lineNumber: 104,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/common/AskQuestion.js",
                        lineNumber: 97,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "py-3 bg-white rounded-lg ",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold mb-4  border-b w-fit mx-auto",
                            children: modelForAnswer ? "Answer" : "Add Question"
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/AskQuestion.js",
                            lineNumber: 107,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/common/AskQuestion.js",
                        lineNumber: 106,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex mx-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                    className: "w-[20px] h-[20px] rounded-full",
                                    src: userData?.profileImage || (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$InitialAvatar$2f$CreateInitialAvatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createInitialsAvatar"])(`${userData.firstName}  ${userData?.lastName}`, {
                                        bgColor: "#3f51b5",
                                        textColor: "#ffffff"
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/components/common/AskQuestion.js",
                                    lineNumber: 111,
                                    columnNumber: 30
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/components/common/AskQuestion.js",
                                lineNumber: 111,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "ml-3",
                                children: modelForAnswer ? "Reply To Question" : "Ask  Question"
                            }, void 0, false, {
                                fileName: "[project]/app/components/common/AskQuestion.js",
                                lineNumber: 115,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/common/AskQuestion.js",
                        lineNumber: 110,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        class: " mx-4 mt-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                rows: 8,
                                maxLength: 250,
                                value: addQuestionInput,
                                onChange: (e)=>setaddQuestionInput(e.target.value),
                                placeholder: "Type here...",
                                className: "w-full bg-[#F4F4F4] rounded-lg p-2  outline-none"
                            }, void 0, false, {
                                fileName: "[project]/app/components/common/AskQuestion.js",
                                lineNumber: 118,
                                columnNumber: 25
                            }, this),
                            !modelForAnswer && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-bold",
                                children: "Select Category"
                            }, void 0, false, {
                                fileName: "[project]/app/components/common/AskQuestion.js",
                                lineNumber: 119,
                                columnNumber: 45
                            }, this),
                            !modelForAnswer && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                className: "w-full p-3 border rounded-lg border-[#EAEAEA] outline-none",
                                value: selectedCategory,
                                onChange: (e)=>setselectedCategory(e.target.value),
                                children: categoryState.map((item)=>{
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: item._id,
                                        children: item.name
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/common/AskQuestion.js",
                                        lineNumber: 122,
                                        columnNumber: 40
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "[project]/app/components/common/AskQuestion.js",
                                lineNumber: 120,
                                columnNumber: 45
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/common/AskQuestion.js",
                        lineNumber: 117,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        class: "my-2 flex justify-start mx-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-[300px] mx-auto",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$SubmitButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                isLoading: isLoading,
                                InnerDiv: InnerDiv,
                                type: "button",
                                btnAction: submitQuestion
                            }, void 0, false, {
                                fileName: "[project]/app/components/common/AskQuestion.js",
                                lineNumber: 137,
                                columnNumber: 29
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/components/common/AskQuestion.js",
                            lineNumber: 136,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/common/AskQuestion.js",
                        lineNumber: 129,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/common/AskQuestion.js",
                lineNumber: 94,
                columnNumber: 17
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/components/common/AskQuestion.js",
            lineNumber: 93,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/common/AskQuestion.js",
        lineNumber: 92,
        columnNumber: 9
    }, this);
}
_s(AskQuestion, "HfClCXlF4iGXtbBEl6mFrW53gLU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AskQuestion;
var _c;
__turbopack_context__.k.register(_c, "AskQuestion");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/landingPage/community/community.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Community)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/moment/moment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$landingPage$2f$community$2f$community$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/components/landingPage/community/community.module.scss.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi2$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/hi2/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/pi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$community$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/community.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/services/profile.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$ShareCard$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/ShareCard.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$AskQuestion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/common/AskQuestion.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
function Community() {
    _s();
    const [question, setQuestion] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [searchQuestiontext, setsearchQuestiontext] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [addQuestionInput, setaddQuestionInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [categoryState, setcategoryState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedCategory, setselectedCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [selectedId, setselectedId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const dummyQuestionsData = [
        {
            question: "What is Rebookit?",
            answer: "The ultimate online community for connecting local sellers with buyers for the sustainable disposal of used textbooks at all academic levels.  From primary to post-graduate school....",
            postedBy: "Kashish Akanksha",
            createdAt: "2025-04-14T17:47:26.064+00:00",
            responses: 0
        },
        {
            question: "What is Rebookit?",
            answer: "The ultimate online community for connecting local sellers with buyers for the sustainable disposal of used textbooks at all academic levels.  From primary to post-graduate school....",
            postedBy: "Kashish Akanksha",
            createdAt: "2025-04-14T17:47:26.064+00:00",
            responses: 0
        },
        {
            question: "What is Rebookit?",
            answer: "The ultimate online community for connecting local sellers with buyers for the sustainable disposal of used textbooks at all academic levels.  From primary to post-graduate school....",
            postedBy: "Kashish Akanksha",
            createdAt: "2025-04-14T17:47:26.064+00:00",
            responses: 0
        }
    ];
    const bookCategories = [
        "School Books",
        "PEP",
        "CSEC",
        "CAPE",
        "Other"
    ];
    // Set initial activeCategory to "all"
    const [activeCategory, setActiveCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("all");
    const [communityQuestions, setCommunityQuestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Community.useEffect": ()=>{
            setCommunityQuestions(dummyQuestionsData);
            getQuestionFunc();
            fetchMasterCategory();
        }
    }["Community.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Community.useEffect": ()=>{
            // If "all" is selected, don't pass categoryId
            if (activeCategory === "all") {
                getQuestionFunc("");
            } else {
                getQuestionFunc("", activeCategory);
            }
        }
    }["Community.useEffect"], [
        activeCategory
    ]);
    const getQuestionFunc = async (searchText, categoryId)=>{
        let searchQuery = "";
        if (searchText) {
            searchQuery += `searchTerm=${searchText}`;
        }
        // Only add categoryId if not "all" and not empty
        if (categoryId && categoryId !== "all") {
            searchQuery += `categoryId=${categoryId}`;
        }
        let questions = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$community$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["allQuestion"])(searchQuery);
        if (questions.status == 200) {
            if (questions.data.data.length > 4) {
                setQuestion(questions.data.data.slice(0, 4));
            } else {
                setQuestion(questions.data.data);
            }
        }
    };
    let categories = [
        {
            label: "School Books",
            values: "School Books"
        },
        {
            label: "PEP",
            values: "PEP"
        },
        {
            label: "CSEC",
            values: "CSEC"
        },
        {
            label: "CAPE",
            values: "CAPE"
        },
        {
            label: "Others",
            values: "Others"
        }
    ];
    const submitQuestion = async ()=>{
        let submitResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$community$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addQuestion"])({
            title: addQuestionInput,
            categoryId: selectedCategory
        });
        if (submitResponse.status == 200) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Question Added Successfully");
            let docElement = document.getElementById("myModal").classList.add("hidden");
        }
    };
    const fetchMasterCategory = async ()=>{
        try {
            let getCateogriesData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$services$2f$profile$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategories"])();
            // Add "All" item at the start of the categories
            if (getCateogriesData?.status == 200) {
                if (getCateogriesData?.data?.categories) {
                    let state = [
                        {
                            name: "Other",
                            _id: "all"
                        },
                        ...getCateogriesData?.data?.categories
                    ];
                    setcategoryState(state);
                }
            }
        // setisLoading(false)
        } catch (err) {
            // setisLoading(false)
            console.log("Err fetchMasterCategory", err);
        }
    };
    // Debug logs
    // console.log("categoryState", categoryState);
    // console.log("selectedCategory", selectedCategory);
    // console.log("selectedId", selectedId);
    // console.log("activeCategory", activeCategory);
    // console.log("question landing", question);
    let userData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userDataFromLocal"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$landingPage$2f$community$2f$community$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].communityContainer} `,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "container-wrapper px-[10px] px-4 py-10 sm:px-6 md:px-12 lg:px-20 xl:px-18",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                className: "md:flex md:justify-between md:items-start  ",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                    className: "md:w-8/12",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-[22px] font-semibold leading-normal uppercase md:text-[48px]",
                                            children: "Ask The Community"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                            lineNumber: 148,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "mt-2.5 font-light text-[12px] md:text-[18px] leading-[18px] md:leading-[27px]",
                                            children: "About textbook printing, use, and disposal, along with reasons why reselling and buying used textbooks helps promote the green agenda:"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                            lineNumber: 151,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                    lineNumber: 147,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/components/landingPage/community/community.js",
                                lineNumber: 146,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                className: "md:flex md:justify-between md:items-center my-5 md:my-[30px] ",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$landingPage$2f$community$2f$community$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].searchBox} w-full`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "search",
                                                className: "w-7/12",
                                                onChange: (e)=>setsearchQuestiontext(e.target.value),
                                                placeholder: "Type your query here"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 161,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: (e)=>getQuestionFunc(searchQuestiontext, activeCategory === "all" ? undefined : activeCategory),
                                                children: [
                                                    " ",
                                                    "Search Now"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 167,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/landingPage/community/community.js",
                                        lineNumber: 160,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        className: `hidden ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$landingPage$2f$community$2f$community$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].askQuestion}  `,
                                        onClick: ()=>{
                                            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
                                                // RedirectToLoginIfNot("/",router)
                                                router.replace("/login");
                                                RedirectToLoginIfNot("/community", router);
                                                return;
                                            } else {
                                                let docElement = document.getElementById("myModal").classList.remove("hidden");
                                                console.log("docElement", docElement);
                                            }
                                        },
                                        children: "Ask your question"
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/landingPage/community/community.js",
                                        lineNumber: 180,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/landingPage/community/community.js",
                                lineNumber: 159,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "md:mt-16 flex flex-wrap no-scrollbar overflow-auto   items-center gap-5 mt-6 md:mt-[30px] md:justify-between ",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        scrollbarWidth: "1px"
                                    },
                                    className: " hide-scrollbar flex justify-start     items-center md:gap-0 border-b border-[#b4b4b42e] lg:justify-center lg:gap-8 lg:overflow-none",
                                    children: categoryState.map((category, idx)=>{
                                        const isActive = activeCategory === category._id;
                                        const categoryClass = `text-[11.5px] md:text-[20px] whitespace-nowrap cursor-pointer pt-[17px] pb-[16px] px-[25px] text-center ${isActive ? __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$landingPage$2f$community$2f$community$2e$module$2e$scss$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].active : ""}`;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: categoryClass.trim(),
                                            onClick: ()=>setActiveCategory(category._id),
                                            children: category.name
                                        }, idx, false, {
                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                            lineNumber: 212,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                    lineNumber: 201,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/components/landingPage/community/community.js",
                                lineNumber: 200,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/landingPage/community/community.js",
                        lineNumber: 145,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: "flex flex-col items-center gap-5 mt-6 md:mt-8 ",
                        children: [
                            question.length > 0 ? question?.map((question, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("article", {
                                    className: "w-full bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden flex flex-col md:flex-row",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "md:max-w-[85%] flex-1 p-4 md:p-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mb-4 ",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                    className: "text-lg font-semibold line-clamp-2 text-gray-900 md:text-xl break-words  max-w-[90%]",
                                                                    children: `Q. ${question?.title}?`
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                                    lineNumber: 235,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "mt-2 text-gray-700 text-sm md:text-base line-clamp-2 break-words   max-w-[90%]",
                                                                    children: `A. ${question?.latestAnswer?.answerText || "N/A"}`
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                                    lineNumber: 238,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                            lineNumber: 234,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                                                            className: " text-xs text-gray-500 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0 ",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex flex-col sm:flex-row sm:gap-4",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            children: [
                                                                                "Posted By: ",
                                                                                " ",
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "font-medium",
                                                                                    children: question?.askedBy?.firstName || "Unknown"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                                                    lineNumber: 247,
                                                                                    columnNumber: 27
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                                            lineNumber: 245,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            children: [
                                                                                "Date:",
                                                                                " ",
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "font-medium",
                                                                                    children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$moment$2f$moment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(question?.createdAt).format("DD/MMMM/YYYY")
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                                                    lineNumber: 253,
                                                                                    columnNumber: 27
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                                            lineNumber: 251,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                                    lineNumber: 244,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "sm:self-end",
                                                                    children: [
                                                                        "Responses:",
                                                                        " ",
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "font-medium",
                                                                            children: question?.totalAnswers
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                                            lineNumber: 260,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                                    lineNumber: 258,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                            lineNumber: 243,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                    lineNumber: 233,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex gap-3 mt-4 md:hidden ",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            className: "flex items-center justify-center px-3 py-2 global_linear_gradient text-white rounded-full text-xs sm:text-sm flex-1",
                                                            onClick: ()=>router.push(`/allresponses?id=${question._id}`),
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi2$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HiChatBubbleLeftRight"], {
                                                                    className: "mr-1.5",
                                                                    size: 16
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                                    lineNumber: 274,
                                                                    columnNumber: 23
                                                                }, this),
                                                                "All Responses"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                            lineNumber: 268,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            className: "flex items-center justify-center px-3 py-2 border border-black-600 text-black rounded-full text-xs sm:text-sm flex-1",
                                                            onClick: ()=>{
                                                                document.getElementById("myShareModal").classList.remove("hidden");
                                                                setselectedId(question._id);
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiShareFill"], {
                                                                    size: 16,
                                                                    className: "mr-1.5"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                                    lineNumber: 286,
                                                                    columnNumber: 23
                                                                }, this),
                                                                "Share"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                            lineNumber: 277,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                    lineNumber: 267,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                            lineNumber: 232,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "hidden md:flex flex-col justify-center items-center p-4 md:p-6 gap-3 border-l ",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: "flex items-center px-4 py-2 global_linear_gradient text-white rounded-full text-sm min-w-[160px] justify-center",
                                                    onClick: ()=>router.push(`/allresponses?id=${question._id}`),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$hi2$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HiChatBubbleLeftRight"], {
                                                            className: "mr-2",
                                                            size: 18
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                            lineNumber: 299,
                                                            columnNumber: 21
                                                        }, this),
                                                        "All Responses"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                    lineNumber: 293,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: "flex items-center px-4 py-2 border border-black text-black rounded-full text-sm min-w-[160px] justify-center",
                                                    onClick: ()=>{
                                                        document.getElementById("myShareModal").classList.remove("hidden");
                                                        setselectedId(question._id);
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PiShareFill"], {
                                                            size: 18,
                                                            className: "mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                                            lineNumber: 311,
                                                            columnNumber: 21
                                                        }, this),
                                                        "Share"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                                    lineNumber: 302,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/landingPage/community/community.js",
                                            lineNumber: 292,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, idx, true, {
                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                    lineNumber: 228,
                                    columnNumber: 15
                                }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full py-10 text-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-500 text-lg",
                                    children: "No Questions Found"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                    lineNumber: 319,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/components/landingPage/community/community.js",
                                lineNumber: 318,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "hidden md:flex justify-center my-8",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/community",
                                    className: "inline-block transition-transform hover:scale-105",
                                    "aria-label": "View all questions",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        width: "178",
                                        height: "72",
                                        viewBox: "0 0 178 72",
                                        fill: "none",
                                        xmlns: "http://www.w3.org/2000/svg",
                                        className: "w-44 md:w-48 lg:w-52",
                                        children: [
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285",
                                                stroke: "#211F54",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 338,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017",
                                                stroke: "#0161AB",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 343,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285",
                                                stroke: "#EFDC2A",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 348,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285",
                                                stroke: "#0161AB",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 353,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M140.693 6L36.937 5.99999",
                                                stroke: "#FF0009",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 358,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M140.693 65.457L36.937 65.457",
                                                stroke: "#4A8B40",
                                                strokeWidth: "11.679"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 363,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                                                x: "11.6016",
                                                y: "7.93848",
                                                width: "154.01",
                                                height: "54.6036",
                                                rx: "27.3018",
                                                fill: "white"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 368,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("text", {
                                                x: "50%",
                                                y: "50%",
                                                dominantBaseline: "middle",
                                                textAnchor: "middle",
                                                fontSize: "20",
                                                fill: "#211F54",
                                                fontFamily: "Poppins, sans-serif",
                                                children: "View All"
                                            }, void 0, false, {
                                                fileName: "[project]/app/components/landingPage/community/community.js",
                                                lineNumber: 376,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/components/landingPage/community/community.js",
                                        lineNumber: 329,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/landingPage/community/community.js",
                                    lineNumber: 324,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/components/landingPage/community/community.js",
                                lineNumber: 323,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "w-full max-w-md py-3 bg-gradient-to-r bg-[#025fa8] text-white rounded-full font-medium md:hidden mt-4",
                                onClick: ()=>{
                                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getToken"])()) {
                                        router.replace("/login");
                                        RedirectToLoginIfNot("/community", router);
                                        return;
                                    } else {
                                        document.getElementById("myModal").classList.remove("hidden");
                                    }
                                },
                                children: "Ask your Question"
                            }, void 0, false, {
                                fileName: "[project]/app/components/landingPage/community/community.js",
                                lineNumber: 391,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/landingPage/community/community.js",
                        lineNumber: 225,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/landingPage/community/community.js",
                lineNumber: 144,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$ShareCard$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                url: `allresponses?id=${selectedId}`
            }, void 0, false, {
                fileName: "[project]/app/components/landingPage/community/community.js",
                lineNumber: 407,
                columnNumber: 7
            }, this),
            userData && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$common$2f$AskQuestion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/app/components/landingPage/community/community.js",
                lineNumber: 410,
                columnNumber: 20
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/landingPage/community/community.js",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
_s(Community, "8IIQZ1MdF0FzQYCEACljDSt5XrM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = Community;
var _c;
__turbopack_context__.k.register(_c, "Community");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/landingPage/community/community.js [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/components/landingPage/community/community.js [app-client] (ecmascript)"));
}}),
}]);

//# sourceMappingURL=app_990a3016._.js.map