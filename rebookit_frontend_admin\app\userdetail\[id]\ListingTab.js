// "use client";
// import Image from "next/image";
// import React from "react";

// const ListingTab = ({userDetails}) => {
//   return (
//     <div className="overflow-x-auto">
//       <table className="min-w-full divide-y divide-gray-200">
//         <thead className="bg-gray-50">
//           <tr>
//             <th className="w-12 p-3">
//               <input type="checkbox" className="h-4 w-4 text-blue-600" />
//             </th>
//             <th className="p-3 text-left text-sm font-medium text-gray-700">
//               Book
//             </th>
//             <th className="p-3 text-left text-sm font-medium text-gray-700">
//               Location
//             </th>
//             <th className="p-3 text-center text-sm font-medium text-gray-700">
//               Action
//             </th>
//           </tr>
//         </thead>
//         <tbody className="divide-y divide-gray-100">
//           {userDetails?.user?.itemlistsDoc?.map((item, idx) => (
//             <tr key={idx}>
//               <td className="text-center p-3">
//                 <input type="checkbox" className="h-4 w-4 text-blue-600" />
//               </td>
//               <td className="p-3 flex items-center gap-3">
//                 <div className="w-12 h-16 relative">
//                   <Image
//                     src={item?.images[0]}
//                     alt={item?.title}
//                     layout="fill"
//                     objectFit="cover"
//                     className="rounded w-full h-full"
//                   />
//                 </div>
//                 <div>
//                   <p className="font-medium line-clamp-1" title={item?.title}>
//                     {item?.title}
//                   </p>
//                   <p className="text-xs text-gray-500">
//                     {item?.categoryDoc?.name}
//                   </p>
//                 </div>
//               </td>
//               <td className="p-3 text-sm text-gray-600">
//                 <p className="line-clamp-2">
//                   {`${item?.address?.administrative_area_level_1}, ${item?.address?.formatted_address} `}
//                 </p>
//               </td>
//               <td className="p-3 text-right">
//                 <button className="px-4 py-2 bg-gradient-to-r global_linear_gradient text-white rounded-full text-sm">
//                   View Detail
//                 </button>
//               </td>
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     </div>
//   );
// };

// export default ListingTab;

"use client";
import Image from "next/image";
import {useRouter} from "next/navigation";
import React from "react";

const ListingTab = ({userDetails}) => {
  const router = useRouter();
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 table-auto">
        <thead className="bg-gray-50">
          <tr>
            <th className="w-12 p-3">
              <input type="checkbox" className="h-4 w-4 text-blue-600" />
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-700">
              Book
            </th>
            <th className="p-3 text-left text-sm font-medium text-gray-700">
              Location
            </th>
            <th className="p-3 text-center text-sm font-medium text-gray-700">
              Action
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-100">
          {userDetails?.user?.itemlistsDoc?.map((item, idx) => (
            <tr key={idx} className="hover:bg-gray-50 transition-colors">
              <td className="text-center p-3">
                <input type="checkbox" className="h-4 w-4 text-blue-600" />
              </td>
              <td className="p-3 flex items-center gap-3">
                <div className="w-12 h-16 relative flex-shrink-0">
                  <Image
                    src={item?.images?.[0] || "/placeholder.jpg"}
                    alt={item?.title}
                    fill
                    className="rounded object-cover"
                  />
                </div>
                <div className="min-w-0">
                  <p className="font-medium line-clamp-1" title={item?.title}>
                    {item?.title}
                  </p>
                  <p className="text-xs text-gray-500 line-clamp-1">
                    {item?.categoryDoc?.name}
                  </p>
                </div>
              </td>
              <td className="p-3 text-sm text-gray-600">
                <p
                  className="line-clamp-2"
                  title={item?.address?.formatted_address}
                >
                  {`${item?.address?.administrative_area_level_1}, ${item?.address?.formatted_address}`}
                </p>
              </td>
              <td className="p-3 text-right ">
                <button
                  onClick={() => router.push(`/book-detail?id=${item?._id}`)}
                  className=" px-4 py-2 bg-gradient-to-r global_linear_gradient text-white rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  View
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ListingTab;
