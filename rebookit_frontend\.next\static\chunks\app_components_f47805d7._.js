(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/components/common/Slider.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_31dcf66a._.js",
  "static/chunks/app_components_common_Slider_9ca31ec7.js",
  "static/chunks/app_components_common_Slider_4a6d848a.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/app/components/common/Slider.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/app/components/about/BuyAndSellComponent.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/app_components_about_BuyAndSellComponent_7b01df9b.js",
  "static/chunks/app_components_about_BuyAndSellComponent_4a6d848a.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/app/components/about/BuyAndSellComponent.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);